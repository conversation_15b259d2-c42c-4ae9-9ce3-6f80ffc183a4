Section 3 eBook
Organizing Notifications with Custom Channels

Chapter Title
Section Overview

[T] In this section, you'll explore:
[List]
What notification channels are and why they're important
How to create and configure notification channels with different importance levels
How to implement custom sounds for notification channels
How to provide a consistent experience across both Android and iOS platforms
How to let users customize their notification preferences

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!


Next Chapter Title
Understanding Notification Channels

[T] In this chapter, we'll explore the concept of notification channels and how they help users manage notification overload.

[H2] What Are Notification Channels?
[T] Notification channels are a feature introduced in Android 8.0 (API level 26) that help categorize notifications and allow users to control them at a granular level. Each channel represents a category of notifications with its own importance level, sound, vibration pattern, and other settings.

[T] Think of notification channels like TV channels on your television. Just as you might have different channels for news, sports, movies, and documentaries, your app can have different notification channels for various types of alerts. And just as you can adjust the volume or mute specific TV channels based on your preferences, users can customize how each notification channel behaves.

[T] For example, a messaging app might have separate channels for:
[List]
Direct messages
Group messages
Friend requests
System notifications

[T] Users can then customize how each type of notification behaves, such as:
[List]
Allowing some notifications to make sounds while silencing others
Setting different importance levels for different types of notifications
Blocking certain types of notifications entirely while allowing others

[H2] Why Are Notification Channels Important?
[T] Notification channels provide several benefits:

[List]
**User Control**: Users can customize notification behavior at a granular level
**Reduced Notification Fatigue**: Users can silence less important notifications without disabling all notifications
**Better Organization**: Developers can categorize notifications logically
**Compliance**: Required for apps targeting Android 8.0 (API level 26) or higher

[T] While notification channels are an Android-specific feature, we'll implement them in a way that provides a consistent experience across both Android and iOS.

[H2] Planning Our Notification Channels
[T] For our Timora app, we'll create several notification channels:

[List]
**Work**: For work-related reminders and meetings
**Personal**: For personal reminders and events
**Health**: For health and fitness reminders
**Default**: For general notifications

[T] Each channel will have different default settings for importance and sound options, providing a rich and customizable notification experience.

[T] Up Next, we'll implement these notification channels in our app, starting with the basic channel structure.


Next Chapter Title
Implementing Notification Channels

[T] In this chapter, we'll set up the foundation for our notification organization system by implementing channels that categorize different types of alerts.

[H2] Setting Up Channel Constants
[T] First, let's define constants for our notification channels. Open **lib/core/constants/notification_constants.dart** and examine the following:

[Code: dart]
/// Notification channel IDs
class NotificationChannelIds {
  // Standard notification channels
  static const String work = 'work_notifications';
  static const String personal = 'personal_notifications';
  static const String health = 'health_notifications';
  static const String defaultChannel = 'default_notifications';

  // Channels with custom sounds - naming convention: <channel>_sound
  static const String workSound = '${work}_sound';
  static const String personalSound = '${personal}_sound';
  static const String healthSound = '${health}_sound';
}

/// Notification channel display names and descriptions
class NotificationChannelDetails {
  // Standard channel names and descriptions
  static const String workName = 'Work Notifications';
  static const String workDescription =
      'Notifications related to work and productivity';

  static const String personalName = 'Personal Notifications';
  static const String personalDescription =
      'Notifications related to personal matters';

  static const String healthName = 'Health Notifications';
  static const String healthDescription =
      'Notifications related to health and wellness';

  // Custom sound channel names and descriptions - consistent naming pattern
  static const String workSoundName = '$workName with Sound';
  static const String workSoundDescription =
      '$workDescription (with custom sound)';

  static const String personalSoundName = '$personalName with Sound';
  static const String personalSoundDescription =
      '$personalDescription (with custom sound)';

  static const String healthSoundName = '$healthName with Sound';
  static const String healthSoundDescription =
      '$healthDescription (with custom sound)';
}

/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] These constants define the IDs, names, descriptions, and resources for our notification channels. Note how we have pairs of channels - a standard version and a version with custom sound for each category.

[H2] Implementing the _setupNotificationChannels Method
[T] Now, let's implement the _setupNotificationChannels method in the NotificationManager class. Open **lib/service/notification-manager/notification_manager.dart** and update the method:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined channels with appropriate importance levels for
/// different notification categories (work, personal, health).
Future<void> _setupNotificationChannels() async {
  if (!Platform.isAndroid) return;

  final plugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >();
  if (plugin == null) return;

  // Create standard and custom sound channels for each category
  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.work,
    standardName: NotificationChannelDetails.workName,
    standardDesc: NotificationChannelDetails.workDescription,
    soundId: NotificationChannelIds.workSound,
    soundName: NotificationChannelDetails.workSoundName,
    soundDesc: NotificationChannelDetails.workSoundDescription,
    importance: Importance.high,
  );

  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.personal,
    standardName: NotificationChannelDetails.personalName,
    standardDesc: NotificationChannelDetails.personalDescription,
    soundId: NotificationChannelIds.personalSound,
    soundName: NotificationChannelDetails.personalSoundName,
    soundDesc: NotificationChannelDetails.personalSoundDescription,
    importance: Importance.defaultImportance,
  );

  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.health,
    standardName: NotificationChannelDetails.healthName,
    standardDesc: NotificationChannelDetails.healthDescription,
    soundId: NotificationChannelIds.healthSound,
    soundName: NotificationChannelDetails.healthSoundName,
    soundDesc: NotificationChannelDetails.healthSoundDescription,
    importance: Importance.high,
  );
}

[T] This method creates three pairs of notification channels - one for work, one for personal, and one for health notifications. Each pair consists of a standard channel and a channel with custom sound.

[H2] Implementing the _createChannelPair Helper Method
[T] To avoid code duplication, we'll implement a helper method to create pairs of notification channels:

[Code: dart]
/// Helper method to create a pair of notification channels (standard and custom sound)
Future<void> _createChannelPair(
  AndroidFlutterLocalNotificationsPlugin plugin, {
  required String standardId,
  required String standardName,
  required String standardDesc,
  required String soundId,
  required String soundName,
  required String soundDesc,
  required Importance importance,
}) async {
  // Standard channel
  await plugin.createNotificationChannel(
    AndroidNotificationChannel(
      standardId,
      standardName,
      description: standardDesc,
      importance: importance,
    ),
  );

  // Channel with custom sound
  await plugin.createNotificationChannel(
    AndroidNotificationChannel(
      soundId,
      soundName,
      description: soundDesc,
      importance: importance,
      sound: const RawResourceAndroidNotificationSound(
        NotificationResources.customSoundAndroid,
      ),
    ),
  );
}

[T] This helper method creates two notification channels for each category - a standard channel and a channel with custom sound. This approach allows users to choose whether they want to hear a custom sound for each type of notification.

[T] Up Next, we'll explore how to use these channels when creating notifications.


Next Chapter Title
Using Notification Channels in Notifications

[T] In this chapter, we'll apply our notification channels to actual notifications, ensuring users receive alerts with the appropriate priority and sound settings.

[H2] Selecting the Appropriate Channel
[T] When creating a notification, we need to select the appropriate channel based on the notification's category and whether it should play a custom sound. This is similar to how a postal service might route mail through different processing centers based on its priority and destination. Let's implement a helper method to get the effective channel ID:

[Code: dart]
/// Helper method to get the appropriate channel ID based on custom sound flag
String _getEffectiveChannelId(String channelId, bool customSound) {
  if (!Platform.isAndroid || !customSound) {
    return channelId;
  }

  // Map of standard channels to their sound-enabled counterparts
  const channelMap = {
    NotificationChannelIds.work: NotificationChannelIds.workSound,
    NotificationChannelIds.personal: NotificationChannelIds.personalSound,
    NotificationChannelIds.health: NotificationChannelIds.healthSound,
  };

  return channelMap[channelId] ?? channelId;
}

[T] This method takes a channel ID and a custom sound flag, and returns the appropriate channel ID. If the custom sound flag is true, it returns the sound-enabled version of the channel; otherwise, it returns the standard channel.

[H2] Using Channels in Notification Details
[T] Now, let's see how to use these channels when building notification details:

[Code: dart]
/// Builds platform-specific notification details based on the notification model.
///
/// Configures Android and iOS specific settings according to the notification properties.
Future<NotificationDetails> _buildNotificationDetails({
  required String channelId,
  required NotificationLevel level,
  bool customSound = false,
  bool hasActions = false,
  // Other parameters...
}) async {
  // Get the effective channel ID based on custom sound flag
  final effectiveChannelId = _getEffectiveChannelId(channelId, customSound);

  // Configure Android-specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    effectiveChannelId,
    effectiveChannelId.toUpperCase(),
    channelDescription: '$effectiveChannelId notifications',
    importance: _getAndroidImportance(level),
    priority: _getAndroidPriority(level),
    // Other settings...
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: level.playSound,
    sound: customSound ? NotificationResources.customSoundIOS : null,
    // Other settings...
  );

  // Combine the platform-specific details
  return NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

[T] This method builds platform-specific notification details based on the provided parameters. For Android, it uses the effective channel ID to determine which channel to use. For iOS, it sets the appropriate category and sound settings.

[H2] Creating a Notification with a Specific Channel
[T] Finally, let's see how to create a notification with a specific channel:

[Code: dart]
/// Shows a notification with the specified details.
Future<void> showNotification({
  required int id,
  required String title,
  required String body,
  required String channelId,
  NotificationLevel level = NotificationLevel.default_,
  bool customSound = false,
  // Other parameters...
}) async {
  final details = await _buildNotificationDetails(
    channelId: channelId,
    level: level,
    customSound: customSound,
    // Other parameters...
  );

  await _flutterLocalNotificationsPlugin.show(
    id,
    title,
    body,
    details,
    payload: _buildPayload(/* payload parameters */),
  );
}

[T] This method shows a notification with the specified details, using the appropriate channel based on the provided channel ID and custom sound flag.

[T] Up Next, we'll explore how to implement iOS notification categories to provide a consistent experience across platforms.


Next Chapter Title
Implementing iOS Notification Categories

[T] In this chapter, we'll bridge the platform gap by implementing iOS notification categories that provide a consistent experience for users regardless of their device.

[H2] Understanding iOS Notification Categories
[T] While Android uses notification channels, iOS uses notification categories to organize notifications. iOS notification categories serve a similar purpose but work differently:

[List]
**Android Channels**: Focus on notification appearance and behavior
**iOS Categories**: Focus on notification actions and interaction options

[T] To provide a consistent experience, we'll implement iOS notification categories that correspond to our Android notification channels.

[H2] Defining iOS Notification Categories
[T] First, let's define a constant for our interactive notification category:

[Code: dart]
/// Notification categories and interaction types
class NotificationCategories {
  static const String interactive = 'INTERACTIVE_CATEGORY';
}

[T] This constant defines the ID for our interactive notification category, which we'll use for notifications that have actions.

[H2] Implementing iOS Notification Categories
[T] Now, let's implement iOS notification categories in our _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  final snoozeAction = DarwinNotificationAction.plain(
    NotificationActionIds.snooze,
    NotificationActionTexts.snooze,
    options: {DarwinNotificationActionOption.foreground},
  );
  final dismissAction = DarwinNotificationAction.plain(
    NotificationActionIds.dismiss,
    NotificationActionTexts.dismiss,
    options: {DarwinNotificationActionOption.foreground},
  );
  final replyAction = DarwinNotificationAction.text(
    NotificationActionIds.reply,
    NotificationActionTexts.reply,
    buttonTitle: NotificationActionTexts.reply,
  );

  final interactiveCategory = DarwinNotificationCategory(
    NotificationCategories.interactive,
    actions: [snoozeAction, dismissAction, replyAction],
  );

  final iosSettings = DarwinInitializationSettings(
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
    notificationCategories: [interactiveCategory],
  );

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This method configures iOS notification categories with actions for interactive notifications. The interactive category includes actions for snoozing, dismissing, and replying to notifications.

[H2] Using iOS Categories in Notifications
[T] When building notification details, we need to set the appropriate category for iOS notifications:

[Code: dart]
// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  // Other settings...
);

[T] This code sets the category identifier based on whether the notification has actions. If it has actions, it uses the interactive category; otherwise, it uses the channel ID as the category.

[T] Up Next, we'll explore how to organize notifications using the NotificationChannel enum.


Next Chapter Title
Organizing Notifications with the NotificationChannel Enum

[T] In this chapter, we'll create a unified system for notification management using enums, making our code more maintainable while providing visual consistency for users.

[H2] Understanding the NotificationChannel Enum
[T] The NotificationChannel enum provides a centralized way to manage notification channels, their display names, and associated colors. This is similar to how a library uses a classification system like the Dewey Decimal System to organize books - it provides a structured way to categorize and retrieve information:

[Code: dart]
/// Enhanced enum for notification channels with associated colors and display names.
///
/// This enum provides a centralized way to manage notification channels,
/// their display names, and associated colors.
enum NotificationChannel {
  work(
    id: NotificationChannelIds.work,
    displayName: 'Work',
    color: Colors.blue,
    soundId: NotificationChannelIds.workSound,
  ),

  personal(
    id: NotificationChannelIds.personal,
    displayName: 'Personal',
    color: Colors.green,
    soundId: NotificationChannelIds.personalSound,
  ),

  health(
    id: NotificationChannelIds.health,
    displayName: 'Health',
    color: Colors.red,
    soundId: NotificationChannelIds.healthSound,
  ),

  default_(
    id: NotificationChannelIds.defaultChannel,
    displayName: 'Default',
    color: Colors.blueGrey,
    soundId: null,
  );

  /// The channel ID used for notification configuration
  final String id;

  /// The human-readable display name for the channel
  final String displayName;

  /// The color associated with this channel for UI elements
  final Color color;

  /// The ID for the sound variant of this channel (if applicable)
  final String? soundId;

  const NotificationChannel({
    required this.id,
    required this.displayName,
    required this.color,
    this.soundId,
  });

  /// Get a NotificationChannel from its ID
  static NotificationChannel fromId(String id) {
    return NotificationChannel.values.firstWhere(
      (channel) => channel.id == id || channel.soundId == id,
      orElse: () => NotificationChannel.default_,
    );
  }
}

[T] This enum defines four notification channels (work, personal, health, and default) with their associated IDs, display names, colors, and sound IDs. It also provides a method to get a NotificationChannel from its ID.

[H2] Using the NotificationChannel Enum in the UI
[T] The NotificationChannel enum can be used in the UI to provide a consistent visual identity for each type of notification:

[Code: dart]
class CategorySection extends StatelessWidget {
  final CreateNotificationController controller;

  const CategorySection({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final categories = {
      NotificationChannelIds.work: 'Work',
      NotificationChannelIds.personal: 'Personal',
      NotificationChannelIds.health: 'Health',
    };

    return ChipSelector(
      options: categories.keys.toList(),
      selectedOption: controller.value.channelId,
      onSelected: controller.updateChannelId,
      // Custom display function to show the category name instead of the ID
      customLabelBuilder: (option) => categories[option] ?? option,
    );
  }
}

[T] This widget displays a chip selector for notification categories, using the NotificationChannel enum to get the display names for each category.

[T] Up Next, we'll tackle a code challenge to test your understanding of notification channels.


Next Chapter Title
Code Challenge: Creating Custom Sound Notifications

[T] In this chapter, you'll apply your knowledge of notification channels by creating notifications with custom sounds that help users distinguish between different types of alerts.

[H2] The Challenge
[T] Think of notification sounds like ringtones on your phone - they help you know who's calling without looking at the screen. In our app, we want to give users the same ability to distinguish between different types of notifications just by hearing them.

[T] Your challenge is to create a notification system for a productivity app that helps users manage their work-life balance. The app needs to send different types of notifications:

[List]
1. Work notifications that sound professional and urgent
2. Personal notifications that sound friendly but noticeable
3. Health notifications that are gentle reminders

[T] For this challenge:

[List]
1. Create a work notification with a custom sound that would alert the user about an upcoming meeting
2. Make sure the notification has high importance since meetings are time-sensitive
3. Implement the notification using the appropriate channel and custom sound flag

[T] This challenge simulates a real-world scenario where different notification types need different levels of user attention. Take some time to implement this before moving to the solution chapter.

Next Chapter Title
Solution: Creating Custom Sound Notifications

[T] In this chapter, we'll walk through the solution to our custom sound notification challenge, examining how to properly implement different notification types with appropriate sounds.

[H2] Understanding the Solution
[T] Just as a restaurant might use different bell sounds to indicate when food is ready for different tables, our app uses different sounds to indicate different types of notifications. Let's implement our solution:

[Code: dart]
// Create a notification builder for a work notification
final builder = NotificationBuilder.create(
  notificationManager,
  id: 1,
  title: 'Work Meeting',
  body: 'You have a meeting in 15 minutes',
  channelId: NotificationChannelIds.work,
  level: NotificationLevel.high,
);

// Set the custom sound flag to true
builder.setCustomSound(true);

// Show the notification
await builder.show();

[T] This code creates a notification builder for a work notification, sets the custom sound flag to true, and shows the notification. The notification will use the work channel with custom sound, which we defined in the _setupNotificationChannels method.

[H2] Breaking Down the Implementation
[T] Let's examine what's happening in our solution:

[List]
1. We create a notification builder with specific parameters:
   - An ID to uniquely identify this notification
   - A title and body that clearly communicate the purpose
   - The work channel ID to categorize this as a work notification
   - High importance level since meetings are time-sensitive
2. We enable the custom sound by calling setCustomSound(true)
3. We show the notification, which triggers the system to display it using our configured channel

[T] When this notification appears, the user will hear the custom sound we associated with work notifications, helping them immediately recognize that it's work-related without even looking at their device.

[T] Up Next, we'll summarize what we've learned in this section and preview what's coming in the next section.


Last Chapter Title
Section Summary

[T] In this chapter, we'll review the key concepts we've learned about notification channels and how they enhance the user experience of our app.

[H2] Key Takeaways

[T] Let's summarize what we have covered in this section:
[List]
We learned about notification channels and their importance in Android
We implemented multiple notification channels with different importance levels
We created pairs of channels for each category - a standard channel and a channel with custom sound
We implemented iOS notification categories for a consistent cross-platform experience
We used the NotificationChannel enum to organize notifications and provide a consistent visual identity
We learned how to select the appropriate channel based on the notification's category and custom sound flag

[T] Think of notification channels like the sorting system in a post office. Just as mail gets sorted into different categories (express, standard, bulk) with different handling priorities, our notification channels sort our app's messages into different categories (work, personal, health) with different importance levels and sounds.

[T] You now have a solid understanding of how to organize notifications using channels and provide a consistent experience across both Android and iOS platforms. Your app can now deliver notifications with different importance levels and sounds, giving users more control over how they receive information.

[T] The notification channels we've implemented provide a foundation for more advanced notification features, which we'll explore in the upcoming sections.

[T] Up Next, we'll dive into scheduling notifications with time zone awareness, where you'll learn how to create notifications that appear at specific times in the future, regardless of the user's location or time zone changes.
