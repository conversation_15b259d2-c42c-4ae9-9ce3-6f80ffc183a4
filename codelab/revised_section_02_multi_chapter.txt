[H1] Displaying Instant Notifications in Flutter

<!-- Chapter 1: Section Overview -->
[H2] Section Overview

[T] In this chapter, we'll set the stage for implementing instant notifications in our Flutter app. Just like a doorbell alerts you immediately when someone arrives, instant notifications provide timely information to your app's users without delay.

[T] In this section, you'll explore:
[List]
How to initialize the notification manager and request permissions
How to implement instant notifications that appear immediately
How to customize notification appearance with different icons and styles
How to handle notification taps and navigate to specific screens

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this section.

[T] Let's begin!


<!-- Chapter 2: Setting Up the Notification Manager -->
[H2] Setting Up the Notification Manager

[T] In this chapter, we'll implement the NotificationManager class and its initialization methods to handle all notification-related operations in our app. Think of this class as the central post office for your app's notifications—it manages sending, scheduling, and organizing all messages to your users.

[H3] Understanding the Notification Manager
[T] The NotificationManager class serves as a facade for the flutter_local_notifications plugin, providing a simplified API for common notification tasks and handling platform-specific details.

[T] Let's examine the structure of the NotificationManager class in the starter project:

[Code: dart]
class NotificationManager {
  // Singleton pattern implementation
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  // Initialization methods
  Future<void> init() async {
    // TODO: Implement initialization
    debugPrint('NotificationManager.init() not implemented yet');
  }

  // Other methods with TODOs...
}

[T] The class uses the Singleton pattern to ensure that only one instance exists throughout the app. This is important for managing notification IDs and ensuring consistent behavior, similar to how a city has just one postal service to avoid confusion and duplication of mail delivery.

[H3] Initializing the Notification Manager
[T] The first step is to initialize our notification manager in the main.dart file. This will set up the notification plugin and prepare it for use throughout the app.

[T] Open **lib/main.dart** and find the main() function:

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // TODO: Initialize notification manager
    // await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] Uncomment the line that initializes the notification manager. This is a critical first step—just as you need to set up your phone's settings before receiving calls, your app needs to initialize the notification system before it can display alerts to users.

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize notification manager
    await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] This initializes the notification manager before the app starts, ensuring that all notification functionality is ready when the app launches.

[H3] Implementing the init() Method
[T] Now, let's implement the init() method in the NotificationManager class. This method will initialize the notification plugin and set up the required configuration.

[T] Open **lib/service/notification-manager/notification_manager.dart** and implement the init() method:

[Code: dart]
/// Initializes the notification system.
///
/// Must be called before any notifications are shown. Configures time zones,
/// notification settings, and platform-specific channels.
Future<void> init() async {
  if (_isInitialized) return;

  await _initializeTimeZones();
  await _initializeNotificationSettings();
  await _setupNotificationChannels();

  _isInitialized = true;
  debugPrint('Notification manager initialized');
}

[T] This method calls three helper methods to initialize different aspects of the notification system:

[List]
**_initializeTimeZones()**: Sets up time zone handling for scheduled notifications
**_initializeNotificationSettings()**: Configures notification settings and tap handling
**_setupNotificationChannels()**: Creates notification channels for Android

[T] Think of these three methods as setting up the foundation for our notification system—like installing the wiring, configuring the control panel, and setting up the zones for a home security system.


[H3] Initializing Time Zones
[T] Even though we're not implementing scheduled notifications yet, it's good to set up time zone handling from the beginning. Let's implement the _initializeTimeZones() method:

[Code: dart]
/// Initializes time zone data for scheduled notifications.
///
/// Sets up the time zone database and configures the default time zone
/// to the device's local time zone.
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();

    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();

    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');

    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));

    debugPrint('Falling back to UTC timezone');
  }
}

[T] This method initializes the time zone database and sets the default time zone to the device's local time zone. It also includes error handling to fall back to UTC if the initialization fails. This is similar to how a smart alarm clock needs to know your local time zone to wake you up at the correct time, regardless of where you are in the world.


<!-- Chapter 3: Configuring Notification Settings and Channels -->

[H3] Quiz: Test Your Knowledge

[T] Let's test your understanding with a few multiple-choice questions:

[T] **Question 1**: Which method is responsible for actually showing an instant notification?
[List]
A. NotificationBuilder.show()
B. NotificationManager.showInstantNotification()
C. FlutterLocalNotificationsPlugin.show()
D. NotificationManager.displayNotification()

[T] **Question 2**: What should you do if you want to change the default notification icon on Android?
[List]
A. Modify the app icon in the Android manifest
B. Update the NotificationResources.defaultIcon constant
C. Override the icon parameter in AndroidNotificationDetails
D. Call setIcon() on the NotificationBuilder

[T] **Question 3**: How are deep links handled when a notification is tapped?
[List]
A. The app automatically opens the notification details screen
B. The _handleNotificationTap method extracts the deep link and passes it to DeepLinkHandler
C. The onDidReceiveNotificationResponse callback handles navigation
D. The notification payload contains a route name that's pushed to the navigator
[H2] Configuring Notification Settings and Channels

[T] In this chapter, we'll implement the methods to configure notification settings, set up notification channels for Android, and handle notification taps. Just as your phone has different ringtones and vibration patterns for different types of calls, our app needs different notification settings for various types of alerts.

[H3] Initializing Notification Settings
[T] Let's implement the _initializeNotificationSettings() method to configure the notification plugin:

[Code: dart]
/// Initializes notification settings and callback handlers.
///
/// Configures platform-specific initialization settings and sets up
/// notification tap handling.
Future<void> _initializeNotificationSettings() async {
  // Define the initialization settings for Android
  final AndroidInitializationSettings androidInitializationSettings =
      AndroidInitializationSettings(NotificationResources.defaultIcon);

  // Define the initialization settings for iOS
  final DarwinInitializationSettings iosInitializationSettings =
      DarwinInitializationSettings(
    requestAlertPermission: false,
    requestBadgePermission: false,
    requestSoundPermission: false,
    onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) {
      debugPrint(
        'Received notification: id=$id, title=$title, body=$body, payload=$payload',
      );
      // We don't need to handle this callback for iOS 10+ as it's deprecated
      return;
    },
  );

  // Combine the initialization settings
  final InitializationSettings initializationSettings = InitializationSettings(
    android: androidInitializationSettings,
    iOS: iosInitializationSettings,
  );

  // Initialize the plugin with the settings
  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification tap
      debugPrint('Notification tapped: ${response.payload}');

      // If there's a payload, try to parse it and handle the notification
      if (response.payload != null) {
        try {
          final model = NotificationModel.fromPayload(response.payload!);
          _handleNotificationTap(model);
        } catch (e) {
          debugPrint('Failed to parse notification payload: $e');
        }
      }
    },
    onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
  );

  debugPrint('Initialized notification settings');
}

[T] This method configures the notification plugin with platform-specific settings and sets up callbacks to handle notification taps in both the foreground and background. It's like setting up your doorbell to not only ring when pressed but also to show you who's at the door and let you talk to them.

[H3] Handling Notification Taps
[T] Now, let's add the _handleNotificationTap method to handle notification taps:

[Code: dart]
/// Handles a notification tap event.
///
/// Processes the notification payload and navigates to the appropriate screen
/// or performs the requested action.
void _handleNotificationTap(NotificationModel model) {
  // If the notification has a deep link, handle it
  if (model.deepLink != null) {
    final uri = Uri.parse(model.deepLink!);
    DeepLinkHandler.instance.handleDeepLink(uri);
  } else {
    // If there's no deep link, navigate to the notification details screen
    AppRouter.navigatorKey.currentState?.pushNamed(
      AppRoutes.notificationDetails.value,
      arguments: {'id': model.id},
    );
  }
}

[T] This method extracts the deep link from the notification payload and passes it to the DeepLinkHandler, which will navigate to the appropriate screen. If there's no deep link, it navigates to the notification details screen. This is similar to how tapping on a text message notification opens that specific conversation, rather than just opening the messaging app.

[H3] Setting Up Notification Channels
[T] Android 8.0 (API level 26) and higher requires notification channels to categorize notifications and give users control over which types they receive. Let's implement the _setupNotificationChannels method:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined notification channels with different importance levels
/// and configurations. This is required for Android 8.0 (API level 26) and higher.
Future<void> _setupNotificationChannels() async {
  // Only needed for Android
  if (!Platform.isAndroid) return;

  // Get the Android-specific implementation
  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin == null) {
    debugPrint('Failed to get Android plugin implementation');
    return;
  }

  // Create the default channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.defaultChannel,
      'Default Notifications',
      description: 'Default notification channel',
      importance: Importance.defaultImportance,
    ),
  );

  debugPrint('Set up notification channels');
}

[T] This method creates a default notification channel for Android. We'll expand on this in a later section to create more channels with different configurations. Think of notification channels like different categories in your email inbox—they help organize notifications and allow users to control which types they want to receive.

[T] For example, a user might want to receive work-related notifications with sound and vibration, but prefer personal notifications to be silent. Notification channels make this level of customization possible.

[H3] Quiz Answers

[T] Here are the answers to the quiz questions:

[List]
**Question 1**: C. FlutterLocalNotificationsPlugin.show() - While NotificationBuilder.show() initiates the process and NotificationManager.showInstantNotification() handles the configuration, the actual display is performed by the FlutterLocalNotificationsPlugin.show() method.

**Question 2**: B. Update the NotificationResources.defaultIcon constant - This constant is used throughout the app to specify the default notification icon. Changing it will update the icon for all notifications that don't specify a custom icon.

**Question 3**: B. The _handleNotificationTap method extracts the deep link and passes it to DeepLinkHandler - When a notification is tapped, the payload is parsed into a NotificationModel, and if it contains a deep link, the _handleNotificationTap method passes it to the DeepLinkHandler to navigate to the appropriate screen.


<!-- Chapter 4: Implementing Notifications and Platform-Specific Details -->
[H2] Implementing Notifications and Platform-Specific Details

[T] In this chapter, we'll implement permission requests, instant notifications, and platform-specific notification details. Just as a delivery service needs your permission to leave packages at your door and customizes delivery options based on your preferences, we'll set up our notification system to respect user permissions and adapt to different platforms.

[H3] Implementing Permission Requests
[T] Both iOS and Android require permission to show notifications. Let's implement the requestPermissions() method:

[Code: dart]
/// Requests notification permissions from the system.
///
/// Returns `true` if permissions are granted, `false` otherwise.
/// Handles platform-specific permission requests for iOS and Android.
Future<bool> requestPermissions() async {
  if (Platform.isIOS) {
    final plugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >();
    final bool? result = await plugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );
    return result ?? false;
  } else if (Platform.isAndroid) {
    final plugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();
    final bool? result = await plugin?.requestNotificationsPermission();
    return result ?? false;
  }
  return false;
}

[T] This method handles permission requests differently for iOS and Android:

[List]
On iOS, it requests permission for alerts, badges, and sounds
On Android, it requests the notification permission (required for Android 13+)

[T] This platform-specific approach is like how different countries might have different procedures for obtaining a permit—the end result is the same (permission granted), but the process varies depending on where you are.

[H3] Adding a Helper Method for Prerequisites
[T] Before showing any notification, we should check if all prerequisites are met. Let's implement the _checkNotificationPrerequisites() method:

[Code: dart]
/// Checks if all prerequisites for showing notifications are met.
///
/// Verifies that the plugin is initialized and permissions are granted.
/// Returns `true` if notifications can be shown, `false` otherwise.
Future<bool> _checkNotificationPrerequisites() async {
  if (!_isInitialized) {
    debugPrint('Notification manager not initialized');
    return false;
  }

  final bool permissionsGranted = await requestPermissions();
  if (!permissionsGranted) {
    debugPrint('Notification permissions not granted');
    return false;
  }

  return true;
}

[T] This method checks two important prerequisites:

[List]
The notification manager is initialized
Notification permissions are granted

[T] If either condition is not met, the method returns false, and we should not attempt to show notifications. This is like checking that your car has both fuel and a driver before attempting a journey—both are essential prerequisites.

[H3] Implementing Instant Notifications
[T] Now that we've set up the notification manager, let's implement the showInstantNotification() method to display notifications immediately:

[Code: dart]
/// Shows an instant notification immediately.
///
/// Displays a notification with the configuration specified in [model].
/// Validates prerequisites before showing the notification.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // Show the notification
  await _flutterLocalNotificationsPlugin.show(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    details,
    payload: notificationWithDeepLink.toPayload(),
  );

  debugPrint('Showed instant notification: ${notificationWithDeepLink.id}');
}

[T] This method does several important things:

[List]
Checks if the notification prerequisites are met
Adds a deep link to the notification if not already set
Builds platform-specific notification details
Shows the notification with the configured details
Includes the notification payload for handling notification taps

[T] Think of this method as the final assembly line for notifications—it takes all the components we've prepared (permissions, settings, details) and puts them together to create a complete notification ready to be displayed to the user.

[H3] Building Platform-Specific Notification Details
[T] Now, let's implement the _buildNotificationDetails method to configure the platform-specific details. Just as a tailor customizes a suit to fit perfectly, we need to tailor our notifications to look and behave correctly on each platform.

[Code: dart]
/// Builds platform-specific notification details based on the notification model.
///
/// Configures Android and iOS specific settings according to the notification properties.
Future<NotificationDetails> _buildNotificationDetails(NotificationModel model) async {
  // Configure Android-specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    channelDescription: '${model.channelId} notifications',
    importance: _getAndroidImportance(model.level),
    priority: _getAndroidPriority(model.level),
    styleInformation: const DefaultStyleInformation(true, true),
    icon: NotificationResources.defaultIcon,
    playSound: model.customSound,
    enableLights: true,
    fullScreenIntent: model.isFullScreen,
    category: AndroidNotificationCategory.reminder,
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: model.customSound,
    sound: model.customSound ? NotificationResources.customSoundIOS : null,
    interruptionLevel: _getIOSInterruptionLevel(model.level),
  );

  // Combine the platform-specific details
  return NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

[T] This method configures platform-specific details for both Android and iOS, including:

[List]
Channel ID and description
Importance and priority levels
Sound settings
Visual style and appearance options

[T] This platform-specific configuration is crucial because Android and iOS handle notifications differently—like how different car models might have different dashboard layouts but still serve the same purpose of providing information to the driver.

[H3] Implementing Helper Methods for Importance Levels
[T] We also need to implement helper methods to convert our notification level to platform-specific importance and priority levels:

[Code: dart]
/// Converts a NotificationLevel to an Android Importance level.
Importance _getAndroidImportance(NotificationLevel level) {
  switch (level) {
    case NotificationLevel.low:
      return Importance.low;
    case NotificationLevel.normal:
      return Importance.defaultImportance;
    case NotificationLevel.high:
      return Importance.high;
    case NotificationLevel.critical:
      return Importance.max;
  }
}

/// Converts a NotificationLevel to an Android Priority level.
Priority _getAndroidPriority(NotificationLevel level) {
  switch (level) {
    case NotificationLevel.low:
      return Priority.low;
    case NotificationLevel.normal:
      return Priority.defaultPriority;
    case NotificationLevel.high:
      return Priority.high;
    case NotificationLevel.critical:
      return Priority.max;
  }
}

/// Converts a NotificationLevel to an iOS Interruption Level.
DarwinNotificationInterruptionLevel _getIOSInterruptionLevel(NotificationLevel level) {
  switch (level) {
    case NotificationLevel.low:
      return DarwinNotificationInterruptionLevel.passive;
    case NotificationLevel.normal:
      return DarwinNotificationInterruptionLevel.active;
    case NotificationLevel.high:
      return DarwinNotificationInterruptionLevel.timeSensitive;
    case NotificationLevel.critical:
      return DarwinNotificationInterruptionLevel.critical;
  }
}

[T] These methods map our app's notification levels to the platform-specific levels, ensuring consistent behavior across platforms. It's like having a universal translator that converts our app's standardized importance levels into the specific "languages" that Android and iOS understand.

[T] Up Next, we'll implement the NotificationBuilder class to provide a fluent API for creating notifications.


<!-- Chapter 5: Implementing the Notification Builder -->
[H2] Implementing the Notification Builder

[T] In this chapter, we'll implement the NotificationBuilder class to provide a fluent API for creating and configuring notifications. Think of this class as a notification workshop where you can customize every aspect of your notification before sending it out.

[H3] Understanding the Builder Pattern
[T] The Builder pattern is a design pattern that allows for the step-by-step construction of complex objects. It's particularly useful for objects with many optional parameters, like our NotificationModel.

[T] The NotificationBuilder class provides a fluent API, allowing us to chain method calls to configure different aspects of a notification. This is similar to how you might customize a sandwich at a deli—adding ingredients one by one until you have exactly what you want.

[H3] Implementing the show() Method
[T] Open **lib/service/notification-manager/notification_builder.dart** and implement the show() method:

[Code: dart]
/// Shows the notification immediately or at the configured time.
///
/// The behavior depends on the notification type (instant, scheduled, periodic).
///
/// Throws an [ArgumentError] if required properties for the notification type are missing.
Future<void> show() async {
  switch (_model.type) {
    case NotificationType.instant:
      return await _manager.showInstantNotification(model: _model);

    case NotificationType.scheduled:
      if (_model.scheduledTime == null) {
        throw ArgumentError(
          'scheduledTime must be set for scheduled notifications',
        );
      }
      return await _manager.scheduleNotification(model: _model);

    case NotificationType.periodic:
      if (_model.repeatInterval == null) {
        throw ArgumentError(
          'repeatInterval must be set for periodic notifications',
        );
      }
      return await _manager.schedulePeriodic(model: _model);
  }
}

[T] This method delegates to the appropriate NotificationManager method based on the notification type, ensuring that the required properties are set. It acts like a traffic controller, directing each notification to the right processing method based on its type.

[T] For now, we'll only implement the showInstantNotification method in the NotificationManager class. We'll implement the other methods in later sections.

[H3] Using the NotificationBuilder
[T] Here's an example of how to use the NotificationBuilder to create and show a notification:

[Code: dart]
// Create a notification builder
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 1,
  title: 'Hello, World!',
  body: 'This is my first notification',
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
);

// Configure additional properties if needed
builder
  .setType(NotificationType.instant)
  .setCustomSound(true);

// Show the notification
await builder.show();

[T] This code creates a basic notification with a title, body, and channel ID, then configures it as an instant notification with a custom sound, and finally shows it. The fluent API makes the code read almost like a sentence, making it intuitive and easy to understand—similar to how recipe instructions flow naturally from one step to the next.

[T] Up Next, we'll test our implementation by creating and showing instant notifications.


<!-- Chapter 6: Testing Instant Notifications -->
[H2] Testing Instant Notifications

[T] In this chapter, we'll test our implementation by creating and showing instant notifications, and we'll learn how to customize notification icons. Just as you'd test a new doorbell before relying on it, we need to verify our notification system works as expected.

[H3] Creating and Showing Instant Notifications
[T] Now that we've implemented instant notifications, let's test them by creating a simple notification.

[T] Run the app and navigate to the "Create Notification" screen. Fill in the following details:

[List]
**Title**: "Hello, World!"
**Body**: "This is my first notification"
**Channel**: "Default"
**Type**: "Instant"
**Level**: "Normal"

[T] Tap the "Create" button, and you should see a notification appear immediately.

[Image]

[T] If you tap on the notification, it should take you to the notification details screen, demonstrating that the deep link functionality is working correctly. This interaction flow is similar to how clicking on an email notification opens that specific email rather than just the inbox.

[H3] Customizing Notification Icons
[T] By default, notifications use the app icon, but you can customize the icon for different types of notifications. Let's add a custom icon for our notifications.

[T] First, add the icon to the **android/app/src/main/res/drawable** directory. Then, update the NotificationResources class in **lib/core/constants/notification_constants.dart**:

[Code: dart]
/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customIcon = '@drawable/custom_icon';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] Now, update the _buildNotificationDetails method to use the custom icon for certain notifications:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  model.channelId,
  model.channelId.toUpperCase(),
  channelDescription: '${model.channelId} notifications',
  importance: _getAndroidImportance(model.level),
  priority: _getAndroidPriority(model.level),
  styleInformation: const DefaultStyleInformation(true, true),
  icon: model.channelId == NotificationChannelIds.work
      ? NotificationResources.customIcon
      : NotificationResources.defaultIcon,
  playSound: model.customSound,
  enableLights: true,
  fullScreenIntent: model.isFullScreen,
  category: AndroidNotificationCategory.reminder,
);

[T] This code uses a custom icon for work notifications and the default icon for other notifications. Using different icons for different notification types helps users quickly identify the nature of the notification at a glance—similar to how different road signs have distinct shapes to convey their meaning from a distance.

[T] Up Next, we'll tackle a code challenge to test your understanding of notification customization.


<!-- Chapter 7: Code Challenge: Customizing Notification Appearance -->
[H2] Code Challenge: Customizing Notification Appearance

[T] In this chapter, you'll get a chance to practice what you've learned with a code challenge focused on customizing notification appearance. Just as interior designers personalize spaces with color schemes, we'll customize our notifications with distinct visual identities.

[H3] The Challenge
[T] Your challenge is to modify the _buildNotificationDetails method to add a custom color for notifications in the "Personal" channel. You'll need to:

[List]
1. Add a new constant in NotificationResources for the personal color
2. Update the _buildNotificationDetails method to use this color for personal notifications
3. Test the change by creating a personal notification

[T] Take some time to implement this challenge before looking at the solution.

[H3] Solution
[T] Let's go through the solution to the challenge:

[T] First, add a new constant in NotificationResources:

[Code: dart]
/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customIcon = '@drawable/custom_icon';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
  static const int personalColor = 0xFF9C27B0; // Purple color
}

[T] Then, update the _buildNotificationDetails method to use this color for personal notifications:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  model.channelId,
  model.channelId.toUpperCase(),
  channelDescription: '${model.channelId} notifications',
  importance: _getAndroidImportance(model.level),
  priority: _getAndroidPriority(model.level),
  styleInformation: const DefaultStyleInformation(true, true),
  icon: model.channelId == NotificationChannelIds.work
      ? NotificationResources.customIcon
      : NotificationResources.defaultIcon,
  color: model.channelId == NotificationChannelIds.personal
      ? Color(NotificationResources.personalColor)
      : null,
  playSound: model.customSound,
  enableLights: true,
  fullScreenIntent: model.isFullScreen,
  category: AndroidNotificationCategory.reminder,
);

[T] Now, when you create a notification with the "Personal" channel, it will have a purple color on Android devices.

[T] This challenge demonstrates how to customize notifications based on their channel or other properties, allowing for a more personalized notification experience. By using different colors for different notification channels, you're creating a visual language that helps users quickly identify the type of notification at a glance.

[T] Up Next, we'll summarize what we've learned in this section and preview what's coming in the next section.


<!-- Chapter 8: Section Summary -->
[H2] Section Summary

[T] In this chapter, we'll recap what we've learned about implementing instant notifications in Flutter. Like reviewing a blueprint after completing a construction project, this summary helps solidify our understanding of the notification system we've built.

[T] Let's summarize what we have covered in this section:
[List]
We implemented the NotificationManager class to handle notification operations
We initialized the flutter_local_notifications plugin with platform-specific settings
We set up notification channels for Android
We implemented permission requests for both Android and iOS
We created the showInstantNotification method to display notifications immediately
We implemented the NotificationBuilder class with a fluent API for creating notifications
We customized notification appearance with different icons and colors
We tested our implementation by creating and showing instant notifications

[T] You now have a solid understanding of how to display instant notifications in Flutter. You can create notifications with different titles, messages, and appearance options, and handle notification taps to navigate to specific screens.

[H3] Key Takeaways

[Emphasis, download]
**Platform-Specific Configuration**: Both Android and iOS require specific settings for notifications to work properly

**Builder Pattern**: The NotificationBuilder class provides a clean, fluent API for creating notifications

**Permission Handling**: Always check for and request permissions before showing notifications

**Deep Links**: Adding deep links to notifications enables seamless navigation when users tap on them

[T] The code we've implemented in this section provides the foundation for more advanced notification features, which we'll explore in the upcoming sections.

[T] Up Next, we'll dive into organizing notifications with custom channels, where you'll learn how to categorize notifications and give users more control over how they appear.
