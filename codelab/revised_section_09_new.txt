[H1] Enhancing Notifications with Advanced Features

[H2] Overview

[T] In this section, we'll explore advanced notification features that take our notifications to the next level. We'll implement progress indicators, notification grouping, interactive actions, full-screen notifications, and media-rich notifications.

[T] By the end of this section, you'll be able to create rich, interactive notifications that engage users and provide more functionality directly from the notification drawer.

[H3] Learning Objectives

[List]
Display notifications with progress indicators for ongoing tasks
Group multiple notifications into stacks for better organization
Add interactive actions like buttons to notifications
Set up full-screen notifications for urgent alerts
Display media-rich notifications with images

[H2] Implementing Interactive Notification Actions

[T] Notification actions allow users to interact with notifications without opening the app. They appear as buttons in the notification and can trigger specific behaviors when tapped.

[H3] Setting Up Notification Categories

[T] First, let's set up notification categories with actions. Open **lib/service/notification-manager/notification_manager.dart** and modify the _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  final snoozeAction = DarwinNotificationAction.plain(
    NotificationActionIds.snooze,
    NotificationActionTexts.snooze,
    options: {DarwinNotificationActionOption.foreground},
  );
  final dismissAction = DarwinNotificationAction.plain(
    NotificationActionIds.dismiss,
    NotificationActionTexts.dismiss,
    options: {DarwinNotificationActionOption.foreground},
  );
  final replyAction = DarwinNotificationAction.text(
    NotificationActionIds.reply,
    NotificationActionTexts.reply,
    buttonTitle: NotificationActionTexts.reply,
  );

  final interactiveCategory = DarwinNotificationCategory(
    NotificationCategories.interactive,
    actions: [snoozeAction, dismissAction, replyAction],
  );

  final iosSettings = DarwinInitializationSettings(
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
    notificationCategories: [interactiveCategory],
  );

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This sets up notification categories with actions for iOS. For Android, actions are added directly to the notification.

[H3] Adding Action Constants

[T] Let's add constants for notification actions:

[Code: dart]
/// Notification action IDs
class NotificationActionIds {
  static const String dismiss = 'dismiss';
  static const String snooze = 'snooze';
  static const String reply = 'reply';
}

/// Notification action texts
class NotificationActionTexts {
  static const String dismiss = 'Dismiss';
  static const String snooze = 'Snooze';
  static const String reply = 'Reply';
}

/// Notification categories
class NotificationCategories {
  static const String interactive = 'interactive';
}

[H3] Enhancing the Notification Builder

[T] Let's enhance the NotificationBuilder to make it easier to add actions to notifications:

[Code: dart]
/// Configures the notification to include action buttons.
///
/// Enables actions for the notification, which will be determined based on the channel.
NotificationBuilder setActions() {
  _model = _model.copyWith(hasActions: true);
  return this;
}

[T] This method simply sets the hasActions flag to true, which will cause the notification to include actions.

[H3] Modifying the Notification Details Configuration

[T] Now, let's modify the getNotificationDetailsConfig method to add actions to notifications:

[Code: dart]
/// Creates a notification details configuration based on settings.
Future<NotificationDetails> getNotificationDetailsConfig({
  required String channelId,
  required NotificationLevel level,
  bool isFullScreen = false,
  bool imageAttachment = false,
  bool hasActions = false,
  bool customSound = false,
}) async {
  // ... existing code ...

  // Configure Android specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    effectiveChannelId,
    effectiveChannelId.toUpperCase(),
    importance: level.importance,
    priority: level.priority,
    playSound: level.playSound,
    enableVibration: level.vibrate,
    visibility: level.visibility,
    fullScreenIntent: isFullScreen,
    styleInformation:
        imageAttachment
            ? BigPictureStyleInformation(
              ByteArrayAndroidBitmap(imageBytes),
              largeIcon: ByteArrayAndroidBitmap(imageBytes),
            )
            : null,
    actions: hasActions
        ? [
            const AndroidNotificationAction(
              NotificationActionIds.snooze,
              NotificationActionTexts.snooze,
            ),
            const AndroidNotificationAction(
              NotificationActionIds.dismiss,
              NotificationActionTexts.dismiss,
              cancelNotification: true,
            ),
          ]
        : null,
  );

  // Configure iOS specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier:
        hasActions ? NotificationCategories.interactive : channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: level.playSound,
    sound: customSound ? NotificationResources.customSoundIOS : null,
    attachments:
        imageAttachment
            ? [
              DarwinNotificationAttachment(
                filePath,
                thumbnailClippingRect:
                    const DarwinNotificationAttachmentThumbnailClippingRect(
                      x: 0.0,
                      y: 0.0,
                      width: 0.3,
                      height: 0.3,
                    ),
              ),
            ]
            : null,
  );

  // ... rest of the method ...
}

[T] This adds action buttons to Android notifications and sets the appropriate category for iOS notifications.

[H2] Implementing Full-Screen Notifications

[T] Full-screen notifications appear as high-priority alerts that can break through Do Not Disturb mode on some devices. They're useful for urgent notifications that require immediate attention.

[H3] Enhancing the Notification Builder

[T] The NotificationBuilder already has a method to set a notification as full-screen:

[Code: dart]
/// Configures the notification to appear as a full-screen high-priority alert.
///
/// Full-screen notifications can break through Do Not Disturb mode on some devices.
///
/// [isFullScreen] Whether the notification should be displayed as full-screen
NotificationBuilder setFullScreen(bool isFullScreen) {
  // Avoid unnecessary updates
  if (_model.isFullScreen == isFullScreen) {
    return this;
  }
  _model = _model.copyWith(isFullScreen: isFullScreen);
  return this;
}

[T] This method sets the isFullScreen flag, which is used in the getNotificationDetailsConfig method to set the fullScreenIntent property of the AndroidNotificationDetails.

[H2] Implementing Media-Rich Notifications

[T] Media-rich notifications can display images, making them more visually appealing and informative.

[H3] Enhancing the Notification Builder

[T] Let's enhance the NotificationBuilder to make it easier to add images to notifications:

[Code: dart]
/// Configures the notification to include an image.
///
/// Sets the notification to display an image, which will be loaded from the app's assets.
NotificationBuilder setImage(bool imageAttachment) {
  _model = _model.copyWith(imageAttachment: imageAttachment);
  return this;
}

[T] This method sets the imageAttachment flag, which is used in the getNotificationDetailsConfig method to add an image to the notification.

[H3] Preparing Images for Notifications

[T] To use images in notifications, we need to prepare them in the appropriate format. For Android, we use the BigPictureStyleInformation class, and for iOS, we use DarwinNotificationAttachment.

[T] In our implementation, we're using a sample image encoded as base64 for simplicity:

[Code: dart]
final Uint8List imageBytes = base64Decode(
  NotificationUtils.sampleImageBase64,
);

// Get the temporary directory using path_provider
final Directory tempDir = await getTemporaryDirectory();
final String filePath = '${tempDir.path}/notification_image.png';
final File imageFile = File(filePath);

// Only write the file if it doesn't already exist
if (!await imageFile.exists()) {
  await imageFile.writeAsBytes(imageBytes);
}

[T] This code decodes a base64-encoded image and saves it to a temporary file, which can be used for both Android and iOS notifications.

[H2] Implementing Progress Notifications

[T] Progress notifications display a progress bar in the notification, which is useful for long-running operations like downloads or uploads.

[H3] Enhancing the Notification Builder

[T] Let's enhance the NotificationBuilder to make it easier to create progress notifications:

[Code: dart]
/// Configures the notification to display a progress indicator.
///
/// Shows a progress bar in the notification, useful for download or
/// processing tasks.
///
/// [current] Current progress value
/// [max] Maximum progress value
///
/// Throws an [ArgumentError] if values are invalid.
NotificationBuilder setProgress(int current, int max) {
  if (current < 0) {
    throw ArgumentError('Current progress cannot be negative');
  }
  if (max <= 0) {
    throw ArgumentError('Maximum progress must be positive');
  }
  if (current > max) {
    throw ArgumentError('Current progress cannot exceed maximum');
  }
  _model = _model.copyWith(currentProgress: current, maxProgress: max);
  return this;
}

[T] This method configures a notification to display a progress bar with the specified current and maximum values.

[H3] Implementing the Progress Notification Method

[T] Now, let's implement a method to show a progress notification:

[Code: dart]
/// Shows a notification with a progress indicator.
///
/// Updates an existing notification with progress information or creates a new one.
/// The progress is displayed as a progress bar in the notification.
///
/// [model] must include maxProgress and currentProgress values.
Future<void> showProgressNotification({
  required NotificationModel model,
}) async {
  if (model.maxProgress == null || model.currentProgress == null) {
    throw ArgumentError(
      'maxProgress and currentProgress must be provided for progress notifications',
    );
  }
  
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show progress notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Configure Android-specific details for progress notification
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    importance: Importance.low,
    priority: Priority.low,
    onlyAlertOnce: true,
    showProgress: true,
    maxProgress: model.maxProgress!,
    progress: model.currentProgress!,
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: false,
  );

  NotificationDetails details = NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );

  await _flutterLocalNotificationsPlugin.show(
    model.id,
    model.title,
    model.body,
    details,
    payload: model.toPayload(),
  );
}

[T] This method creates a notification with a progress bar, which is updated as the operation progresses.

[H2] Implementing Grouped Notifications

[T] Grouped notifications combine multiple related notifications into a single expandable group, reducing clutter in the notification drawer.

[H3] Implementing the Grouped Notification Method

[T] Let's implement a method to show grouped notifications:

[Code: dart]
/// Shows a set of grouped notifications with a summary.
///
/// Displays multiple related notifications as a group with a summary notification
/// on platforms that support it (primarily Android).
Future<void> showGroupedNotifications({
  required String groupKey,
  required String groupChannelId,
  required String groupTitle,
  required String groupSummary,
  required List<NotificationModel> notifications,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show grouped notifications: prerequisites not met');
    return;
  }

  // Create individual notifications
  for (final notification in notifications) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: false,
    );

    DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      categoryIdentifier: groupChannelId,
      threadIdentifier: groupKey,
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      notification.id,
      notification.title,
      notification.body,
      details,
      payload: notification.toPayload(),
    );
  }

  // Create summary notification for Android
  if (Platform.isAndroid) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: true,
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0, // Use a unique ID for the summary
      groupTitle,
      groupSummary,
      details,
    );
  }
}

[T] This method creates individual notifications and a summary notification that groups them together. On Android, the summary notification uses the setAsGroupSummary property to indicate that it's a summary notification.

[H3] Creating a Helper Method for Group Notifications

[T] Let's create a helper method to make it easier to create grouped notifications:

[Code: dart]
/// Creates a group of notifications with a summary.
///
/// Converts the list of [notifications] to models and calls
/// [showGroupedNotifications] to display them.
Future<void> createGroupNotification({
  required String groupKey,
  required String channelId,
  required String groupTitle,
  required String groupSummary,
  required List<NotificationBuilder> notifications,
}) async {
  List<NotificationModel> models =
      notifications.map((builder) => builder.model).toList();
  return await showGroupedNotifications(
    groupKey: groupKey,
    groupChannelId: channelId,
    groupTitle: groupTitle,
    groupSummary: groupSummary,
    notifications: models,
  );
}

[T] This method takes a list of NotificationBuilder objects, converts them to NotificationModel objects, and calls showGroupedNotifications to display them.

[H2] Testing Advanced Notification Features

[T] Now that we've implemented various advanced notification features, let's test them by creating notifications with these features.

[H3] Testing Interactive Notifications

[T] Let's test interactive notifications by creating a notification with actions:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: 'Interactive Notification',
  body: 'This notification has action buttons',
  channelId: NotificationChannelIds.defaultChannel,
)
.setActions()
.build();

await NotificationManager().showInstantNotification(model: notification);

[T] This should show a notification with action buttons.

[H3] Testing Full-Screen Notifications

[T] Let's test full-screen notifications by creating a notification with the full-screen flag:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 456,
  title: 'Full-Screen Notification',
  body: 'This notification appears as a high-priority alert',
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.urgent,
)
.setFullScreen(true)
.build();

await NotificationManager().showInstantNotification(model: notification);

[T] This should show a notification as a high-priority alert that can break through Do Not Disturb mode on some devices.

[H3] Testing Media-Rich Notifications

[T] Let's test media-rich notifications by creating a notification with an image:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 789,
  title: 'Image Notification',
  body: 'This notification includes an image',
  channelId: NotificationChannelIds.defaultChannel,
)
.setImage(true)
.build();

await NotificationManager().showInstantNotification(model: notification);

[T] This should show a notification with an image.

[H3] Testing Progress Notifications

[T] Let's test progress notifications by creating a notification with a progress bar:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 101,
  title: 'Download Progress',
  body: 'Downloading file... 50%',
  channelId: NotificationChannelIds.defaultChannel,
)
.setProgress(50, 100)
.build();

await NotificationManager().showProgressNotification(model: notification);

[T] This should show a notification with a progress bar at 50%.

[H3] Testing Grouped Notifications

[T] Let's test grouped notifications by creating a group of notifications:

[Code: dart]
final notifications = [
  NotificationBuilder.create(
    NotificationManager(),
    id: 201,
    title: 'Message 1',
    body: 'This is the first message',
    channelId: NotificationChannelIds.defaultChannel,
  ),
  NotificationBuilder.create(
    NotificationManager(),
    id: 202,
    title: 'Message 2',
    body: 'This is the second message',
    channelId: NotificationChannelIds.defaultChannel,
  ),
  NotificationBuilder.create(
    NotificationManager(),
    id: 203,
    title: 'Message 3',
    body: 'This is the third message',
    channelId: NotificationChannelIds.defaultChannel,
  ),
];

await NotificationManager().createGroupNotification(
  groupKey: 'message_group',
  channelId: NotificationChannelIds.defaultChannel,
  groupTitle: 'Messages',
  groupSummary: '3 new messages',
  notifications: notifications,
);

[T] This should show a group of notifications that are collapsed into a single summary notification.

[H2] Best Practices for Advanced Notifications

[T] When implementing advanced notification features, consider the following best practices:

[List]
**Use Progress Notifications Appropriately**: Only use progress notifications for operations that take a significant amount of time
**Group Related Notifications**: Use grouped notifications to reduce clutter in the notification drawer
**Optimize Images**: Use appropriately sized images to avoid excessive memory usage
**Test on Different Devices**: Advanced notification features may behave differently on different devices
**Consider Battery Impact**: Advanced features like images and progress updates can impact battery life
**Provide Fallbacks**: Implement fallbacks for devices that don't support certain features

[H2] Summary

[T] In this section, we've implemented advanced notification features:

[List]
Added interactive actions to notifications for more user engagement
Implemented full-screen notifications for urgent alerts
Created media-rich notifications with images for more visual appeal
Added progress indicators for showing real-time updates in notifications
Implemented grouped notifications to reduce clutter in the notification drawer
Enhanced the NotificationBuilder with methods for these advanced features
Tested different types of advanced notifications

[T] With these advanced features, our notification system is now much more powerful and engaging, providing a rich and interactive notification experience for users.

[H3] Code to Implement

[T] Here's a summary of the code we've implemented in this section:

[List]
**NotificationManager._initializeNotificationSettings()**: Sets up notification categories with actions
**NotificationBuilder.setActions()**: Configures a notification with actions
**NotificationBuilder.setFullScreen()**: Configures a notification as full-screen
**NotificationBuilder.setImage()**: Configures a notification with an image
**NotificationBuilder.setProgress()**: Configures a notification with a progress indicator
**NotificationManager.showProgressNotification()**: Shows a notification with a progress indicator
**NotificationManager.showGroupedNotifications()**: Shows a group of related notifications
**NotificationManager.createGroupNotification()**: Helper method for creating grouped notifications
