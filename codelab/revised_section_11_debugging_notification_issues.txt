Write Your eBook

Section 3 eBook
Section Title
Debugging Common Notification Issues


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll learn how to identify, diagnose, and resolve common notification issues in Flutter applications. Just as a mechanic needs diagnostic tools to fix a car, developers need debugging techniques to ensure notifications are delivered reliably.

[T] In this section, you'll explore:
[List]
Common notification issues across Android and iOS platforms and how to address them
Debugging techniques to diagnose notification failures using logging and platform tools
Error handling strategies to gracefully manage notification failures
Creating a notification troubleshooter to help users resolve common issues

[T] Let's dive into the world of notification debugging!


Chapter Title
Understanding Common Notification Issues

[H2] Understanding Common Notification Issues
[T] Let's explore the most common issues that can prevent notifications from working correctly.

[H3] Permission-Related Issues
[T] On Android 13+ and iOS, explicit permission is required to show notifications:

[Code: dart]
/// Checks if notification permission is granted.
Future<bool> _checkNotificationPermission() async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();
    
    return await plugin?.areNotificationsEnabled() ?? false;
  } else if (Platform.isIOS) {
    return await _checkIOSNotificationPermission();
  }
  
  return false;
}

/// Requests notification permission with an explanation dialog.
Future<bool> requestPermissionsWithExplanation(BuildContext context) async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();
    
    if (plugin == null) return false;
    
    final bool? hasPermission = await plugin.areNotificationsEnabled();
    if (hasPermission == true) return true;
    
    // Show explanation dialog
    final bool shouldRequest = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Permission'),
        content: const Text(
          'Timora needs notification permission to send you reminders and alerts.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('DENY'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('ALLOW'),
          ),
        ],
      ),
    ) ?? false;
    
    if (!shouldRequest) return false;
    
    // Request the permission
    return await plugin.requestNotificationsPermission() ?? false;
  }
  
  // iOS implementation...
  return false;
}

[H3] Notification Delivery Issues
[T] Battery optimization on Android can prevent notifications from being delivered on time:

[Code: dart]
Future<void> _disableBatteryOptimization() async {
  if (Platform.isAndroid) {
    final intent = AndroidIntent(
      action: 'android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS',
      data: 'package:${await PackageInfo.fromPlatform().then((info) => info.packageName)}',
    );
    await intent.launch();
  }
}

[T] For scheduled notifications on Android, use AndroidScheduleMode.exactAllowWhileIdle to ensure delivery even in doze mode:

[Code: dart]
await _flutterLocalNotificationsPlugin.zonedSchedule(
  model.id,
  model.title,
  model.body,
  scheduledDate,
  details,
  androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  uiLocalNotificationDateInterpretation:
      UILocalNotificationDateInterpretation.wallClockTime,
  payload: model.toPayload(),
);

[H3] Time Zone Issues
[T] Scheduled notifications can fail if time zone changes aren't handled properly:

[Code: dart]
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();
    
    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    
    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));
    
    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');
    
    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));
  }
}


Chapter Title
Implementing Debugging Techniques

[H2] Implementing Debugging Techniques
[T] Let's explore techniques for diagnosing notification issues.

[H3] Enhanced Logging
[T] Implement a logging system to capture notification events:

[Code: dart]
/// Logs a notification event with relevant details.
void _logNotificationEvent(
  String event, {
  int? id,
  String? title,
  NotificationType? type,
  String? error,
}) {
  final buffer = StringBuffer();
  buffer.write('📱 NOTIFICATION: $event');
  
  if (id != null) buffer.write(' | ID: $id');
  if (title != null) buffer.write(' | Title: $title');
  if (type != null) buffer.write(' | Type: ${type.name}');
  if (error != null) buffer.write(' | ERROR: $error');
  
  debugPrint(buffer.toString());
}

[T] Enhance notification methods to use this logging:

[Code: dart]
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  try {
    final canShow = await _checkNotificationPrerequisites();
    if (!canShow) {
      _logNotificationEvent(
        'Cannot show notification: prerequisites not met',
        id: model.id,
        title: model.title,
        type: model.type,
      );
      return;
    }

    // ... existing code ...

    _logNotificationEvent(
      'Showed instant notification',
      id: model.id,
      title: model.title,
      type: model.type,
    );
  } catch (e) {
    _logNotificationEvent(
      'Failed to show notification',
      id: model.id,
      title: model.title,
      type: model.type,
      error: e.toString(),
    );
    rethrow;
  }
}

[H3] Using Platform-Specific Debugging Tools
[T] For iOS, use the Xcode Console to view logs:
[List]
Open Xcode and connect your iOS device
Select Window > Devices and Simulators
Select your device and click on "Open Console"
Filter logs by entering "flutter" or "notification"

[T] For Android, use ADB Logcat:
[List]
Connect your Android device via USB
Run: `adb logcat -s flutter`
Filter for notification logs: `adb logcat -s flutter:* *:E`

[H3] Testing on Different Devices and States
[T] Create a test plan that covers various scenarios:

[Code: dart]
Future<void> runNotificationTests() async {
  // Test instant notification
  await testInstantNotification();
  
  // Test scheduled notification
  await testScheduledNotification();
  
  // Test periodic notification
  await testPeriodicNotification();
  
  // Test notification with actions
  await testInteractiveNotification();
  
  _logNotificationEvent('Completed notification tests');
}


Chapter Title
Implementing Error Handling and Troubleshooting

[H2] Implementing Error Handling and Troubleshooting
[T] Let's implement robust error handling and a troubleshooter for notifications.

[H3] Try-Catch Blocks and User Feedback
[T] Wrap notification operations in try-catch blocks and provide user feedback:

[Code: dart]
Future<void> showNotificationWithErrorHandling({
  required NotificationModel model,
  required BuildContext context,
}) async {
  try {
    await showInstantNotification(model: model);
  } catch (e) {
    // Show a snackbar with the error
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Failed to show notification'),
        action: SnackBarAction(
          label: 'Retry',
          onPressed: () => showNotificationWithErrorHandling(
            model: model,
            context: context,
          ),
        ),
      ),
    );
  }
}

[H3] Device-Specific Handling
[T] Implement device-specific error handling for manufacturers with aggressive battery optimization:

[Code: dart]
Future<String> _getBatteryOptimizationInstructions() async {
  if (!Platform.isAndroid) return '';
  
  final deviceInfo = await DeviceInfoPlugin().androidInfo;
  final manufacturer = deviceInfo.manufacturer.toLowerCase();
  
  if (manufacturer.contains('xiaomi')) {
    return 'For Xiaomi devices: Go to Settings > Apps > Manage Apps > Timora > '
           'Battery > No restrictions and enable Autostart.';
  } else if (manufacturer.contains('huawei')) {
    return 'For Huawei devices: Go to Settings > Apps > Timora > '
           'Battery > Launch and disable Manage automatically.';
  }
  
  return 'To ensure reliable notifications, disable battery optimization for this app.';
}

[H3] Creating a Notification Troubleshooter
[T] Implement a troubleshooter that checks for common issues:

[Code: dart]
class NotificationIssue {
  final String title;
  final String description;
  final String solution;
  final String actionLabel;
  final VoidCallback action;

  NotificationIssue({
    required this.title,
    required this.description,
    required this.solution,
    required this.actionLabel,
    required this.action,
  });
}

class NotificationTroubleshooter {
  Future<List<NotificationIssue>> checkForIssues() async {
    final issues = <NotificationIssue>[];
    
    // Check notification permissions
    final hasPermission = await NotificationManager()._checkNotificationPermission();
    if (!hasPermission) {
      issues.add(NotificationIssue(
        title: 'Notification Permission Denied',
        description: 'You have not granted permission for notifications.',
        solution: 'Open app settings and enable notifications.',
        actionLabel: 'Open Settings',
        action: openAppSettings,
      ));
    }
    
    // Check battery optimization (Android only)
    if (Platform.isAndroid) {
      final isBatteryOptimized = await _isBatteryOptimized();
      if (isBatteryOptimized) {
        issues.add(NotificationIssue(
          title: 'Battery Optimization Enabled',
          description: 'Battery optimization may prevent notifications from appearing.',
          solution: 'Disable battery optimization for this app.',
          actionLabel: 'Disable Optimization',
          action: _disableBatteryOptimization,
        ));
      }
    }
    
    return issues;
  }
}

[T] Create a UI for the troubleshooter:

[Code: dart]
class NotificationTroubleshooterPage extends StatefulWidget {
  @override
  _NotificationTroubleshooterPageState createState() => _NotificationTroubleshooterPageState();
}

class _NotificationTroubleshooterPageState extends State<NotificationTroubleshooterPage> {
  final _troubleshooter = NotificationTroubleshooter();
  List<NotificationIssue> _issues = [];
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _checkForIssues();
  }
  
  Future<void> _checkForIssues() async {
    setState(() => _isLoading = true);
    final issues = await _troubleshooter.checkForIssues();
    setState(() {
      _issues = issues;
      _isLoading = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notification Troubleshooter')),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _checkForIssues,
              child: _issues.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, color: Colors.green, size: 64),
                          SizedBox(height: 16),
                          Text('No issues found!', 
                               style: Theme.of(context).textTheme.headline6),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _issues.length,
                      padding: EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final issue = _issues[index];
                        return Card(
                          margin: EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(issue.title, 
                                     style: Theme.of(context).textTheme.headline6),
                                SizedBox(height: 8),
                                Text(issue.description),
                                SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: issue.action,
                                  child: Text(issue.actionLabel),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _checkForIssues,
        tooltip: 'Refresh',
        child: Icon(Icons.refresh),
      ),
    );
  }
}


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored common notification issues and how to debug them:

[List]
Understood permission issues on Android 13+ and iOS and implemented proper permission handling
Addressed device-specific optimizations that affect notification delivery
Implemented time zone handling for scheduled notifications
Added enhanced logging and error handling to diagnose notification failures
Created a notification troubleshooter to help users resolve common issues

[H3] Key Takeaways
[List]
Always check and request permissions before showing notifications
Handle device-specific optimizations, especially on Android devices
Use proper error handling and provide user feedback when notifications fail
Implement a troubleshooter to help users resolve common notification issues
Test your notification system thoroughly on different devices and in different app states

[T] By implementing these best practices, you'll create a robust notification system that enhances your app's user experience and keeps users engaged.
