Section 4 eBook
Scheduling Notifications with Time Zone Awareness

Chapter Title 
Section Overview 

[T] In this section, you'll explore: 
[List] 
Understanding the importance of time zone handling in scheduled notifications
Initializing time zone data using the timezone package
Implementing scheduled notifications with time zone awareness
Adding date and time pickers to the UI for scheduling notifications
Testing scheduled notifications across different time zones

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section. 

[T] Let's begin! 


Next Chapter Title 
Understanding Time Zone Handling in Notifications

[T] In this chapter, we'll explore why time zones are crucial for scheduled notifications and how to properly handle them in your Flutter app.

[H2] Why Time Zones Matter
[T] Time zones are crucial for scheduled notifications. Consider these scenarios:

[List]
A user schedules a notification for 8:00 AM and then travels to a different time zone
A user in New York schedules a notification for a friend in London
A notification is scheduled for a specific global event (like a live stream)

[T] In each case, we need to decide whether the notification should appear at the local time (8:00 AM in the current time zone) or at a specific absolute time (8:00 AM in the original time zone).

[T] For most reminder apps, the first scenario is the most common: users want their notifications to appear at the same local time, regardless of their current time zone. For example, a "Take medication at 8:00 AM" reminder should appear at 8:00 AM local time, even if the user travels to a different time zone.

[H2] Time Zone Libraries
[T] To handle time zones correctly, we're using two packages:

[List]
**timezone**: Provides time zone data and utilities for working with time zones
**flutter_timezone**: Helps determine the device's local time zone

[T] These packages allow us to:

[List]
Initialize the time zone database with the latest data
Get the device's local time zone
Convert between different time zones
Schedule notifications with proper time zone handling

[H2] Reviewing Time Zone Initialization
[T] We've already implemented the _initializeTimeZones() method in the previous section. Let's review it to ensure it's correctly set up:

[Code: dart]
/// Initializes time zone data for scheduled notifications.
///
/// Sets up the time zone database and configures the default time zone
/// to the device's local time zone.
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();
    
    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    
    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));
    
    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');
    
    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));
    
    debugPrint('Falling back to UTC timezone');
  }
}

[T] This method initializes the time zone database and sets the default time zone to the device's local time zone. It also includes error handling to fall back to UTC if the initialization fails.

[T] Up Next: We'll implement the core functionality for scheduling notifications with proper time zone awareness.


Next Chapter Title 
Implementing Scheduled Notifications

[T] In this chapter, we'll implement the core functionality for scheduling notifications at specific times with proper time zone handling.

[H2] The scheduleNotification Method
[T] Let's implement the scheduleNotification() method in the NotificationManager class. This method will schedule a notification to appear at a specific time in the future.

[Code: dart]
/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot schedule notification: prerequisites not met');
    return;
  }

  if (model.scheduledTime == null) {
    throw ArgumentError(
      'scheduledTime must be provided for scheduled notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Convert DateTime to TZDateTime for proper timezone handling
  final scheduledDate = tz.TZDateTime.from(
    notificationWithDeepLink.scheduledTime!,
    tz.local,
  );

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // Schedule the notification
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    scheduledDate,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
    matchDateTimeComponents: null,
    payload: notificationWithDeepLink.toPayload(),
  );

  debugPrint(
    'Scheduled notification: ${notificationWithDeepLink.id} for ${scheduledDate.toIso8601String()}',
  );
}

[T] This method is similar to showInstantNotification(), but with a few key differences:

[List]
It checks if the notification should be enabled (and cancels it if not)
It validates that scheduledTime is not null
It converts the DateTime to a TZDateTime for proper time zone handling
It uses zonedSchedule() instead of show() to schedule the notification
It configures additional parameters for scheduling behavior

[T] The androidScheduleMode parameter is set to exactAllowWhileIdle, which ensures the notification is delivered at the exact time, even if the device is in doze mode.

[T] The uiLocalNotificationDateInterpretation parameter is set to absoluteTime, which means the notification will be scheduled for the exact time specified, regardless of the device's time zone.

[H2] Implementing Notification Cancellation
[T] We referenced a cancelNotification() method in the scheduleNotification() method. Let's implement it now:

[Code: dart]
/// Cancels a notification with the specified ID.
///
/// Removes the notification from the system and prevents it from being displayed.
/// If the notification is already displayed, it will be removed from the notification drawer.
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
  debugPrint('Cancelled notification: $id');
}

[T] This method simply cancels a notification with the specified ID, preventing it from being displayed or removing it if it's already displayed.

[T] Up Next: We'll enhance the NotificationBuilder class to make it easier to create scheduled notifications.


Next Chapter Title 
Enhancing the Notification Builder

[T] In this chapter, we'll enhance the NotificationBuilder class to make it easier to create scheduled notifications with a fluent API.

[H2] Adding Scheduling Methods to NotificationBuilder
[T] Open **lib/service/notification-manager/notification_builder.dart** and implement the scheduling methods:

[Code: dart]
/// Configures the notification to be scheduled for a specific time.
///
/// Changes the notification type to scheduled and sets the scheduled time.
///
/// [scheduledTime] The time when the notification should be displayed
///
/// Throws an [ArgumentError] if the scheduled time is in the past.
NotificationBuilder scheduleFor(DateTime scheduledTime) {
  if (scheduledTime.isBefore(DateTime.now())) {
    throw ArgumentError('Scheduled time cannot be in the past');
  }
  
  _model = _model.copyWith(
    type: NotificationType.scheduled,
    scheduledTime: scheduledTime,
  );
  
  return this;
}

/// Configures the notification to be scheduled for a specific date and time.
///
/// A convenience method that combines a date and time into a DateTime
/// and calls scheduleFor().
///
/// [date] The date when the notification should be displayed
/// [time] The time when the notification should be displayed
///
/// Throws an [ArgumentError] if the combined date and time is in the past.
NotificationBuilder scheduleForDateAndTime(DateTime date, TimeOfDay time) {
  final scheduledTime = DateTime(
    date.year,
    date.month,
    date.day,
    time.hour,
    time.minute,
  );
  
  return scheduleFor(scheduledTime);
}

[T] These methods make it easy to schedule notifications for a specific time or a combination of date and time. The scheduleFor method validates that the scheduled time is not in the past, and the scheduleForDateAndTime method combines a date and time into a DateTime and calls scheduleFor().

[H2] Using the Enhanced NotificationBuilder
[T] With these enhancements, we can now create scheduled notifications with a clean, fluent API:

[Code: dart]
// Create a notification scheduled for tomorrow at 9:00 AM
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: "Meeting Reminder",
  body: "Don't forget your team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.scheduleForDateAndTime(
  DateTime.now().add(Duration(days: 1)),
  TimeOfDay(hour: 9, minute: 0),
);

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for tomorrow at 9:00 AM with a clean, readable syntax.

[T] Up Next: We'll connect the UI to our scheduled notification implementation, allowing users to create scheduled notifications through the app interface.


Next Chapter Title 
Connecting the UI to Scheduled Notifications

[T] In this chapter, we'll connect the UI components for date and time selection to our notification system, allowing users to create scheduled notifications through the app interface.

[H2] Understanding the Create Notification Screen
[T] Open **lib/view/create-notification/create_notification_page.dart** and examine the CreateNotificationPage class. This screen includes:

[List]
Text fields for the notification title and body
A dropdown for selecting the notification channel
A dropdown for selecting the notification type
Date and time pickers for scheduled notifications
A dropdown for selecting the notification importance level
A button to create the notification

[T] When the user selects "Scheduled" as the notification type, the date and time pickers become visible.

[H2] Implementing the Date and Time Pickers
[T] The date and time pickers are already implemented in the UI, but we need to ensure they're properly connected to the notification creation logic.

[T] In the _createNotification method, we need to ensure that the selected date and time are used when creating a scheduled notification:

[Code: dart]
void _createNotification() {
  // Validate inputs
  if (_formKey.currentState?.validate() ?? false) {
    // Get form values
    final title = _titleController.text;
    final body = _bodyController.text;
    final channelId = _selectedChannel;
    final type = _selectedType;
    final level = _selectedLevel;
    
    // Create notification builder
    final builder = NotificationBuilder.create(
      NotificationManager(),
      id: DateTime.now().millisecondsSinceEpoch % 100000,
      title: title,
      body: body,
      channelId: channelId,
      level: level,
    );
    
    // Configure based on type
    switch (type) {
      case NotificationType.instant:
        // No additional configuration needed
        break;
        
      case NotificationType.scheduled:
        if (_selectedDate != null && _selectedTime != null) {
          builder.scheduleForDateAndTime(_selectedDate!, _selectedTime!);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a date and time for the scheduled notification'),
            ),
          );
          return;
        }
        break;
        
      case NotificationType.periodic:
        // We'll implement this in the next section
        break;
    }
    
    // Show the notification
    builder.show().then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification created successfully'),
        ),
      );
      
      // Navigate back to the home screen
      Navigator.pop(context);
    }).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create notification: $error'),
        ),
      );
    });
  }
}

[T] This method creates a notification builder with the basic properties, then configures it based on the selected notification type. For scheduled notifications, it uses the scheduleForDateAndTime method we added earlier.

[T] Up Next: We'll implement notification management features, including retrieving pending notifications and rescheduling them after device restart.


Next Chapter Title 
Managing Scheduled Notifications

[T] In this chapter, we'll implement features for managing scheduled notifications, including retrieving pending notifications and rescheduling them after device restart.

[H2] Retrieving Pending Notifications
[T] First, let's implement a method to get all pending notifications:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }
    
    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'Unknown',
      body: request.body ?? '',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      level: NotificationLevel.normal,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, which can be used to reschedule notifications.

[H2] Implementing Notification Rescheduling
[T] Next, let's implement a method to reschedule all notifications:

[Code: dart]
/// Reschedules all pending notifications.
///
/// Useful after device restart or app reinstallation to restore
/// scheduled notifications.
Future<void> rescheduleAllNotifications() async {
  final notifications = await getPendingNotifications();
  
  for (final notification in notifications) {
    if (notification.type == NotificationType.scheduled &&
        notification.scheduledTime != null) {
      await scheduleNotification(model: notification);
    } else if (notification.type == NotificationType.periodic &&
               notification.repeatInterval != null) {
      await schedulePeriodic(model: notification);
    }
  }
  
  debugPrint('Rescheduled ${notifications.length} notifications');
}

[T] This method gets all pending notifications and reschedules them based on their type.

[H2] Handling Device Restart
[T] To reschedule notifications after device restart, we need to listen for the BOOT_COMPLETED broadcast on Android. The flutter_local_notifications plugin handles this automatically, but we need to ensure our AndroidManifest.xml has the correct permissions and receivers.

[T] Open **android/app/src/main/AndroidManifest.xml** and add the following:

[Code: xml]
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

[T] This permission allows our app to receive the BOOT_COMPLETED broadcast.

[T] The flutter_local_notifications plugin includes a ScheduledNotificationBootReceiver that listens for the BOOT_COMPLETED broadcast and reschedules notifications. We don't need to implement this ourselves.

[H2] Implementing a Notification Details Screen
[T] Now that we can schedule notifications, let's implement a notification details screen that shows information about a specific notification.

[Code: dart]
/// Gets the details of a specific notification.
///
/// Returns a [NotificationModel] representing the notification with the specified ID,
/// or null if the notification is not found.
Future<NotificationModel?> getNotificationDetails(int id) async {
  final notifications = await getPendingNotifications();
  
  for (final notification in notifications) {
    if (notification.id == id) {
      return notification;
    }
  }
  
  return null;
}

[T] This method retrieves all pending notifications and returns the one with the specified ID, or null if not found.

[T] Up Next: We'll test our scheduled notifications implementation to ensure it works correctly across different scenarios.


Next Chapter Title 
Testing Scheduled Notifications

[T] In this chapter, we'll test our scheduled notifications implementation to ensure it works correctly across different scenarios.

[H2] Creating a Scheduled Notification
[T] Run the app and navigate to the "Create Notification" screen. Fill in the following details:

[List]
**Title**: "Scheduled Reminder"
**Body**: "This notification was scheduled for a future time"
**Channel**: "Default"
**Type**: "Scheduled"
**Date**: Select a date in the future
**Time**: Select a time a few minutes from now
**Level**: "Normal"

[T] Tap the "Create" button. The notification should be scheduled and appear at the specified time.

[Image]

[T] To verify that the notification is scheduled, you can check the debug output or navigate to the home screen, which should show the scheduled notification in the list.

[H2] Testing Time Zone Handling
[T] To test time zone handling, you can change your device's time zone and verify that the notification still appears at the correct local time.

[T] On Android:
1. Go to Settings > System > Date & time
2. Turn off "Automatic time zone"
3. Select a different time zone
4. Schedule a notification and verify it appears at the correct time

[T] On iOS:
1. Go to Settings > General > Date & Time
2. Turn off "Set Automatically"
3. Select a different time zone
4. Schedule a notification and verify it appears at the correct time

[T] Remember to set your device's time zone back to automatic when you're done testing.

[H2] Testing Notification Rescheduling
[T] To test notification rescheduling after device restart:

1. Schedule several notifications for different times
2. Restart your device
3. Open the app and verify that the scheduled notifications are still pending
4. Wait for the notifications to appear at their scheduled times

[T] This test ensures that our notification rescheduling mechanism works correctly.

[T] Up Next: Let's tackle a code challenge to deepen your understanding of scheduled notifications.


Next Chapter Title 
Code Challenge: Weekly Notifications

[T] In this chapter, we'll tackle a code challenge to implement weekly recurring notifications, which will deepen your understanding of scheduled notifications.

[H2] The Challenge
[T] Implement a method to schedule a notification for a specific day of the week and time (e.g., every Monday at 9:00 AM). You'll need to:

[List]
1. Add a method to the NotificationBuilder class to configure weekly scheduling
2. Modify the scheduleNotification method to handle weekly scheduling
3. Test the change by creating a weekly notification

[H2] Solution: Implementing Weekly Notifications
[T] First, let's add a method to the NotificationBuilder class to configure weekly scheduling:

[Code: dart]
/// Configures the notification to be scheduled for a specific day of the week and time.
///
/// Changes the notification type to scheduled and sets the scheduled time
/// to the next occurrence of the specified day and time.
///
/// [dayOfWeek] The day of the week (1 = Monday, 7 = Sunday)
/// [time] The time when the notification should be displayed
///
/// Returns the builder for method chaining.
NotificationBuilder scheduleWeekly(int dayOfWeek, TimeOfDay time) {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw ArgumentError('Day of week must be between 1 (Monday) and 7 (Sunday)');
  }
  
  // Get the current date and time
  final now = DateTime.now();
  
  // Calculate the next occurrence of the specified day and time
  final scheduledDateTime = _getNextDayOfWeek(dayOfWeek, time);
  
  // Configure the notification
  _model = _model.copyWith(
    type: NotificationType.scheduled,
    scheduledTime: scheduledDateTime,
    dayOfWeek: dayOfWeek,
    timeOfDay: time,
    matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime,
  );
  
  return this;
}

/// Calculates the next occurrence of the specified day of the week and time.
///
/// [dayOfWeek] The day of the week (1 = Monday, 7 = Sunday)
/// [time] The time of day
///
/// Returns a DateTime representing the next occurrence.
DateTime _getNextDayOfWeek(int dayOfWeek, TimeOfDay time) {
  // Get the current date and time
  final now = DateTime.now();
  
  // Convert to ISO day of week (1 = Monday, 7 = Sunday)
  final currentDayOfWeek = now.weekday;
  
  // Calculate days to add to get to the target day
  int daysToAdd = dayOfWeek - currentDayOfWeek;
  if (daysToAdd <= 0) {
    // If the target day is today or earlier in the week, go to next week
    daysToAdd += 7;
  }
  
  // If the target day is today, check if the time has already passed
  if (daysToAdd == 0) {
    final currentTimeMinutes = now.hour * 60 + now.minute;
    final targetTimeMinutes = time.hour * 60 + time.minute;
    
    if (targetTimeMinutes <= currentTimeMinutes) {
      // If the time has already passed today, go to next week
      daysToAdd += 7;
    }
  }
  
  // Calculate the target date
  final targetDate = now.add(Duration(days: daysToAdd));
  
  // Combine the target date with the target time
  return DateTime(
    targetDate.year,
    targetDate.month,
    targetDate.day,
    time.hour,
    time.minute,
  );
}

[T] Next, let's modify the scheduleNotification method to handle weekly scheduling:

[Code: dart]
/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot schedule notification: prerequisites not met');
    return;
  }

  if (model.scheduledTime == null) {
    throw ArgumentError(
      'scheduledTime must be provided for scheduled notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Convert DateTime to TZDateTime for proper timezone handling
  final scheduledDate = tz.TZDateTime.from(
    notificationWithDeepLink.scheduledTime!,
    tz.local,
  );

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // Determine if this is a weekly notification
  final DateTimeComponents? matchDateTimeComponents = 
      notificationWithDeepLink.dayOfWeek != null && notificationWithDeepLink.timeOfDay != null
          ? DateTimeComponents.dayOfWeekAndTime
          : null;

  // Schedule the notification
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    scheduledDate,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
    matchDateTimeComponents: matchDateTimeComponents,
    payload: notificationWithDeepLink.toPayload(),
  );

  debugPrint(
    'Scheduled notification: ${notificationWithDeepLink.id} for ${scheduledDate.toIso8601String()}' +
    (matchDateTimeComponents == DateTimeComponents.dayOfWeekAndTime
        ? ' (weekly on day ${notificationWithDeepLink.dayOfWeek})'
        : ''),
  );
}

[T] Now, let's test our implementation by creating a weekly notification:

[Code: dart]
// Create a notification scheduled for every Monday at 9:00 AM
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 456,
  title: "Weekly Team Meeting",
  body: "Don't forget your weekly team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.scheduleWeekly(
  1, // Monday
  TimeOfDay(hour: 9, minute: 0),
);

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for every Monday at 9:00 AM.

[T] Up Next: Let's summarize what we've learned in this section and look ahead to the next section.


Last Chapter Title 
Section Summary

[T] Let's summarize what we have covered in this section: 
[List] 
We explored the importance of time zone handling in scheduled notifications
We implemented the scheduleNotification method to schedule notifications for future delivery
We enhanced the NotificationBuilder with methods for scheduling notifications
We connected the UI date and time pickers to our notification system
We implemented notification management features, including retrieving and rescheduling notifications
We tackled a code challenge to implement weekly recurring notifications

[T] Key concepts to remember:
[List]
Always use TZDateTime for scheduling notifications to ensure proper time zone handling
Consider how notifications should behave when users change time zones
Use the matchDateTimeComponents parameter to create recurring notifications
Implement notification rescheduling to handle device restarts
Test your notifications with different time zones to ensure they behave correctly

[T] Code we implemented:
[List]
NotificationManager.scheduleNotification(): Schedules a notification for a future time
NotificationManager.cancelNotification(): Cancels a notification
NotificationManager.getPendingNotifications(): Gets all pending notifications
NotificationManager.rescheduleAllNotifications(): Reschedules all notifications
NotificationBuilder.scheduleFor(): Configures a notification for a specific time
NotificationBuilder.scheduleForDateAndTime(): Configures a notification for a specific date and time
NotificationBuilder.scheduleWeekly(): Configures a notification for a specific day of the week and time

[T] Up Next, we'll dive into implementing periodic notifications that repeat at regular intervals, allowing you to create notifications that occur daily, hourly, or at custom intervals. You'll learn how to configure different repeat patterns and handle notification updates.
