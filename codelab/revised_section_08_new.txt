[H1] Managing and Cancelling Notifications

[H2] Overview

[T] In this section, we'll implement features for managing and cancelling notifications. These features allow users to view, modify, and cancel their notifications when needed, providing a complete notification management experience.

[T] By the end of this section, you'll be able to retrieve pending notifications, cancel individual or all notifications, and update existing notifications.

[H3] Learning Objectives

[List]
Retrieve and display pending notifications
Cancel individual notifications
Cancel all notifications
Implement a notification details screen for managing notifications

[H2] Retrieving Pending Notifications

[T] The first step in managing notifications is to retrieve a list of pending notifications. Let's implement a method to get this information.

[H3] Implementing getPendingNotifications

[T] Open **lib/service/notification-manager/notification_manager.dart** and add the getPendingNotifications method:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }
    
    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'Unknown',
      body: request.body ?? '',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      level: NotificationLevel.normal,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, which can be used to display and manage notifications.

[H2] Cancelling Notifications

[T] Now that we can retrieve notifications, let's implement methods to cancel them.

[H3] Cancelling Individual Notifications

[T] Add the cancelNotification method to the NotificationManager class:

[Code: dart]
/// Cancels a specific notification by ID.
///
/// Removes both active and pending notifications with the specified [id].
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
}

[T] This method simply cancels a notification with the specified ID, preventing it from being displayed or removing it if it's already displayed.

[H3] Cancelling All Notifications

[T] Let's implement a method to cancel all notifications:

[Code: dart]
/// Cancels all active and scheduled notifications.
///
/// Removes all notifications created by the application.
Future<void> cancelAllNotifications() async {
  await _flutterLocalNotificationsPlugin.cancelAll();
}

[T] This method cancels all pending and active notifications, providing a way to clear all notifications at once.

[H2] Implementing the Notification Details Screen

[T] Now that we have methods for managing notifications, let's implement a notification details screen that allows users to view and cancel notifications.

[H3] Retrieving Notification Details

[T] First, let's implement a method to get the details of a specific notification:

[Code: dart]
/// Gets the details of a specific notification.
///
/// Returns a [NotificationModel] representing the notification with the specified ID,
/// or null if the notification is not found.
Future<NotificationModel?> getNotificationDetails(int id) async {
  final notifications = await getPendingNotifications();
  
  for (final notification in notifications) {
    if (notification.id == id) {
      return notification;
    }
  }
  
  return null;
}

[T] This method retrieves all pending notifications and returns the one with the specified ID, or null if not found.

[H3] Implementing the Notification Details Page

[T] Open **lib/view/notification-details/notification_details_page.dart** and implement the NotificationDetailsPage class:

[Code: dart]
class NotificationDetailsPage extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsPage({
    Key? key,
    required this.notificationId,
  }) : super(key: key);

  @override
  State<NotificationDetailsPage> createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  NotificationModel? _notification;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadNotificationDetails();
  }

  Future<void> _loadNotificationDetails() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final notification = await NotificationManager().getNotificationDetails(widget.notificationId);
      
      setState(() {
        _notification = notification;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load notification details: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _cancelNotification() async {
    if (_notification == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      await NotificationManager().cancelNotification(_notification!.id);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification cancelled successfully'),
        ),
      );
      
      // Navigate back to the home screen
      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _error = 'Failed to cancel notification: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
      ),
      body: _buildBody(),
      floatingActionButton: _notification != null
          ? FloatingActionButton(
              onPressed: _cancelNotification,
              tooltip: 'Cancel Notification',
              child: const Icon(Icons.delete),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Text(
          _error!,
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (_notification == null) {
      return const Center(
        child: Text('Notification not found'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _notification!.title,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _notification!.body,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          _buildNotificationDetails(),
        ],
      ),
    );
  }

  Widget _buildNotificationDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('ID', _notification!.id.toString()),
            _buildDetailRow('Channel', _notification!.channelId),
            _buildDetailRow('Type', _notification!.type.toString()),
            if (_notification!.scheduledTime != null)
              _buildDetailRow(
                'Scheduled Time',
                DateFormat('MMM d, yyyy HH:mm').format(_notification!.scheduledTime!),
              ),
            if (_notification!.repeatInterval != null)
              _buildDetailRow('Repeat Interval', _notification!.repeatInterval.toString()),
            _buildDetailRow('Priority', _notification!.level.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

[T] This screen displays the details of a notification and provides a button to cancel it.

[H2] Implementing the Home Screen

[T] The home screen should display a list of all pending notifications. Let's implement the _loadNotifications method in the HomePage class:

[Code: dart]
Future<void> _loadNotifications() async {
  setState(() {
    _isLoading = true;
  });
  
  try {
    final notifications = await NotificationManager().getPendingNotifications();
    
    setState(() {
      _notifications = notifications;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _error = 'Failed to load notifications: $e';
      _isLoading = false;
    });
  }
}

[T] This method loads all pending notifications and updates the UI accordingly.

[H3] Implementing the Notification List

[T] Now, let's implement the _buildNotificationList method to display the list of notifications:

[Code: dart]
Widget _buildNotificationList() {
  if (_notifications.isEmpty) {
    return const Center(
      child: Text('No pending notifications'),
    );
  }
  
  return ListView.builder(
    itemCount: _notifications.length,
    itemBuilder: (context, index) {
      final notification = _notifications[index];
      
      return ListTile(
        title: Text(notification.title),
        subtitle: Text(notification.body),
        trailing: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () async {
            await NotificationManager().cancelNotification(notification.id);
            
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notification cancelled'),
              ),
            );
            
            // Reload the notifications
            _loadNotifications();
          },
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            AppRoutes.notificationDetails.value,
            arguments: notification.id,
          ).then((_) {
            // Reload the notifications when returning from the details screen
            _loadNotifications();
          });
        },
      );
    },
  );
}

[T] This method builds a list of notification items, each with a tap handler to navigate to the details screen and a delete button to cancel the notification.

[H2] Testing Notification Management

[T] Now that we've implemented notification management features, let's test them by creating, viewing, and cancelling notifications.

[H3] Testing Notification Retrieval

[T] Run the app and create a few notifications with different types (instant, scheduled, periodic). Then, navigate to the home screen to see the list of pending notifications.

[T] Tap on a notification in the list to view its details.

[H3] Testing Notification Cancellation

[T] From the notification details screen, tap the "Cancel" button to cancel the notification. The notification should be removed from the system and you should be navigated back to the home screen.

[T] Alternatively, tap the delete button next to a notification in the list to cancel it directly from the home screen.

[H2] Best Practices for Notification Management

[T] When implementing notification management features, consider the following best practices:

[List]
**Provide Clear Feedback**: Show clear feedback when notifications are created, updated, or cancelled
**Handle Errors Gracefully**: Implement proper error handling for notification operations
**Refresh Data Automatically**: Reload notification data when returning from detail screens
**Confirm Destructive Actions**: Ask for confirmation before cancelling important notifications
**Show Notification Status**: Indicate whether notifications are pending, active, or expired

[H2] Summary

[T] In this section, we've implemented notification management features:

[List]
Retrieved and displayed pending notifications
Implemented methods to cancel individual and all notifications
Created a notification details screen for viewing and managing notifications
Implemented a home screen that displays a list of pending notifications
Added features for cancelling notifications from the UI

[T] With these features, our app now provides a complete notification management experience, allowing users to view and cancel their notifications as needed.

[H3] Code to Implement

[T] Here's a summary of the code we've implemented in this section:

[List]
**NotificationManager.getPendingNotifications()**: Gets all pending notifications
**NotificationManager.cancelNotification()**: Cancels a specific notification
**NotificationManager.cancelAllNotifications()**: Cancels all notifications
**NotificationManager.getNotificationDetails()**: Gets details of a specific notification
**NotificationDetailsPage._loadNotificationDetails()**: Loads notification details
**NotificationDetailsPage._cancelNotification()**: Cancels a notification
**HomePage._loadNotifications()**: Loads all pending notifications
**HomePage._buildNotificationList()**: Builds the notification list
