Section 5 eBook
Implementing Recurring and Periodic Notifications

Chapter Title
Section Overview

[T] In this section, you'll explore:
[List]
What recurring and periodic notifications are and how they differ
How to implement notifications that repeat at regular intervals
How to create daily, weekly, and monthly recurring notifications
How to handle platform differences between Android and iOS
How to manage and cancel recurring notifications

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!


Next Chapter Title
Understanding Recurring and Periodic Notifications

[T] In this chapter, we'll explore what recurring and periodic notifications are, their different types, and how they can be implemented in Flutter.

[H2] Types of Repeating Notifications

[T] There are two main approaches to implementing repeating notifications:

[List]
**Calendar-based (Recurring)**: Notifications that repeat based on calendar patterns (e.g., daily at 9:00 AM, weekly on Mondays)
**Fixed intervals (Periodic)**: Notifications that repeat at fixed time intervals (e.g., every 30 minutes, every 2 hours)

[T] The flutter_local_notifications package supports both approaches through different methods:

[List]
**zonedSchedule() with matchDateTimeComponents**: For calendar-based repetitions
**periodicallyShow()**: For fixed interval repetitions

[H2] Repeat Intervals

[T] The RepeatInterval enum in our app defines the supported repetition patterns:

[Code: dart]
enum RepeatInterval {
  everyMinute,
  hourly,
  daily,
  weekly,
  monthly,
  custom,
}

[T] Each interval corresponds to a different repetition pattern:

[List]
**everyMinute**: Repeats every minute (for testing purposes)
**hourly**: Repeats every hour at the same minute
**daily**: Repeats every day at the same time
**weekly**: Repeats every week on the same day and time
**monthly**: Repeats every month on the same day and time
**custom**: Repeats at a custom interval defined by periodDuration

[H2] Platform Differences

[T] It's important to note that Android and iOS handle periodic notifications differently:

[List]
**Android**: Provides direct support for periodic notifications through the periodicallyShow() method
**iOS**: Requires using zonedSchedule() with custom logic to simulate periodic behavior

[T] In our implementation, we'll handle these platform differences transparently, providing a consistent API for both platforms.

[T] Up Next, we'll implement the core functionality for periodic notifications in our NotificationManager class.


Next Chapter Title
Implementing Periodic Notifications

[T] In this chapter, we'll implement the schedulePeriodic() method in the NotificationManager class, which will handle both calendar-based and fixed-interval notifications.

[H2] The schedulePeriodic Method

[T] Let's implement the schedulePeriodic() method in the NotificationManager class. This method will schedule a notification to repeat at regular intervals.

[Code: dart]
/// Schedules a periodic notification that repeats at regular intervals.
///
/// Configures a notification to be delivered repeatedly according to the
/// [model.repeatInterval]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.repeatInterval] is null.
Future<void> schedulePeriodic({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot schedule periodic notification: prerequisites not met');
    return;
  }

  if (model.repeatInterval == null) {
    throw ArgumentError(
      'repeatInterval must be provided for periodic notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // Handle different repeat intervals
  switch (notificationWithDeepLink.repeatInterval!) {
    case RepeatInterval.everyMinute:
      await _scheduleFixedIntervalNotification(
        notificationWithDeepLink,
        details,
        const Duration(minutes: 1),
      );
      break;

    case RepeatInterval.hourly:
      await _scheduleFixedIntervalNotification(
        notificationWithDeepLink,
        details,
        const Duration(hours: 1),
      );
      break;

    case RepeatInterval.daily:
      await _scheduleCalendarBasedNotification(
        notificationWithDeepLink,
        details,
        DateTimeComponents.time,
      );
      break;

    case RepeatInterval.weekly:
      await _scheduleCalendarBasedNotification(
        notificationWithDeepLink,
        details,
        DateTimeComponents.dayOfWeekAndTime,
      );
      break;

    case RepeatInterval.monthly:
      await _scheduleCalendarBasedNotification(
        notificationWithDeepLink,
        details,
        DateTimeComponents.dayOfMonthAndTime,
      );
      break;

    case RepeatInterval.custom:
      if (notificationWithDeepLink.periodDuration == null) {
        throw ArgumentError(
          'periodDuration must be provided for custom repeat intervals',
        );
      }
      await _scheduleFixedIntervalNotification(
        notificationWithDeepLink,
        details,
        notificationWithDeepLink.periodDuration!,
      );
      break;
  }

  debugPrint(
    'Scheduled periodic notification: ${notificationWithDeepLink.id} '
    'with interval ${notificationWithDeepLink.repeatInterval}',
  );
}

[T] This method handles different repeat intervals by delegating to two helper methods:

[List]
**_scheduleFixedIntervalNotification()**: For fixed interval repetitions (everyMinute, hourly, custom)
**_scheduleCalendarBasedNotification()**: For calendar-based repetitions (daily, weekly, monthly)

[H2] Implementing Helper Methods

[T] Now, let's implement the helper methods that will handle the different types of periodic notifications:

[Code: dart]
/// Schedules a notification that repeats at fixed time intervals.
///
/// Uses the periodicallyShow method for simple interval-based repetition.
Future<void> _scheduleFixedIntervalNotification(
  NotificationModel model,
  NotificationDetails details,
  Duration interval,
) async {
  // For Android, we can use the periodicallyShow method
  if (Platform.isAndroid) {
    // Convert our app's RepeatInterval to the plugin's RepeatInterval
    final pluginRepeatInterval = _getPluginRepeatInterval(interval);

    await _flutterLocalNotificationsPlugin.periodicallyShow(
      model.id,
      model.title,
      model.body,
      pluginRepeatInterval,
      details,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: model.toPayload(),
    );
    return;
  }

  // For iOS, we need to use zonedSchedule with a custom check
  // to simulate periodicallyShow behavior
  tz.TZDateTime scheduledDate = _getNextInstanceOfInterval(interval);

  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    scheduledDate,
    details,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
    payload: model.toPayload(),
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    matchDateTimeComponents: null,
  );
}

/// Schedules a notification that repeats based on calendar components.
///
/// Uses zonedSchedule with matchDateTimeComponents for calendar-based repetition.
Future<void> _scheduleCalendarBasedNotification(
  NotificationModel model,
  NotificationDetails details,
  DateTimeComponents dateTimeComponents,
) async {
  tz.TZDateTime scheduledDate = _getNextInstanceOfTime(model);

  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    scheduledDate,
    details,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
    payload: model.toPayload(),
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    matchDateTimeComponents: dateTimeComponents,
  );
}

[T] These methods use different approaches to schedule periodic notifications:

[List]
**_scheduleFixedIntervalNotification()** uses periodicallyShow() on Android and simulates it on iOS
**_scheduleCalendarBasedNotification()** uses zonedSchedule() with matchDateTimeComponents to repeat based on calendar patterns

[T] Up Next, we'll implement helper methods to calculate the next occurrence of a notification and convert between our app's RepeatInterval and the plugin's RepeatInterval.


Next Chapter Title
Implementing Helper Methods for Time Calculations

[T] In this chapter, we'll implement helper methods to calculate the next occurrence of a notification and convert between different time formats.

[H2] Calculating the Next Occurrence

[T] Let's implement helper methods to calculate the next occurrence of a notification based on different criteria:

[Code: dart]
/// Calculates the next instance of a time-based notification.
///
/// Returns a TZDateTime representing the next occurrence of the notification
/// based on the model's time settings.
tz.TZDateTime _getNextInstanceOfTime(NotificationModel model) {
  final now = tz.TZDateTime.now(tz.local);

  // If timeOfDay is specified, use it; otherwise, use current time
  final timeOfDay = model.timeOfDay ?? TimeOfDay.fromDateTime(now);

  // Create a TZDateTime for today with the specified time
  tz.TZDateTime scheduledDate = tz.TZDateTime(
    tz.local,
    now.year,
    now.month,
    now.day,
    timeOfDay.hour,
    timeOfDay.minute,
  );

  // If the time is in the past, move to the next day
  if (scheduledDate.isBefore(now)) {
    scheduledDate = scheduledDate.add(const Duration(days: 1));
  }

  // If dayOfWeek is specified (for weekly notifications), adjust to that day
  if (model.repeatInterval == RepeatInterval.weekly && model.dayOfWeek != null) {
    // Convert from DateTime's weekday (1-7, starting with Monday)
    // to our dayOfWeek (0-6, starting with Sunday)
    final currentWeekday = scheduledDate.weekday % 7;
    final targetWeekday = model.dayOfWeek! % 7;

    // Calculate days to add to reach the target weekday
    final daysToAdd = (targetWeekday - currentWeekday) % 7;

    // Adjust the date
    scheduledDate = scheduledDate.add(Duration(days: daysToAdd));
  }

  return scheduledDate;
}

/// Calculates the next instance of an interval-based notification.
///
/// Returns a TZDateTime representing the next occurrence of the notification
/// based on the specified interval.
tz.TZDateTime _getNextInstanceOfInterval(Duration interval) {
  final now = tz.TZDateTime.now(tz.local);
  tz.TZDateTime scheduledDate = now;

  // Add the interval to the current time
  scheduledDate = scheduledDate.add(interval);

  return scheduledDate;
}

[T] These methods calculate the next occurrence of a notification based on different criteria:

[List]
**_getNextInstanceOfTime()** calculates the next occurrence based on a specific time of day and optionally a day of the week
**_getNextInstanceOfInterval()** calculates the next occurrence by adding a fixed interval to the current time

[H2] Converting Between RepeatInterval Types

[T] We also need to implement a helper method to convert between our app's RepeatInterval and the plugin's RepeatInterval:

[Code: dart]
/// Converts a Duration to the plugin's RepeatInterval.
///
/// Returns the closest matching RepeatInterval for the specified duration.
/// This is used for Android's periodicallyShow method.
RepeatInterval _getPluginRepeatInterval(Duration interval) {
  if (interval.inMinutes <= 1) {
    return RepeatInterval.everyMinute;
  } else if (interval.inHours <= 1) {
    return RepeatInterval.hourly;
  } else if (interval.inDays <= 1) {
    return RepeatInterval.daily;
  } else {
    return RepeatInterval.weekly;
  }
}

[T] This method converts a Duration to the plugin's RepeatInterval enum, which is used for Android's periodicallyShow method.

[H2] Understanding Time Zone Handling

[T] It's important to note that our implementation uses the tz package to handle time zones correctly. This ensures that notifications are scheduled at the correct time regardless of the user's time zone or daylight saving time changes.

[T] The tz.TZDateTime class is similar to the standard DateTime class but with time zone awareness. We use tz.local to get the user's local time zone, which ensures that notifications are scheduled in the user's local time.

[T] Up Next, we'll enhance the NotificationBuilder to make it easier to create recurring notifications.


Next Chapter Title
Enhancing the Notification Builder

[T] In this chapter, we'll enhance the NotificationBuilder to make it easier to create recurring notifications with a fluent API.

[H2] Adding Methods for Recurring Notifications

[T] Let's add methods to the NotificationBuilder class to make it easier to create different types of recurring notifications:

[Code: dart]
/// Configures the notification to repeat daily at a specific time.
///
/// Changes the notification type to periodic and sets the repeat interval to daily.
///
/// [time] The time of day when the notification should be displayed
NotificationBuilder repeatDaily(TimeOfDay time) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: RepeatInterval.daily,
    timeOfDay: time,
  );

  return this;
}

/// Configures the notification to repeat weekly on a specific day and time.
///
/// Changes the notification type to periodic and sets the repeat interval to weekly.
///
/// [dayOfWeek] The day of the week (0-6, where 0 is Sunday)
/// [time] The time of day when the notification should be displayed
///
/// Throws an [ArgumentError] if dayOfWeek is not between 0 and 6.
NotificationBuilder repeatWeekly(int dayOfWeek, TimeOfDay time) {
  if (dayOfWeek < 0 || dayOfWeek > 6) {
    throw ArgumentError('dayOfWeek must be between 0 and 6');
  }

  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: RepeatInterval.weekly,
    dayOfWeek: dayOfWeek,
    timeOfDay: time,
  );

  return this;
}

/// Configures the notification to repeat monthly on a specific day and time.
///
/// Changes the notification type to periodic and sets the repeat interval to monthly.
///
/// [time] The time of day when the notification should be displayed
NotificationBuilder repeatMonthly(TimeOfDay time) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: RepeatInterval.monthly,
    timeOfDay: time,
  );

  return this;
}

/// Configures the notification to repeat at a custom interval.
///
/// Changes the notification type to periodic and sets the repeat interval to custom.
///
/// [interval] The duration between repetitions
///
/// Throws an [ArgumentError] if interval is less than 15 minutes.
NotificationBuilder repeatEvery(Duration interval) {
  if (interval < const Duration(minutes: 15)) {
    throw ArgumentError('Interval must be at least 15 minutes');
  }

  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: RepeatInterval.custom,
    periodDuration: interval,
  );

  return this;
}

[T] These methods make it easy to create different types of periodic notifications:

[List]
**repeatDaily()** configures a notification to repeat every day at a specific time
**repeatWeekly()** configures a notification to repeat every week on a specific day and time
**repeatMonthly()** configures a notification to repeat every month on the same day and time
**repeatEvery()** configures a notification to repeat at a custom interval

[H2] Using the Enhanced NotificationBuilder

[T] With these enhancements, creating recurring notifications becomes much simpler. Here are some examples:

[Code: dart]
// Create a daily reminder at 9:00 AM
await NotificationBuilder.create(
  notificationManager,
  id: 1,
  title: 'Daily Reminder',
  body: 'This is your daily reminder',
  channelId: NotificationChannelIds.personal,
)
.repeatDaily(const TimeOfDay(hour: 9, minute: 0))
.show();

// Create a weekly reminder on Monday at 10:00 AM
await NotificationBuilder.create(
  notificationManager,
  id: 2,
  title: 'Weekly Meeting',
  body: 'Team meeting',
  channelId: NotificationChannelIds.work,
)
.repeatWeekly(1, const TimeOfDay(hour: 10, minute: 0)) // 1 = Monday
.show();

// Create a custom reminder every 2 hours
await NotificationBuilder.create(
  notificationManager,
  id: 3,
  title: 'Drink Water',
  body: 'Stay hydrated!',
  channelId: NotificationChannelIds.health,
)
.repeatEvery(const Duration(hours: 2))
.show();

[T] This fluent API makes it easy to create recurring notifications with just a few lines of code, hiding the complexity of the underlying implementation.

[T] Up Next, we'll update the UI to allow users to select repetition options when creating notifications.


Next Chapter Title
Updating the UI for Periodic Notifications

[T] In this chapter, we'll update the UI to allow users to select repetition options when creating notifications.

[H2] Understanding the UI Components

[T] The starter project already includes UI components for repetition options, but they're not fully implemented. Let's examine how they work.

[T] Open **lib/view/create-notification/create_notification_page.dart** and examine the CreateNotificationPage class. This screen includes:

[List]
A dropdown for selecting the notification type (instant, scheduled, periodic)
A dropdown for selecting the repetition interval (daily, weekly, etc.)
A dropdown for selecting the day of the week (for weekly notifications)
A time picker for selecting the time of day (for daily and weekly notifications)
Text fields for entering a custom interval (for custom notifications)

[T] When the user selects "Periodic" as the notification type, these repetition options become visible.

[H2] Implementing the Repetition Options UI

[T] Let's update the _buildRepeatIntervalDropdown method to display the appropriate options based on the selected repeat interval:

[Code: dart]
Widget _buildRepeatIntervalDropdown() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('Repeat Interval'),
      DropdownButtonFormField<RepeatInterval>(
        value: _selectedRepeatInterval,
        items: RepeatInterval.values.map((interval) {
          return DropdownMenuItem<RepeatInterval>(
            value: interval,
            child: Text(interval.toString().split('.').last),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedRepeatInterval = value;
          });
        },
        decoration: const InputDecoration(
          border: OutlineInputBorder(),
        ),
      ),
      const SizedBox(height: 16),

      // Show day of week selector for weekly notifications
      if (_selectedRepeatInterval == RepeatInterval.weekly)
        _buildDayOfWeekDropdown(),

      // Show time picker for daily, weekly, and monthly notifications
      if (_selectedRepeatInterval == RepeatInterval.daily ||
          _selectedRepeatInterval == RepeatInterval.weekly ||
          _selectedRepeatInterval == RepeatInterval.monthly)
        _buildTimePicker(),

      // Show custom interval fields for custom notifications
      if (_selectedRepeatInterval == RepeatInterval.custom)
        _buildCustomIntervalFields(),
    ],
  );
}

[T] This method displays different UI components based on the selected repeat interval:

[List]
For weekly notifications, it shows a day of week dropdown
For daily, weekly, and monthly notifications, it shows a time picker
For custom notifications, it shows fields for entering hours and minutes

[H2] Implementing the Day of Week Dropdown

[T] Let's implement the _buildDayOfWeekDropdown method:

[Code: dart]
Widget _buildDayOfWeekDropdown() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('Day of Week'),
      DropdownButtonFormField<int>(
        value: _selectedDayOfWeek,
        items: [
          const DropdownMenuItem<int>(value: 0, child: Text('Sunday')),
          const DropdownMenuItem<int>(value: 1, child: Text('Monday')),
          const DropdownMenuItem<int>(value: 2, child: Text('Tuesday')),
          const DropdownMenuItem<int>(value: 3, child: Text('Wednesday')),
          const DropdownMenuItem<int>(value: 4, child: Text('Thursday')),
          const DropdownMenuItem<int>(value: 5, child: Text('Friday')),
          const DropdownMenuItem<int>(value: 6, child: Text('Saturday')),
        ],
        onChanged: (value) {
          setState(() {
            _selectedDayOfWeek = value;
          });
        },
        decoration: const InputDecoration(
          border: OutlineInputBorder(),
        ),
      ),
      const SizedBox(height: 16),
    ],
  );
}

[T] This method displays a dropdown for selecting the day of the week, which is used for weekly notifications.

[H2] Implementing the Time Picker

[T] Let's implement the _buildTimePicker method:

[Code: dart]
Widget _buildTimePicker() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('Time'),
      InkWell(
        onTap: () async {
          final TimeOfDay? time = await showTimePicker(
            context: context,
            initialTime: _selectedTime ?? TimeOfDay.now(),
          );
          if (time != null) {
            setState(() {
              _selectedTime = time;
            });
          }
        },
        child: InputDecorator(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          child: Text(
            _selectedTime != null
                ? '${_selectedTime!.hour}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                : 'Select Time',
          ),
        ),
      ),
      const SizedBox(height: 16),
    ],
  );
}

[T] This method displays a time picker for selecting the time of day, which is used for daily, weekly, and monthly notifications.

[H2] Implementing the Custom Interval Fields

[T] Let's implement the _buildCustomIntervalFields method:

[Code: dart]
Widget _buildCustomIntervalFields() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('Custom Interval'),
      Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Hours',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setState(() {
                  _customIntervalHours = value;
                });
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Minutes',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setState(() {
                  _customIntervalMinutes = value;
                });
              },
            ),
          ),
        ],
      ),
      const SizedBox(height: 16),
    ],
  );
}

[T] This method displays fields for entering the hours and minutes for a custom interval, which is used for custom notifications.

[H2] Updating the Create Notification Method

[T] Now that we have the UI components in place, let's update the _createNotification method to use the selected repetition options:

[Code: dart]
void _createNotification() {
  // Validate inputs
  if (_formKey.currentState?.validate() ?? false) {
    // Get form values
    final title = _titleController.text;
    final body = _bodyController.text;
    final channelId = _selectedChannel;
    final type = _selectedType;
    final level = _selectedLevel;

    // Create notification builder
    final builder = NotificationBuilder.create(
      NotificationManager(),
      id: DateTime.now().millisecondsSinceEpoch % 100000,
      title: title,
      body: body,
      channelId: channelId,
      level: level,
    );

    // Configure based on type
    switch (type) {
      case NotificationType.instant:
        // No additional configuration needed
        break;

      case NotificationType.scheduled:
        if (_selectedDate != null && _selectedTime != null) {
          builder.scheduleForDateAndTime(_selectedDate!, _selectedTime!);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a date and time for the scheduled notification'),
            ),
          );
          return;
        }
        break;

      case NotificationType.periodic:
        switch (_selectedRepeatInterval) {
          case RepeatInterval.daily:
            if (_selectedTime != null) {
              builder.repeatDaily(_selectedTime!);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select a time for the daily notification'),
                ),
              );
              return;
            }
            break;

          case RepeatInterval.weekly:
            if (_selectedDayOfWeek != null && _selectedTime != null) {
              builder.repeatWeekly(_selectedDayOfWeek!, _selectedTime!);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select a day and time for the weekly notification'),
                ),
              );
              return;
            }
            break;

          case RepeatInterval.monthly:
            if (_selectedTime != null) {
              builder.repeatMonthly(_selectedTime!);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select a time for the monthly notification'),
                ),
              );
              return;
            }
            break;

          case RepeatInterval.custom:
            if (_customIntervalHours != null && _customIntervalMinutes != null) {
              final hours = int.tryParse(_customIntervalHours!) ?? 0;
              final minutes = int.tryParse(_customIntervalMinutes!) ?? 0;

              if (hours <= 0 && minutes < 15) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Custom interval must be at least 15 minutes'),
                  ),
                );
                return;
              }

              final interval = Duration(
                hours: hours,
                minutes: minutes,
              );

              builder.repeatEvery(interval);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter a custom interval'),
                ),
              );
              return;
            }
            break;

          default:
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Please select a repeat interval'),
              ),
            );
            return;
        }
        break;
    }

    // Show the notification
    builder.show().then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification created successfully'),
        ),
      );

      // Navigate back to the home screen
      Navigator.pop(context);
    }).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create notification: $error'),
        ),
      );
    });
  }
}

[T] This method creates a notification builder with the basic properties, then configures it based on the selected notification type and repetition options.

[T] Up Next, we'll test our implementation by creating different types of periodic notifications.


Next Chapter Title
Testing Periodic Notifications

[T] In this chapter, we'll test our implementation by creating different types of periodic notifications.

[H2] Testing Daily Notifications

[T] Let's test our implementation by creating a daily notification:

[List]
1. Run the app and navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Daily Reminder"
   - **Body**: "This notification repeats every day"
   - **Channel**: "Default"
   - **Type**: "Periodic"
   - **Repeat**: "Daily"
   - **Time**: Select a time a few minutes from now
   - **Level**: "Normal"
3. Tap the "Create" button

[T] The notification should appear at the specified time and repeat every day at the same time.

[Image]

[H2] Testing Weekly Notifications

[T] Now, let's create a weekly notification:

[List]
1. Navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Weekly Reminder"
   - **Body**: "This notification repeats every week"
   - **Channel**: "Default"
   - **Type**: "Periodic"
   - **Repeat**: "Weekly"
   - **Day**: Select a day of the week
   - **Time**: Select a time
   - **Level**: "Normal"
3. Tap the "Create" button

[T] The notification should appear on the specified day and time and repeat every week.

[H2] Testing Custom Interval Notifications

[T] For testing purposes, let's create a notification that repeats every 15 minutes:

[List]
1. Navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Custom Interval"
   - **Body**: "This notification repeats every 15 minutes"
   - **Channel**: "Default"
   - **Type**: "Periodic"
   - **Repeat**: "Custom"
   - **Interval**: 0 hours, 15 minutes
   - **Level**: "Normal"
3. Tap the "Create" button

[T] This notification should repeat every 15 minutes, allowing you to quickly verify that periodic notifications are working correctly.

[Emphasis, bulb] In a real app, you would use more reasonable intervals like hourly or custom durations of several hours or days. High-frequency notifications can drain the battery and annoy users.

[H2] Verifying Notifications

[T] To verify that your periodic notifications are working correctly, you can:

[List]
1. Check the notification drawer to see if the notification appears at the scheduled time
2. Wait for the next occurrence to verify that the notification repeats
3. Check the system logs to see if the notification is scheduled correctly

[T] On Android, you can also use the notification settings to see the scheduled notifications:

[List]
1. Open the system settings
2. Navigate to "Apps & notifications" > "See all apps" > "Timora" > "Notifications"
3. You should see the notification channels and any active notifications

[T] Up Next, we'll implement methods to manage periodic notifications, including cancelling and updating them.


Next Chapter Title
Managing Periodic Notifications

[T] In this chapter, we'll implement methods to manage periodic notifications, including cancelling and updating them.

[H2] Cancelling Periodic Notifications

[T] Periodic notifications continue to repeat until they're explicitly cancelled. We've already implemented the cancelNotification() method, which works for all types of notifications, including periodic ones:

[Code: dart]
/// Cancels a notification with the specified ID.
///
/// Removes the notification from the system and prevents it from being displayed.
/// If the notification is already displayed, it will be removed from the notification drawer.
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
  debugPrint('Cancelled notification: $id');
}

[T] To cancel a periodic notification, simply call this method with the notification's ID:

[Code: dart]
// Cancel a notification with ID 123
await notificationManager.cancelNotification(123);

[T] This will stop the notification from repeating and remove it from the notification drawer if it's currently displayed.

[H2] Updating Periodic Notifications

[T] To update a periodic notification, you can cancel the existing notification and schedule a new one with the same ID but different properties:

[Code: dart]
/// Updates an existing notification with new properties.
///
/// Cancels the existing notification and schedules a new one with the same ID.
Future<void> updateNotification(NotificationModel model) async {
  await cancelNotification(model.id);

  switch (model.type) {
    case NotificationType.instant:
      await showInstantNotification(model: model);
      break;

    case NotificationType.scheduled:
      await scheduleNotification(model: model);
      break;

    case NotificationType.periodic:
      await schedulePeriodic(model: model);
      break;
  }

  debugPrint('Updated notification: ${model.id}');
}

[T] This method cancels the existing notification and schedules a new one based on the notification type. For example, to update a daily notification to a weekly notification:

[Code: dart]
// Get the existing notification model
final existingModel = await notificationManager.getNotification(123);

// Update the properties
final updatedModel = existingModel.copyWith(
  repeatInterval: RepeatInterval.weekly,
  dayOfWeek: 1, // Monday
  timeOfDay: const TimeOfDay(hour: 10, minute: 0),
);

// Update the notification
await notificationManager.updateNotification(updatedModel);

[T] This will cancel the existing daily notification and schedule a new weekly notification with the same ID.

[H2] Implementing the Notification Details Page

[T] To make it easier for users to manage their notifications, let's implement a notification details page that allows them to view, update, and cancel notifications.

[T] The starter project already includes a NotificationDetailsPage class, but it's not fully implemented. Let's update it to support periodic notifications:

[Code: dart]
class NotificationDetailsPage extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsPage({
    Key? key,
    required this.notificationId,
  }) : super(key: key);

  @override
  _NotificationDetailsPageState createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  late Future<NotificationModel?> _notificationFuture;
  final _notificationManager = NotificationManager();

  @override
  void initState() {
    super.initState();
    _notificationFuture = _loadNotification();
  }

  Future<NotificationModel?> _loadNotification() async {
    return await _notificationManager.getNotification(widget.notificationId);
  }

  Future<void> _cancelNotification(NotificationModel model) async {
    await _notificationManager.cancelNotification(model.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification cancelled'),
      ),
    );
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
      ),
      body: FutureBuilder<NotificationModel?>(
        future: _notificationFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          final notification = snapshot.data;
          if (notification == null) {
            return const Center(
              child: Text('Notification not found'),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  notification.title,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(notification.body),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                _buildNotificationDetails(notification),
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () => _cancelNotification(notification),
                      child: const Text('Cancel Notification'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Navigate to edit page
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditNotificationPage(
                              notification: notification,
                            ),
                          ),
                        ).then((_) {
                          // Refresh the notification details
                          setState(() {
                            _notificationFuture = _loadNotification();
                          });
                        });
                      },
                      child: const Text('Edit Notification'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNotificationDetails(NotificationModel notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type: ${notification.type.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Channel: ${notification.channelId}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Level: ${notification.level.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (notification.type == NotificationType.scheduled)
          Text(
            'Scheduled for: ${DateFormat('MMM d, yyyy HH:mm').format(notification.scheduledDate!)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (notification.type == NotificationType.periodic)
          _buildPeriodicDetails(notification),
      ],
    );
  }

  Widget _buildPeriodicDetails(NotificationModel notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Repeat Interval: ${notification.repeatInterval.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (notification.repeatInterval == RepeatInterval.daily ||
            notification.repeatInterval == RepeatInterval.weekly ||
            notification.repeatInterval == RepeatInterval.monthly)
          Text(
            'Time: ${notification.timeOfDay!.hour}:${notification.timeOfDay!.minute.toString().padLeft(2, '0')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (notification.repeatInterval == RepeatInterval.weekly &&
            notification.dayOfWeek != null)
          Text(
            'Day of Week: ${_getDayOfWeekName(notification.dayOfWeek!)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (notification.repeatInterval == RepeatInterval.custom &&
            notification.periodDuration != null)
          Text(
            'Interval: ${_formatDuration(notification.periodDuration!)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
      ],
    );
  }

  String _getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 0:
        return 'Sunday';
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      default:
        return 'Unknown';
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '$hours hours, $minutes minutes';
    } else {
      return '$minutes minutes';
    }
  }
}

[T] This page displays the details of a notification, including its type, channel, level, and repetition settings. It also provides buttons to cancel or edit the notification.

[T] Up Next, we'll tackle a code challenge to test your understanding of periodic notifications.


Next Chapter Title
Code Challenge: Limiting Notification Occurrences

[T] In this chapter, you'll get a chance to practice what you've learned with a code challenge focused on limiting the number of times a periodic notification repeats.

[H2] The Challenge

[T] Sometimes you might want to limit the number of times a periodic notification repeats. For example, a "Take medication" reminder might repeat for 7 days and then stop.

[T] Your challenge is to implement a feature to limit the number of occurrences for a periodic notification. You'll need to:

[List]
1. Add an occurrenceCount property to the NotificationModel class
2. Implement a limitOccurrences() method in the NotificationBuilder class
3. Store the current occurrence count in shared preferences
4. Increment the count each time the notification is displayed
5. Cancel the notification when the count reaches the limit

[T] Take some time to implement this challenge before looking at the solution.

[H2] Solution

[T] Let's go through the solution to the challenge:

[T] First, let's add the limitOccurrences() method to the NotificationBuilder class:

[Code: dart]
/// Configures the notification to repeat a limited number of times.
///
/// Sets the maximum number of occurrences for a periodic notification.
///
/// [count] The number of times the notification should be displayed
///
/// Throws an [ArgumentError] if count is less than 1.
NotificationBuilder limitOccurrences(int count) {
  if (count < 1) {
    throw ArgumentError('Occurrence count must be at least 1');
  }

  _model = _model.copyWith(occurrenceCount: count);
  return this;
}

[T] Next, we need to track the current occurrence count. Let's add a method to the NotificationManager class to increment the count and check if the limit has been reached:

[Code: dart]
/// Increments the occurrence count for a notification and checks if the limit has been reached.
///
/// Returns true if the notification should continue to be displayed,
/// or false if it has reached its occurrence limit and should be cancelled.
Future<bool> _incrementOccurrenceCount(int id) async {
  final prefs = await SharedPreferences.getInstance();
  final key = 'notification_occurrence_$id';

  // Get the current count
  final currentCount = prefs.getInt(key) ?? 0;

  // Get the notification model
  final model = await getNotification(id);
  if (model == null) {
    return true; // No model found, continue displaying
  }

  // If there's no limit, continue displaying
  if (model.occurrenceCount == null || model.occurrenceCount! <= 0) {
    return true;
  }

  // Increment the count
  final newCount = currentCount + 1;
  await prefs.setInt(key, newCount);

  // Check if the limit has been reached
  if (newCount >= model.occurrenceCount!) {
    // Cancel the notification
    await cancelNotification(id);
    return false;
  }

  return true;
}

[T] Now, we need to call this method when a notification is displayed. We can do this by updating the onDidReceiveNotificationResponse callback in the initialize() method:

[Code: dart]
await _flutterLocalNotificationsPlugin.initialize(
  initSettings,
  onDidReceiveNotificationResponse: (NotificationResponse response) async {
    // Increment the occurrence count for periodic notifications
    if (response.payload != null) {
      final model = NotificationModel.fromPayload(response.payload!);
      if (model.type == NotificationType.periodic) {
        await _incrementOccurrenceCount(model.id);
      }

      // Handle the notification tap
      _handleNotificationTap(response);
    }
  },
);

[T] Finally, let's update the UI to allow users to set a limit when creating a periodic notification:

[Code: dart]
Widget _buildOccurrenceLimitField() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('Occurrence Limit (optional)'),
      TextFormField(
        decoration: const InputDecoration(
          labelText: 'Number of occurrences',
          border: OutlineInputBorder(),
          hintText: 'Leave empty for unlimited',
        ),
        keyboardType: TextInputType.number,
        onChanged: (value) {
          setState(() {
            _occurrenceLimit = int.tryParse(value);
          });
        },
      ),
      const SizedBox(height: 16),
    ],
  );
}

[T] And update the _createNotification method to use this limit:

[Code: dart]
// If occurrence limit is set, apply it
if (_occurrenceLimit != null && _occurrenceLimit! > 0) {
  builder.limitOccurrences(_occurrenceLimit!);
}

[T] With these changes, users can now create periodic notifications that repeat a limited number of times. For example, a medication reminder that repeats for 7 days and then stops.

[T] This feature is particularly useful for reminders that should only occur for a specific period, such as a course of medication or a temporary event.

[T] Up Next, we'll summarize what we've learned in this section.


Last Chapter Title
Section Summary

[T] Let's summarize what we have covered in this section:
[List]
We learned about recurring and periodic notifications and their different types
We implemented the schedulePeriodic() method to handle both calendar-based and fixed-interval notifications
We created helper methods to calculate the next occurrence of a notification
We enhanced the NotificationBuilder with methods for different repetition patterns
We updated the UI to allow users to select repetition options
We implemented methods to manage periodic notifications, including cancelling and updating them
We created a notification details page to view and manage notifications
We tackled a code challenge to limit the number of occurrences for a periodic notification

[T] You now have a solid understanding of how to implement recurring and periodic notifications in Flutter. Your app can now deliver notifications that repeat at regular intervals, with full control over the repetition pattern.

[T] The periodic notifications we've implemented provide a powerful way to keep users engaged with your app by delivering timely reminders and updates. Whether it's a daily medication reminder, a weekly meeting notification, or a custom interval alert, you now have the tools to create notifications that meet your users' needs.

[T] Up Next, we'll dive into customizing notification appearance and behavior, where you'll learn how to create rich notifications with images, actions, and more.
