[H1] Handling and Managing Notifications

[H2] Overview

[T] In this section, we'll implement comprehensive notification handling features, including notification click handling, deep linking, and notification management. These features allow users to interact with notifications, navigate to specific screens, and manage their notifications effectively.

[T] By the end of this section, you'll be able to handle notification taps, implement deep links, retrieve pending notifications, and cancel notifications when needed.

[H3] Learning Objectives

[List]
Understand deep links and their role in notifications
Implement notification tap handling to navigate to specific screens
Set up deep links using the app_links package
Retrieve and display pending notifications
Cancel individual or all notifications
Implement a notification details screen for managing notifications

[H2] Understanding Deep Links

[H3] What are Deep Links?

[T] Deep links are URLs that navigate to specific content within your app. In the context of notifications, deep links allow users to tap a notification and be taken directly to the relevant screen, rather than just opening the app to the home screen.

[T] In our app, we'll use the format `timora:/notification-details?id=123` to navigate to a specific notification's details screen.

[H2] Implementing Deep Link Handling

[T] Our app already has a DeepLinkHandler class that handles deep links. Let's examine how it works and implement the notification deep link handling.

[T] Open **lib/core/util/deeplink/deeplink_handler.dart** and examine the class:

[Code: dart]
/// Handles deep link navigation within the app
///
/// This handler supports two types of deeplinks with a centralized processing approach:
/// 1. External deeplinks - from browsers, other apps, etc. (using app_links package)
/// 2. Notification deeplinks - from tapping on notifications
class DeepLinkHandler {
  // Private constructor for singleton pattern
  DeepLinkHandler._();

  // Singleton instance
  static final DeepLinkHandler instance = DeepLinkHandler._();

  // App links instance for handling external deep links
  final AppLinks _appLinks = AppLinks();

  /// Initialize deep link handling - should be called on app startup
  void init() {
    _handleInitialLink();
    _listenForLinks();
  }

  /// Sets up a listener for incoming external deep links while the app is running
  void _listenForLinks() {
    _appLinks.uriLinkStream.listen(
      (Uri? uri) {
        if (uri != null) {
          _handleDeepLink(uri);
        }
      },
      onError: (err) {
        debugPrint('Error handling external deep link: $err');
      },
    );
  }

  /// Handle initial deep link when app is launched from an external link
  Future<void> _handleInitialLink() async {
    try {
      final Uri? initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        debugPrint('App launched from deeplink: $initialUri');
        _handleDeepLink(initialUri);
      }
    } catch (e) {
      debugPrint('Error handling initial deep link: $e');
    }
  }
}

[T] Now, let's implement the methods to handle notification deep links:

[Code: dart]
/// Handle a deeplink from a notification payload
///
/// This is a public method that can be called directly when a notification is tapped.
/// It takes a deeplink URI string from the notification payload and processes it.
void handleNotificationDeeplink(String? deeplinkUri) {
  if (deeplinkUri == null) return;

  try {
    final uri = Uri.parse(deeplinkUri);
    debugPrint('Processing notification deeplink: $uri');
    // Use the shared processing logic
    processDeepLink(uri);
  } catch (e) {
    debugPrint('Error parsing notification deeplink URI: $e');
    // Navigate to home as fallback
    AppRouter.navigateTo('/');
  }
}

/// Process the deep link from external sources
void _handleDeepLink(Uri uri) {
  debugPrint('Processing external deeplink: $uri');
  // Use the shared processing logic
  processDeepLink(uri);
}

/// Shared processing logic for all deeplinks
void processDeepLink(Uri uri) {
  try {
    final DeepLinkData linkData = _parseDeepLink(uri);
    debugPrint(
      'Navigating to: ${linkData.path} with arguments: ${linkData.arguments}',
    );

    // Use AppRouter's navigation method
    AppRouter.navigateTo(linkData.path, arguments: linkData.arguments);
  } catch (e) {
    debugPrint('Error processing deeplink: $e');
    // Navigate to home as fallback
    AppRouter.navigateTo('/');
  }
}

/// Parse a deep link URI into path and arguments
DeepLinkData _parseDeepLink(Uri uri) {
  // Extract the path from the URI
  String path = uri.path;
  if (path.isEmpty) {
    path = '/';
  }

  // Default arguments is null
  dynamic arguments;

  // Handle notification details deeplink
  if (path == AppRoutes.notificationDetails.value) {
    final notificationId = NotificationDeepLinkUtil.extractNotificationId(uri);
    if (notificationId != null) {
      // Replace the arguments with just the ID as an integer
      arguments = notificationId;
    }
  }

  return DeepLinkData(path: path, arguments: arguments);
}

[H3] Creating Deep Links for Notifications

[T] Now, let's examine the NotificationDeepLinkUtil class, which generates deep links for notifications:

[Code: dart]
/// Utility class for generating and parsing notification deeplinks
class NotificationDeepLinkUtil {
  // Private constructor for utility class
  NotificationDeepLinkUtil._();

  /// The scheme used for deeplinks in the app
  static const String scheme = 'timora';

  /// Generates a deeplink for a notification details page
  static String generateNotificationDetailsDeepLink(int notificationId) {
    return '$scheme:${AppRoutes.notificationDetails.value}?id=$notificationId';
  }

  /// Extracts a notification ID from a deeplink URI
  static int? extractNotificationId(Uri uri) {
    if (uri.path == AppRoutes.notificationDetails.value) {
      final idParam = uri.queryParameters['id'];
      if (idParam != null) {
        return int.tryParse(idParam);
      }
    }
    return null;
  }
}

[T] This class provides methods to generate deep links for notification details pages and extract notification IDs from deep link URIs.

[H2] Handling Notification Taps

[T] When a user taps a notification, we want to navigate to the appropriate screen based on the deep link in the notification payload. Let's implement the notification tap handler in the NotificationManager class:

[Code: dart]
/// Callback handler for notification responses.
void _onNotificationResponse(NotificationResponse response) {
  debugPrint('Notification tapped: ${response.payload}');

  // If there's a payload, try to parse it and handle the notification
  if (response.payload != null) {
    try {
      final notificationModel = NotificationModel.fromPayload(response.payload!);

      // If the notification has a deeplink, use it
      if (notificationModel.deepLink != null) {
        debugPrint(
          'Notification tapped with deeplink: ${notificationModel.deepLink}',
        );
        // Use the DeepLinkHandler to process the deeplink
        DeepLinkHandler.instance.handleNotificationDeeplink(
          notificationModel.deepLink,
        );
      } else {
        // Fallback to direct navigation if no deeplink is provided
        debugPrint(
          'Notification tapped without deeplink, using ID: ${notificationModel.id}',
        );
        AppRouter.navigateTo(
          AppRoutes.notificationDetails.value,
          arguments: notificationModel.id,
        );
      }
    } catch (e) {
      debugPrint('Error processing notification payload: $e');
    }
  }
}

[T] This method extracts the deep link from the notification payload and passes it to the DeepLinkHandler, which will navigate to the appropriate screen.

[H3] Adding Deep Links to Notifications

[T] To add a deep link to a notification, we need to modify the NotificationBuilder class:

[Code: dart]
/// Configures the notification with a custom deep link.
///
/// Sets a custom URI that will be used for navigation when the notification is tapped.
///
/// [deepLink] The deep link URI string
NotificationBuilder withDeepLink(String deepLink) {
  _model = _model.copyWith(deepLink: deepLink);
  return this;
}

[T] We also need to ensure that all notifications have a deep link by default. Let's modify the showInstantNotification method in the NotificationManager class:

[Code: dart]
/// Shows an instant notification immediately.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  final details = await getNotificationDetailsConfig(
    channelId: notificationWithDeepLink.channelId,
    level: notificationWithDeepLink.level,
    isFullScreen: notificationWithDeepLink.isFullScreen,
    imageAttachment: notificationWithDeepLink.imageAttachment,
    hasActions: notificationWithDeepLink.hasActions,
    customSound: notificationWithDeepLink.customSound,
  );

  await _flutterLocalNotificationsPlugin.show(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    details,
    payload: notificationWithDeepLink.toPayload(),
  );
}

[T] This ensures that all notifications have a deep link, even if one isn't explicitly provided.

[H2] Platform-Specific Configuration for Deep Links

[T] To make deep links work properly, we need to configure them in the platform-specific project files.

[H3] Android Configuration

[T] Open **android/app/src/main/AndroidManifest.xml** and add the following inside the `<activity>` tag:

[Code: xml]
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="timora" />
</intent-filter>

[T] This tells Android that your app can handle URLs with the `timora:` scheme.

[H3] iOS Configuration

[T] Open **ios/Runner/Info.plist** and add the following:

[Code: xml]
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>com.droidcon.timora</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>timora</string>
        </array>
    </dict>
</array>

[T] This tells iOS that your app can handle URLs with the `timora:` scheme.

[H2] Retrieving Pending Notifications

[T] Now that we've implemented notification tap handling and deep links, let's move on to notification management. The first step is to retrieve a list of pending notifications.

[H3] Implementing getPendingNotifications

[T] Open **lib/service/notification-manager/notification_manager.dart** and add the getPendingNotifications method:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }

    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'Unknown',
      body: request.body ?? '',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      level: NotificationLevel.normal,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, which can be used to display and manage notifications.

[H2] Cancelling Notifications

[T] Now that we can retrieve notifications, let's implement methods to cancel them.

[H3] Cancelling Individual Notifications

[T] Add the cancelNotification method to the NotificationManager class:

[Code: dart]
/// Cancels a specific notification by ID.
///
/// Removes both active and pending notifications with the specified [id].
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
}

[T] This method simply cancels a notification with the specified ID, preventing it from being displayed or removing it if it's already displayed.

[H3] Cancelling All Notifications

[T] Let's implement a method to cancel all notifications:

[Code: dart]
/// Cancels all active and scheduled notifications.
///
/// Removes all notifications created by the application.
Future<void> cancelAllNotifications() async {
  await _flutterLocalNotificationsPlugin.cancelAll();
}

[T] This method cancels all pending and active notifications, providing a way to clear all notifications at once.

[H2] Implementing the Notification Details Screen

[T] Now that we have methods for managing notifications, let's implement a notification details screen that allows users to view and cancel notifications.

[H3] Retrieving Notification Details

[T] First, let's implement a method to get the details of a specific notification:

[Code: dart]
/// Gets the details of a specific notification.
///
/// Returns a [NotificationModel] representing the notification with the specified ID,
/// or null if the notification is not found.
Future<NotificationModel?> getNotificationDetails(int id) async {
  // First, we need to retrieve all pending notifications from the system
  // This gives us a list of all notifications that are currently scheduled
  final notifications = await getPendingNotifications();

  // Next, we iterate through each notification in the list
  // We're looking for a notification that matches the provided ID
  for (final notification in notifications) {
    // When we find a notification with a matching ID, we return it immediately
    // This allows the caller to access all the notification's properties
    if (notification.id == id) {
      return notification;
    }
  }

  // If we've gone through all notifications and haven't found a match,
  // we return null to indicate that no notification with the given ID exists
  // This allows the caller to handle the case where a notification might have been cancelled
  return null;
}

[T] The getNotificationDetails method provides a crucial function in our notification management system. It allows us to retrieve detailed information about a specific notification when we know its unique identifier. This is particularly important for the notification details screen, where we need to display all the properties of a notification and allow users to edit or cancel it.

[T] The method works by first retrieving all pending notifications from the system using the getPendingNotifications method we implemented earlier. This gives us a comprehensive list of all notifications that are currently scheduled or active. Then, it iterates through this list, comparing each notification's ID with the one we're looking for. When it finds a match, it immediately returns that notification object, which contains all the details we need including the title, body, scheduled time, and other properties.

[T] If no matching notification is found after checking all pending notifications, the method returns null. This is an important behavior because it allows the calling code to handle the case where a notification might have been cancelled or expired. For example, the notification details screen can display an appropriate message if the requested notification no longer exists.

[H3] Implementing the Notification Details Page

[T] Open **lib/view/notification-details/notification_details_page.dart** and implement the NotificationDetailsPage class:

[Code: dart]
class NotificationDetailsPage extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsPage({
    Key? key,
    required this.notificationId,
  }) : super(key: key);

  @override
  State<NotificationDetailsPage> createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  NotificationModel? _notification;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadNotificationDetails();
  }

  Future<void> _loadNotificationDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notification = await NotificationManager().getNotificationDetails(widget.notificationId);

      setState(() {
        _notification = notification;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load notification details: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _cancelNotification() async {
    if (_notification == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await NotificationManager().cancelNotification(_notification!.id);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification cancelled successfully'),
        ),
      );

      // Navigate back to the home screen
      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _error = 'Failed to cancel notification: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
      ),
      body: _buildBody(),
      floatingActionButton: _notification != null
          ? FloatingActionButton(
              onPressed: _cancelNotification,
              tooltip: 'Cancel Notification',
              child: const Icon(Icons.delete),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Text(
          _error!,
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (_notification == null) {
      return const Center(
        child: Text('Notification not found'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _notification!.title,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _notification!.body,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          _buildNotificationDetails(),
        ],
      ),
    );
  }

  Widget _buildNotificationDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('ID', _notification!.id.toString()),
            _buildDetailRow('Channel', _notification!.channelId),
            _buildDetailRow('Type', _notification!.type.toString()),
            if (_notification!.scheduledTime != null)
              _buildDetailRow(
                'Scheduled Time',
                DateFormat('MMM d, yyyy HH:mm').format(_notification!.scheduledTime!),
              ),
            if (_notification!.repeatInterval != null)
              _buildDetailRow('Repeat Interval', _notification!.repeatInterval.toString()),
            _buildDetailRow('Priority', _notification!.level.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

[T] This screen displays the details of a notification and provides a button to cancel it. However, we also want to allow users to edit notifications. Let's enhance the NotificationDetailsPage to support editing.

[H3] Implementing Notification Editing

[T] Let's modify the NotificationDetailsPage to add editing functionality. We'll need to add text controllers for the title and body, and a method to pick a new date and time for scheduled notifications:

[Code: dart]
class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  NotificationModel? _notification;
  bool _isLoading = true;
  String? _error;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _bodyController;
  DateTime? _scheduledDateTime;
  final NotificationManager _notificationManager = NotificationManager();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _bodyController = TextEditingController();
    _loadNotificationDetails();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    super.dispose();
  }

  Future<void> _loadNotificationDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notification = await _notificationManager.getNotificationDetails(widget.notificationId);

      setState(() {
        _notification = notification;
        _isLoading = false;
        if (notification != null) {
          _titleController.text = notification.title;
          _bodyController.text = notification.body;
          _scheduledDateTime = notification.scheduledTime;
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load notification details: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _pickDateTime() async {
    if (_notification == null) return;

    final currentDate = _scheduledDateTime ?? DateTime.now();

    // Pick date
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate == null) return;

    // Pick time
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(currentDate),
    );

    if (pickedTime == null) return;

    // Combine date and time
    setState(() {
      _scheduledDateTime = DateTime(
        pickedDate.year,
        pickedDate.month,
        pickedDate.day,
        pickedTime.hour,
        pickedTime.minute,
      );
    });
  }

  Future<void> _updateNotification() async {
    // First, we need to ensure we have a notification to update and that the form is valid
    if (_notification == null) return;
    if (!_formKey.currentState!.validate()) return;

    // Show a loading indicator while we update the notification
    setState(() {
      _isLoading = true;
    });

    try {
      // Create an updated version of the notification with the new values
      // The copyWith method allows us to create a new instance with specific properties changed
      // while keeping all other properties the same
      final updatedNotification = _notification!.copyWith(
        title: _titleController.text,  // Use the new title from the text field
        body: _bodyController.text,    // Use the new body from the text field
        scheduledTime: _scheduledDateTime,  // Use the new scheduled time if it was changed
      );

      // To update a notification, we first need to cancel the existing one
      // This removes it from the system's notification manager
      await _notificationManager.cancelNotification(_notification!.id);

      // Then we need to reschedule the notification based on its type
      // Different types of notifications require different scheduling methods
      if (updatedNotification.type == NotificationType.scheduled &&
          updatedNotification.scheduledTime != null) {
        // For scheduled notifications, we use the scheduleNotification method
        // This sets the notification to appear at a specific future time
        await _notificationManager.scheduleNotification(model: updatedNotification);
      } else if (updatedNotification.type == NotificationType.periodic &&
                updatedNotification.repeatInterval != null) {
        // For periodic notifications, we use the showPeriodicNotification method
        // This sets the notification to repeat at regular intervals
        await _notificationManager.showPeriodicNotification(model: updatedNotification);
      } else {
        // For instant notifications, we use the showInstantNotification method
        // This shows the notification immediately
        await _notificationManager.showInstantNotification(model: updatedNotification);
      }

      // Show a success message to the user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification updated successfully'),
        ),
      );

      // Navigate back to the previous screen
      // This will typically be the home screen or notification list
      Navigator.pop(context);
    } catch (e) {
      // If anything goes wrong, show an error message and stop the loading indicator
      setState(() {
        _error = 'Failed to update notification: $e';
        _isLoading = false;
      });
    }
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
        actions: [
          if (_notification != null && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _toggleEditMode,
              tooltip: 'Edit Notification',
            ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _notification != null && !_isEditing
          ? FloatingActionButton(
              onPressed: _cancelNotification,
              tooltip: 'Cancel Notification',
              child: const Icon(Icons.delete),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Text(
          _error!,
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (_notification == null) {
      return const Center(
        child: Text('Notification not found'),
      );
    }

    return _isEditing ? _buildEditForm() : _buildDetailsView();
  }

  Widget _buildEditForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bodyController,
              decoration: const InputDecoration(
                labelText: 'Body',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a body';
                }
                return null;
              },
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            if (_notification!.type == NotificationType.scheduled)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Scheduled Time',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ListTile(
                    title: Text(
                      _scheduledDateTime != null
                          ? DateFormat('MMM d, yyyy HH:mm').format(_scheduledDateTime!)
                          : 'Not scheduled',
                    ),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: _pickDateTime,
                    tileColor: Colors.grey[200],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _toggleEditMode,
                  icon: const Icon(Icons.cancel),
                  label: const Text('Cancel'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _updateNotification,
                  icon: const Icon(Icons.save),
                  label: const Text('Save Changes'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _notification!.title,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _notification!.body,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          _buildNotificationDetails(),
        ],
      ),
    );
  }

  Widget _buildNotificationDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('ID', _notification!.id.toString()),
            _buildDetailRow('Channel', _notification!.channelId),
            _buildDetailRow('Type', _notification!.type.toString()),
            if (_notification!.scheduledTime != null)
              _buildDetailRow(
                'Scheduled Time',
                DateFormat('MMM d, yyyy HH:mm').format(_notification!.scheduledTime!),
              ),
            if (_notification!.repeatInterval != null)
              _buildDetailRow('Repeat Interval', _notification!.repeatInterval.toString()),
            _buildDetailRow('Priority', _notification!.level.toString()),
            if (_notification!.isFullScreen)
              _buildDetailRow('Full Screen', 'Yes'),
            if (_notification!.hasActions)
              _buildDetailRow('Has Actions', 'Yes'),
            if (_notification!.imageAttachment)
              _buildDetailRow('Has Image', 'Yes'),
            if (_notification!.customSound)
              _buildDetailRow('Custom Sound', 'Yes'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

[T] This enhanced implementation adds the following features to the notification details page:

[List]
Text fields for editing the notification title and body
A date/time picker for scheduled notifications
A toggle between view mode and edit mode
A form validation to ensure required fields are filled
A method to update the notification with the new values
A more detailed view showing all notification properties

[H2] Implementing the Home Screen

[T] The home screen should display a list of all pending notifications. Let's implement the _loadNotifications method in the HomePage class:

[Code: dart]
Future<void> _loadNotifications() async {
  setState(() {
    _isLoading = true;
  });

  try {
    final notifications = await NotificationManager().getPendingNotifications();

    setState(() {
      _notifications = notifications;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _error = 'Failed to load notifications: $e';
      _isLoading = false;
    });
  }
}

[T] This method loads all pending notifications and updates the UI accordingly.

[H3] Implementing the Notification List

[T] Now, let's implement the _buildNotificationList method to display the list of notifications:

[Code: dart]
Widget _buildNotificationList() {
  if (_notifications.isEmpty) {
    return const Center(
      child: Text('No pending notifications'),
    );
  }

  return ListView.builder(
    itemCount: _notifications.length,
    itemBuilder: (context, index) {
      final notification = _notifications[index];

      return ListTile(
        title: Text(notification.title),
        subtitle: Text(notification.body),
        trailing: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () async {
            await NotificationManager().cancelNotification(notification.id);

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notification cancelled'),
              ),
            );

            // Reload the notifications
            _loadNotifications();
          },
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            AppRoutes.notificationDetails.value,
            arguments: notification.id,
          ).then((_) {
            // Reload the notifications when returning from the details screen
            _loadNotifications();
          });
        },
      );
    },
  );
}

[T] This method builds a list of notification items, each with a tap handler to navigate to the details screen and a delete button to cancel the notification.

[H2] Testing Notification Interactions and Management

[T] Now that we've implemented notification interaction handling, deep links, and notification management, let's test these features.

[H3] Testing Basic Notification Taps

[T] Run the app and create a simple notification:

[List]
**Title**: "Tap Test"
**Body**: "Tap this notification to open the details screen"
**Channel**: "Default"
**Type**: "Instant"
**Level**: "Normal"

[T] Tap the "Create" button. The notification should appear. Tap on the notification, and you should be taken to the notification details screen.

[H3] Testing Custom Deep Links

[T] Let's create a notification with a custom deep link:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: 'Custom Deep Link',
  body: 'Tap to open the create notification screen',
  channelId: NotificationChannelIds.defaultChannel,
)
.withDeepLink('timora:/create-notification')
.build();

await NotificationManager().showInstantNotification(model: notification);

[T] This notification, when tapped, should take you to the create notification screen instead of the details screen.

[H3] Testing Notification Retrieval

[T] Run the app and create a few notifications with different types (instant, scheduled, periodic). Then, navigate to the home screen to see the list of pending notifications.

[T] Tap on a notification in the list to view its details.

[H3] Testing Notification Cancellation

[T] From the notification details screen, tap the "Cancel" button to cancel the notification. The notification should be removed from the system and you should be navigated back to the home screen.

[T] Alternatively, tap the delete button next to a notification in the list to cancel it directly from the home screen.

[H2] Best Practices for Notification Handling and Management

[T] When implementing notification handling and management features, consider the following best practices:

[List]
**Use Deep Links Consistently**: Always include deep links in notifications to provide a consistent navigation experience
**Provide Clear Feedback**: Show clear feedback when notifications are created, updated, or cancelled
**Handle Errors Gracefully**: Implement proper error handling for notification operations
**Refresh Data Automatically**: Reload notification data when returning from detail screens
**Confirm Destructive Actions**: Ask for confirmation before cancelling important notifications
**Show Notification Status**: Indicate whether notifications are pending, active, or expired

[H2] Summary

[T] In this section, we've implemented comprehensive notification handling and management features:

[List]
Understood the structure and purpose of deep links in notifications
Implemented the DeepLinkHandler to process deep links and navigate to appropriate screens
Added deep links to notifications to enable direct navigation
Configured platform-specific settings for deep links
Retrieved and displayed pending notifications
Implemented methods to cancel individual and all notifications
Created a notification details screen for viewing and managing notifications
Implemented editing functionality to update notification content and schedules
Implemented a home screen that displays a list of pending notifications
Added features for cancelling notifications from the UI

[T] With these features, our notification system now provides a seamless user experience, allowing users to navigate directly to relevant content by tapping on notifications and manage their notifications effectively.

[H3] Code to Implement

[T] Here's a summary of the code we've implemented in this section:

[List]
**DeepLinkHandler.handleNotificationDeeplink()**: Processes deep links from notification payloads
**DeepLinkHandler.processDeepLink()**: Shared logic for processing all deep links
**DeepLinkHandler._parseDeepLink()**: Parses a deep link URI into path and arguments
**NotificationDeepLinkUtil.generateNotificationDetailsDeepLink()**: Generates deep links for notification details
**NotificationDeepLinkUtil.extractNotificationId()**: Extracts notification IDs from deep links
**NotificationManager._onNotificationResponse()**: Handles notification taps
**NotificationBuilder.withDeepLink()**: Configures a notification with a custom deep link
**NotificationManager.getPendingNotifications()**: Gets all pending notifications
**NotificationManager.cancelNotification()**: Cancels a specific notification
**NotificationManager.cancelAllNotifications()**: Cancels all notifications
**NotificationManager.getNotificationDetails()**: Gets details of a specific notification
**NotificationDetailsPage._loadNotificationDetails()**: Loads notification details
**NotificationDetailsPage._cancelNotification()**: Cancels a notification
**NotificationDetailsPage._updateNotification()**: Updates a notification with new values
**NotificationDetailsPage._pickDateTime()**: Allows selecting a new date and time for scheduled notifications
**NotificationDetailsPage._toggleEditMode()**: Toggles between view and edit modes
**HomePage._loadNotifications()**: Loads all pending notifications
**HomePage._buildNotificationList()**: Builds the notification list
