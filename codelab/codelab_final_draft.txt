Write Your eBook

Section 1 eBook
Section Title
Getting Started with Local Notifications in Flutter



Chapter Title
Section Overview


[H2] Section Overview
[T] In this section, you'll explore:
[List]
Discover what local notifications are and why they’re crucial for enhancing mobile app engagement .
Set up the flutter_local_notifications package seamlessly in your Flutter project
Explore the starter project’s structure and its core notification components.
Run and verify the starter project to confirm your setup works flawlessly

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Ready to bring your mobile app to life via adding notifications to your Flutter app? Let’s dive in!


Next Chapter Title
Understanding Local Notifications and the flutter_local_notifications Package


[T] In this chapter, you’ll learn about the power of local notifications, their significance, and how well they work specifically in mobile applications.


[Image]





[H2] Local Notifications: What Are They?
[T] Without requiring a server, local notifications are app-driven alerts that appear on a user's device and land straight in the notification tray. Packed with text, images, or even interactive buttons, they grab attention and keep users connected.

[T] Two primary forms of notifications are used by mobile apps:
[List]
Local Notifications: Crafted by the app itself, these fire off right on the device—no internet or external server required. Schedule them, trigger them, done.
Remote Notifications: Delivered from a server, these need an internet connection and services like Firebase Cloud Messaging (FCM) or Apple Push Notification Service (APNS).

[T] We're focussing on local notifications in this codelab. They are your first choice for:
[List]
Alarms and reminders.
Dates of task completion.
Schedules for exercises or medications.
Updates on the status of ongoing operations like count-down, timers, local backups, etc.
[H3] Benefits of Local Notifications
[T] Local notifications bring advantages for users and developers like:
[List]
User Engagement: Engage users by sending them required updates, so that user keeps coming back to our app.
Timely reminders: Providing important information when it matters the most. This will help the users to not miss a critical task.
Offline First:  All the local notifications are configured on-device with platform specific APIs which triggers when a condition is met, not necessarily requiring a server.
[H3] Common Use Cases
[T] Numerous apps are powered by local notifications:
[List]
Productivity apps: Alert users to assignments, appointments, or approaching due dates.
Apps for wellness and fitness: Encourage exercise, medication, or a brief hydration break.
Calendar apps: Mark appointments or events and send out timely reminders.
Media Apps: Declare completed downloads or the release of new content.
Games: Use energy refills, or daily rewards to entice players.
[H2] Exploring the flutter_local_notifications Package
[T] Now let's dive into the flutter_local_notifications package, which makes implementing these features straightforward in Flutter applications. We'll look at its functionalities and organisation to assist us in integrating notifications into our Flutter application.
[H3] Overview of the Package
[T] flutter_local_notification is the cross-platform plugin for showing local notifications in Flutter apps. It allows for platform-specific customisations while offering a single API for iOS and Android.

[Image]

[T] By abstracting away a lot of the platform-specific details, this package makes managing notifications across various platforms easier while still granting access to more sophisticated features when required.
[H3] Key Features
[T] The plugin offers many important features such as:
[List]
Cross-platform support: Uses a single API to work on both iOS and Android.
Scheduling: Plan notifications for future delivery.
Customization:  Add icons, sounds, and vibration patterns to customize the look of notifications.
Notification Channels for Android.
Interactive Actions: Include buttons and input fields in notifications.
Background Handling
[H3] Plugin Structure
[T] There are multiple elements in the flutter_local_notifications plugin, some of which include:
[List]
FlutterLocalNotificationsPlugin: The primary class for setting up and displaying notifications
NotificationDetails: Sets up platform-specific notification settings by accepting AndroidNotificationDetails/DarwinNotificationDetails.
InitializationSettings: Settings required for initializing the plugin
NotificationAppLaunchDetails: Data about the notification, when the app launches.

[T] By learning about these elements, we can incorporate a robust notification system into our application.

[T] Up next, we will be going through the native setup that is required for this plugin to work.



Next Chapter Title
Native Configuration and Project Setup


[T] In this chapter, you’ll configure the native platforms (Android and iOS) to support local notifications, and explore the initial project setup to understand the files and TODOs you’ll be working with.
[H2] Native Configuration and Project Setup
[H3] Native Configuration Requirements
[T] In order for the flutter_local_notifications package to function correctly, we'll look at the platform-specific configuration requirements for iOS and Android in this chapter.
[H3] Android Configuration
[T] To enable different notification features, Android needs a number of permissions and settings in the AndroidManifest.xml file:

[T] Let's look at the permissions needed in the AndroidManifest.xml file:

[Code: xml]
<!-- Basic notification permissions -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

<!-- For scheduled notifications -->
<uses-permission android:name="android.permission.USE_EXACT_ALARM" />

<!-- Required for Android 13+ -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

<!-- For foreground services -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

[T] Additionally, you need to register several components in the application section of your AndroidManifest.xml:

[Code: xml]
<!-- Foreground service for notifications -->
<service
    android:name="com.dexterous.flutterlocalnotifications.ForegroundService"
    android:exported="false"
    android:stopWithTask="false"
    android:foregroundServiceType="specialUse">
    <property
        android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
        android:value="To show notifications"/>
</service>

<!-- Receivers for notification actions and scheduling -->
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
    <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED"/>
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
    </intent-filter>
</receiver>
[H3] iOS Configuration
[T] iOS also requires specific configurations in the Info.plist file and AppDelegate.swift file:


[T] In the AppDelegate.swift file, you need to add the following code:

[Code: swift]
import Flutter
import UIKit
import flutter_local_notifications

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    // This is required to make any communication available in the action isolate.
    FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
        GeneratedPluginRegistrant.register(with: registry)
    }

    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

[Emphasis, bulb]
For your app's notifications to work properly, these native configurations are necessary. The AndroidManifest.xml and AppDelegate.swift files in the starter project contain TODOs that remind you to add these configurations.
[H3] Project Structure Overview
[T] Take a moment to explore the project structure:

[Image]


[List]
lib/main.dart: The entry point of the application
lib/model/notification_model.dart: The data model for notifications
lib/service/notification-manager/: Contains the notification manager and builder classes
lib/view/: Contains the UI screens for creating and managing notifications
lib/core/: Contains utility classes, constants, and routing
[H3] Understanding the TODOs
[T] The starter project contains several TODOs that guide you through the implementation process. Let's look at some of the key TODOs:

[List]
main.dart: TODO to initialize the notification manager
notification_manager.dart: TODOs for implementing initialization, permissions, and notification display methods
notification_builder.dart: TODOs for implementing the show method and other notification features
AndroidManifest.xml: TODOs for adding required permissions and configurations
AppDelegate.swift: TODOs for setting up iOS notification handling

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // TODO: Initialize notification manager
    // This is where you'll initialize the notification system
    // Uncomment the line below when you've implemented the notification manager
    // await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] In the notification_manager.dart file, you'll find TODOs for implementing various methods:

[Code: dart]
/// Initializes timezone data for scheduling notifications.
///
/// Sets up the local timezone to ensure scheduled notifications appear at
/// the correct time.
Future<void> _initializeTimeZones() async {
  // TODO: Initialize timezone data
  // 1. Initialize timezone database
  // 2. Get the local timezone
  // 3. Set the local location
}

/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  // TODO: Configure platform-specific settings
  // 1. Set up Android settings with an icon
  // 2. Set up iOS settings with permissions and categories
  // 3. Create initialization settings for both platforms
  // 4. Initialize the plugin with the settings
  // 5. Configure notification action handling
}
[H3] Understanding the Dependencies
[T] To see the dependencies we'll be using, open pubspec.yaml file:
[Code: yaml]
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_local_notifications: ^18.0.1
  timezone: ^0.10.0
  flutter_timezone: ^4.1.0
  app_links: ^6.4.0
  http: ^1.3.0
  path_provider: ^2.1.5
  intl: ^0.20.2

[T] Let's understand the key dependencies:

[List]
flutter_local_notifications: The main package for handling local notifications
timezone and flutter_timezone: For handling scheduled notifications across different time zones
app_links: For handling deep links from notifications
intl: For date and time formatting
path_provider: For accessing the file system (used for storing notification images)
http: For network requests (used in some advanced notification features)

[T] Up Next we will be looking at the NotificationModel and other architectural components.


Next Chapter Title
Notification Models and Architecture


[H2] Notification Models and Architecture
[H3] Understanding the Notification Model
[T] In this chapter, we'll explore the notification model that represents the data structure for our notifications, helping us understand how notifications are configured and managed in our app.




[Image]



[T] Open lib/model/notification_model.dart and examine the NotificationModel class:
[T] NotificationModel class represents all the information needed to create and display a notification, some of the key properties are:
[List]
Basic Properties: id, title, body, channelId, type, level
Scheduling Properties: scheduledTime, timeOfDay, dayOfWeek, repeatInterval, periodDuration, occurrenceCount
Features: isFullScreen, hasActions, imageAttachment, customSound

[Emphasis, edit]
The copyWith method is particularly important as it allows us to create new instances with modified properties while preserving immutability. The NotificationBuilder class makes extensive use of this to set up notifications in a fluent API style.

[H3] Notification Types and Levels
[T] The NotificationModel uses enums to represent different types and importance levels of notifications:

[Code: dart]
enum NotificationType {
  instant,
  scheduled,
  periodic,
}

enum NotificationLevel {
  low,
  normal,
  urgent,
}

enum RepeatInterval {
  everyMinute,
  hourly,
  daily,
  weekly,
  monthly,
  custom,
}

[T] These enums help categorize notifications and determine their behavior:

[List]
NotificationType: Determines when the notification is displayed (immediately, at a scheduled time, or periodically)
NotificationLevel: Determines the importance and visibility of the notification
RepeatInterval: Determines how often a periodic notification repeats
[H3] Exploring the Notification Builder and Manager
[T] Next, let's examine the notification builder and manager classes that will be responsible for creating and displaying notifications in our app.
[H3] The NotificationBuilder Class
[T] The starter project includes a NotificationBuilder class that provides a fluent API for creating and configuring notifications. Let's examine this class.
[T] Open lib/service/notification-manager/notification_builder.dart:

[Code: dart]
class NotificationBuilder {
  final NotificationManager _manager;
  NotificationModel _model;

  NotificationBuilder._(this._manager, this._model);

  /// Creates a basic notification builder with required information.
  factory NotificationBuilder.create(
    NotificationManager manager, {
    required int id,
    required String title,
    required String body,
    required String channelId,
    NotificationLevel level = NotificationLevel.normal,
  }) {
    // Validation and initialization...
  }

  // Configuration methods...

  /// Shows the notification immediately or at the configured time.
  Future<void> show() async {
    // This is a placeholder - you'll implement this in the codelab
    debugPrint('Notification show method not implemented yet');
  }
}

[T] This class uses the Builder pattern to provide a fluent API for configuring notifications. It includes methods for:
[List]
Creating notifications: The create factory method initializes a basic notification
Configuring properties: Methods like setFullScreen, setImage, setActions, etc.
Scheduling: Methods like scheduleFor, setRepeatInterval, atTimeOfDay, etc.
Showing and cancelling: The show and cancel methods
[H3] Key TODOs in NotificationBuilder
[T] The NotificationBuilder class contains several TODOs that you'll need to implement during this codelab. Let's look at some of the most important ones:
[Code: dart]
/// Sets a default deeplink to the notification details page.
///
/// This is a convenience method that generates a deeplink to the notification details page
/// using the notification ID.
///
/// TODO: Implement this method to generate a deeplink to the notification details page
/// 1. Generate a deeplink URL that includes the notification ID
/// 2. Update the model with the new deeplink
/// 3. Return this builder instance for method chaining
NotificationBuilder setDefaultDeepLink() {
  // This is a placeholder - you'll implement this in the codelab
  return this;
}

[T] Another important TODO is in the show() method, which is responsible for displaying the notification based on its type:

[Code: dart]
/// Shows the notification immediately or at the configured time.
///
/// The behavior depends on the notification type (instant, scheduled, periodic).
///
/// Throws an [ArgumentError] if required properties for the notification type are missing.
///
/// TODO: Implement this method to show the notification based on its type
/// 1. Check the notification type (instant, scheduled, periodic)
/// 2. Call the appropriate method on the NotificationManager
/// 3. Handle any errors or missing properties
Future<void> show() async {
  // This is a placeholder - you'll implement this in the codelab
  debugPrint('Notification show method not implemented yet');
}
[H3] The NotificationManager Class
[T] The NotificationManager class is responsible for initializing the notification plugin and handling all notification operations. Let's examine this class.
[T] Open lib/service/notification-manager/notification_manager.dart.

[Code: dart]
class NotificationManager {
  // Singleton pattern implementation
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  // Initialization methods
  Future<void> init() async {
    if (_isInitialized) return;

    await _initializeTimeZones();
    await _initializeNotificationSettings();
    await _setupNotificationChannels();

    _isInitialized = true;
  }

  // Other methods...
}

[T] This class uses the Singleton pattern to ensure that only one instance exists throughout the app. It includes methods for:

[List]
Initialization: The init method sets up the notification plugin
Showing notifications: Methods for showing instant, scheduled, and periodic notifications
Managing notifications: Methods for cancelling and retrieving notifications
[H3] Key TODOs in NotificationManager
[T] The NotificationManager class contains several TODOs that you'll need to implement during this codelab. Let's look at some of the most important ones:

[Code: dart]
/// Shows an instant notification immediately.
///
/// Displays a notification with the configuration specified in [model].
/// Validates prerequisites before showing the notification.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  // TODO: Implement instant notification display
  // 1. Check if the plugin is initialized
  // 2. Request notification permissions if needed
  // 3. Configure notification details based on the model
  // 4. Show the notification with the plugin
  // 5. Include the model payload for handling notification taps
}

/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  // TODO: Implement scheduled notification
  // 1. Check if the notification should be enabled (cancel if not)
  // 2. Validate the scheduled time is not null
  // 3. Check notification prerequisites (initialization and permissions)
  // 4. Convert the DateTime to a TZDateTime with proper timezone handling
  // 5. Configure notification details
}

[T] Throughout this codelab, we'll implement these methods to create a complete notification system.
[H3] How the Classes Work Together
[T] The NotificationBuilder and NotificationManager classes work together to provide a clean, fluent API for creating and displaying notifications:

[Image]


[List]
1. The NotificationManager provides a createNotification method that returns a NotificationBuilder instance
2. The NotificationBuilder allows chaining configuration methods to set up the notification.
3. When show() is called on the builder, it delegates to the appropriate method on the NotificationManager.
4. The NotificationManager handles the platform-specific details of displaying the notification.

[Emphasis, tick]
This separation of concerns makes the code more maintainable and easier to understand.
[H3] Running and Exploring the Starter Project
[T] Now let's run the starter project to ensure everything is set up correctly and explore the UI that we'll be working with throughout this codelab:

[Code: bash]
cd source-code/starter-project/timora
flutter run

[T] The Timora app should launch with the entire UI but without any notification features. Notification creation and management screens are part of the app's interface.

[Image]


[T] Since we have not yet implemented the notification functionality, nothing will happen if you attempt to create a notification at this time. We will gradually implement all of the notification features during this codelab.
[H3] Exploring the UI
[T] Let's explore the main screens of the app:

[List]
Home Screen: Displays a list of scheduled notifications and provides options to create new ones
Create Notification Screen: Allows users to configure and create new notifications with various options
Notification Details Screen: Shows details of a specific notification and allows editing or deleting it

[T] The app's home screen serves as its primary entry point. Along with buttons to create new notifications, it shows a list of scheduled ones.

[Image]


[T] Key components of the home screen include:
Notification List: Shows pending notifications with their titles, bodies, and scheduled times
Create Button: Opens the create notification screen
Pull-to-Refresh: Refreshes the list of pending notifications
Notification Cards: Tapping a notification card opens its details

[T] Users can configure and create new notifications using the create notification screen. It has options for personalising notifications as well as a variety of form fields.

[Image]


[T] Key components of this screen include:
[List]
Title and Body Fields: For entering the notification text
Channel Selector: For choosing the notification category
Type Selector: For selecting when the notification should appear
Schedule Options: For configuring when scheduled or periodic notifications should appear
Priority Selector: For setting the importance level
Feature Toggles: For enabling additional advanced notification features
Create Button: For registering the notification

[Emphasis, question mark]
Currently, the create button doesn't do anything because we haven't implemented the notification functionality yet. Throughout this codelab, we'll implement the missing functionality step by step.
[H3] Code Challenge: Exploring the Notification Model
[T] To assess your understanding of the ideas we've discussed thus far, let's take on a code challenge. A challenge that focuses on interpreting the notification model.
[H3] The Challenge
[T] Your challenge is to explore the NotificationModel class and answer the following questions:
[List]
1. What properties are required to create a basic notification?
2. How would you create a scheduled notification for tomorrow at 9:00 AM?
3. What enum values would you use for a high-priority notification that repeats daily?
4. How would you configure a notification to show as a full-screen alert?

[T] Take some time to review the NotificationModel class and its properties before answering these questions.
[H3] Solution
[T] Let's go through the answers to the challenge:
[List]
1. Required Properties: id, title, body, channelId, and type are required for a basic notification. The level property has a default value of NotificationLevel.normal.

2. Scheduled Notification: To create a scheduled notification for tomorrow at 9:00 AM, you would:
   - Set type to NotificationType.scheduled
   - Set scheduledTime to DateTime.now().add(Duration(days: 1)).copyWith(hour: 9, minute: 0, second: 0)

3. High-Priority Daily Notification: You would use:
   - NotificationLevel.urgent for the priority
   - NotificationType.periodic for the type
   - RepeatInterval.daily for the repeat interval

4. Full-Screen Alert: To configure a notification as a full-screen alert, you would:
   - Set isFullScreen to true
   - You would also set the level to NotificationLevel.urgent

[T] This challenge helps you understand how to configure different types of notifications using the NotificationModel class.

[T] Up Next, we’ll summarize everything we’ve learned in this section and highlight the key takeaways.


Last Chapter Title
Section Summary



[H2] Section Summary
[T] Let's summarize what we have covered in this section:
[List]
We looked at local notifications' definition and advantages for mobile apps.
We studied the main characteristics of the flutter_local_notifications package.
We looked at the iOS and Android native configuration requirements.
We understood the NotificationModel class and how it represents notifications functionality.
We also looked at key TODOs in the project.
We explored the NotificationBuilder and NotificationManager classes

[T] You now have a solid understanding of the foundation of our notification system.
[H3] What's Next
[T] Up next, we'll start implementing the notification functionality, beginning with instant notifications that appear immediately when triggered. We'll then progress to more advanced features like scheduled notifications, notification channels, and interactive notifications.





Write Your eBook

Section 2 eBook
Section Title
Displaying Instant Notifications in Flutter



Chapter Title
Section Overview

[H2] Section Overview
[T] We'll build the foundation for adding instant notifications to our Flutter application in this chapter. Instant notifications give users of your app timely information without delay, much like a doorbell does when someone arrives.

[T] In this section, you'll explore:
[List]
How to request permissions and set up the notification manager
How to set up immediate notifications that show up right away
How to alter the look of notifications using various styles and icons
How to navigate to particular screens and manage notification taps

[T] After going over the fundamentals, you will be given a Code Challenge to practise, and at the conclusion of this section, we will talk about how to solve it.

[T] Let's begin!



Chapter Title
Setting Up the Notification Manager

[H2] Setting Up the Notification Manager
[T] To manage all notification-related tasks in our application, we will implement the NotificationManager class and its initialisation methods in this chapter. This class handles sending, scheduling, and organising all of your messages to users; think of it as the main post office for your app's notifications.
[H3] Understanding the Notification Manager
[T] By handling platform-specific details and offering a streamlined API for common notification tasks, the NotificationManager class acts as a facade for the flutter_local_notifications plugin.

[T] Let's examine the structure of the NotificationManager class in the starter project:

[Code: dart]
class NotificationManager {
  // Singleton pattern implementation
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  // Initialization methods
  Future<void> init() async {
    // TODO: Implement initialization
    debugPrint('NotificationManager.init() not implemented yet');
  }

  // Other methods with TODOs...
}

[T] To ensure that there is only one instance across the application, the class employs the Singleton pattern. Just as an area has a single postal service to prevent confusion and duplicate mail delivery, this is crucial for managing notification IDs and guaranteeing consistent behaviour.
[H3] Initializing the Notification Manager
[T] The first step is to initialize our notification manager in the main.dart file. This will set up the notification plugin and prepare it for use throughout the app.

[T] Open lib/main.dart and find the main() function:

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // TODO: Initialize notification manager
    // await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] Uncomment the line that initializes the notification manager. This is an important first step because your app must initialise the notification system before it can show users alerts, just as you must configure your phone's SIM settings before receiving calls.

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize notification manager
    await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] This ensures that all notification features are available when the app starts by initialising the notification manager before the app launches.
[H3] Implementing the init() Method
[T]  Let's now add the init() method to the NotificationManager class. The notification plugin will be initialised and the necessary configuration will be set up using this method.

[T] Open lib/service/notification-manager/notification_manager.dart and implement the init() method:

[Code: dart]
/// Initializes the notification system.
///
/// Must be called before any notifications are shown. Configures time zones,
/// notification settings, and platform-specific channels.
Future<void> init() async {
  if (_isInitialized) return;

  await _initializeTimeZones();
  await _initializeNotificationSettings();
  await _setupNotificationChannels();

  _isInitialized = true;
  debugPrint('Notification manager initialized');
}

[T] This method calls three helper methods to initialize different aspects of the notification system:

[List]
_initializeTimeZones(): Sets up time zone handling for scheduled notifications
_initializeNotificationSettings(): Configures notification settings and tap handling
_setupNotificationChannels(): Creates notification channels for Android

[T] Consider these three techniques as laying the groundwork for our notification system, similar to wiring installation, control panel configuration, and home security system zone setup.
[H3] Initializing Time Zones
[T] Setting up time zone handling early on is a good idea, even if we aren't yet using scheduled notifications. Let's implement the _initializeTimeZones() method:

[Code: dart]
/// Initializes time zone data for scheduled notifications.
///
/// Sets up the time zone database and configures the default time zone
/// to the device's local time zone.
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();

    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();

    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');

    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));

    debugPrint('Falling back to UTC timezone');
  }
}

[T] By using this method, the time zone database is initialised and the local time zone of the device is set as the default time zone. In the event that the initialisation fails, it also has error handling to revert to UTC.


Chapter Title
Configuring Notification Settings and Channels

[H2] Configuring Notification Settings and Channels

[T] In this chapter, we'll implement the methods to configure notification settings, set up notification channels for Android, and handle notification taps. Just as your phone has different ringtones and vibration patterns for different types of calls, our app needs different notification settings for various types of alerts.
[H3] Initializing Notification Settings
[T] Let's implement the _initializeNotificationSettings() method to configure the notification plugin:

[Code: dart]
/// Initializes notification settings and callback handlers.
///
/// Configures platform-specific initialization settings and sets up
/// notification tap handling.
Future<void> _initializeNotificationSettings() async {
  // Define the initialization settings for Android
  final AndroidInitializationSettings androidInitializationSettings =
      AndroidInitializationSettings(NotificationResources.defaultIcon);

  // Define the initialization settings for iOS
  final DarwinInitializationSettings iosInitializationSettings =
      DarwinInitializationSettings(
    requestAlertPermission: false,
    requestBadgePermission: false,
    requestSoundPermission: false,
    onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) {
      debugPrint(
        'Received notification: id=$id, title=$title, body=$body, payload=$payload',
      );
      // We don't need to handle this callback for iOS 10+ as it's deprecated
      return;
    },
  );

  // Combine the initialization settings
  final InitializationSettings initializationSettings = InitializationSettings(
    android: androidInitializationSettings,
    iOS: iosInitializationSettings,
  );

  // Initialize the plugin with the settings
  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification tap
      debugPrint('Notification tapped: ${response.payload}');

      // If there's a payload, try to parse it and handle the notification
      if (response.payload != null) {
        try {
          final model = NotificationModel.fromPayload(response.payload!);
          _handleNotificationTap(model);
        } catch (e) {
          debugPrint('Failed to parse notification payload: $e');
        }
      }
    },
    onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
  );

  debugPrint('Initialized notification settings');
}

[T] This method configures the notification plugin with platform-specific settings and sets up callbacks to handle notification taps in both the foreground and background. It's like setting up your doorbell to not only ring when pressed but also to show you who's at the door and let you talk to them.
[H3] Handling Notification Taps
[T] Now, let's add the _handleNotificationTap method to handle notification taps:

[Code: dart]
/// Handles a notification tap event.
///
/// Processes the notification payload and navigates to the appropriate screen
/// or performs the requested action.
void _handleNotificationTap(NotificationModel model) {
  // If the notification has a deep link, handle it
  if (model.deepLink != null) {
    final uri = Uri.parse(model.deepLink!);
    DeepLinkHandler.instance.handleDeepLink(uri);
  } else {
    // If there's no deep link, navigate to the notification details screen
    AppRouter.navigatorKey.currentState?.pushNamed(
      AppRoutes.notificationDetails.value,
      arguments: {'id': model.id},
    );
  }
}

[T] The DeepLinkHandler will navigate to the relevant screen after this method extracts the deep link from the notification payload. It goes to the notification details screen if there isn't a deep link. This is similar to how tapping on a text message notification triggers that particular conversation instead of merely launching the messaging app.
[H3] Setting Up Notification Channels
[T] Android 8.0 (API level 26) and higher requires notification channels to categorize notifications and give users control over which types they receive. Let's implement the _setupNotificationChannels method:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined notification channels with different importance levels
/// and configurations. This is required for Android 8.0 (API level 26) and higher.
Future<void> _setupNotificationChannels() async {
  // Only needed for Android
  if (!Platform.isAndroid) return;

  // Get the Android-specific implementation
  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin == null) {
    debugPrint('Failed to get Android plugin implementation');
    return;
  }

  // Create the default channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.defaultChannel,
      'Default Notifications',
      description: 'Default notification channel',
      importance: Importance.defaultImportance,
    ),
  );

  debugPrint('Set up notification channels');
}

[T] Using this, an Android default notification channel is created. In order to create additional channels with various configurations, we'll go into more detail in a later section. Identical to the various categories in your email inbox, notification channels aid in notification organisation and give users the option to select which kinds of notifications they wish to receive.

[T] For example, a user might want to receive work-related notifications with sound and vibration, but prefer personal notifications to be silent. Notification channels make this level of customization possible.


Chapter Title
Implementing Notifications and Platform-Specific Details

[H2] Implementing Notifications and Platform-Specific Details
[T] In this chapter, we'll implement permission requests, instant notifications, and platform-specific notification details. We'll configure our notification system to respect user permissions and adjust to various platforms, much like a delivery service requires your consent before leaving packages at your door and tailors delivery options according to your preferences.
[H3] Implementing Permission Requests
[T] Both iOS and Android require permission to show notifications. Let's implement the requestPermissions() method:

[Code: dart]
/// Requests notification permissions from the system.
///
/// Returns `true` if permissions are granted, `false` otherwise.
/// Handles platform-specific permission requests for iOS and Android.
Future<bool> requestPermissions() async {
  if (Platform.isIOS) {
    final plugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >();
    final bool? result = await plugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );
    return result ?? false;
  } else if (Platform.isAndroid) {
    final plugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();
    final bool? result = await plugin?.requestNotificationsPermission();
    return result ?? false;
  }
  return false;
}

[T] This method handles permission requests differently for iOS and Android:

[List]
On iOS, it requests permission for alerts, badges, and sounds
On Android, it requests the notification permission (required for Android 13+)

[T] This platform-specific approach is similar to how various countries may have distinct driving license application processes; the process differs based on the location, but the outcome is always the same (ability to drive).
[H3] Adding a Helper Method for Prerequisites
[T] Before showing any notification, we should check if all prerequisites are met. Let's implement the _checkNotificationPrerequisites() method:

[Code: dart]
/// Checks if all prerequisites for showing notifications are met.
///
/// Verifies that the plugin is initialized and permissions are granted.
/// Returns `true` if notifications can be shown, `false` otherwise.
Future<bool> _checkNotificationPrerequisites() async {
  if (!_isInitialized) {
    debugPrint('Notification manager not initialized');
    return false;
  }

  final bool permissionsGranted = await requestPermissions();
  if (!permissionsGranted) {
    debugPrint('Notification permissions not granted');
    return false;
  }

  return true;
}

[T] This method checks two important prerequisites:
[List]
The notification manager is initialized
Notification permissions are granted

[T] If either condition is not met, the method returns false, and we should not attempt to show notifications. This is like checking your car’s condition before attempting a journey - essential prerequisites.
[H3] Implementing Instant Notifications
[T] Now that we've set up the notification manager, let's implement the showInstantNotification() method to display notifications immediately:

[Code: dart]
/// Shows an instant notification immediately.
///
/// Displays a notification with the configuration specified in [model].
/// Validates prerequisites before showing the notification.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // Show the notification
  await _flutterLocalNotificationsPlugin.show(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    details,
    payload: notificationWithDeepLink.toPayload(),
  );

  debugPrint('Showed instant notification: ${notificationWithDeepLink.id}');
}

[T] This method does several important things:
[List]
Verifies that the requirements for notification are fulfilled
Adds a deep link to the notification if not already set
Builds platform-specific notification details
Displays the notification along with the configured information.
Includes the notification payload for handling notification taps

[T] Consider this approach as the last assembly line for notifications; it assembles all of the previously prepared parts (permissions, settings, and details) to produce a finished notification that is ready to be shown to the user.
[H3] Building Platform-Specific Notification Details
[T] To set up the platform-specific information, let's now implement the _buildNotificationDetails method. We must modify our notifications to appear and function properly on every platform, much like a tailored-fit suit.

[Code: dart]
/// Builds platform-specific notification details based on the notification model.
///
/// Configures Android and iOS specific settings according to the notification properties.
Future<NotificationDetails> _buildNotificationDetails(NotificationModel model) async {
  // Configure Android-specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    channelDescription: '${model.channelId} notifications',
    importance: _getAndroidImportance(model.level),
    priority: _getAndroidPriority(model.level),
    styleInformation: const DefaultStyleInformation(true, true),
    icon: NotificationResources.defaultIcon,
    playSound: model.customSound,
    enableLights: true,
    fullScreenIntent: model.isFullScreen,
    category: AndroidNotificationCategory.reminder,
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: model.customSound,
    sound: model.customSound ? NotificationResources.customSoundIOS : null,
    interruptionLevel: _getIOSInterruptionLevel(model.level),
  );

  // Combine the platform-specific details
  return NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

[T] This method configures platform-specific details for both Android and iOS, including:
[List]
Channel ID and description
Importance and priority levels
Sound settings
Visual style and appearance options

[T] Because Android and iOS handle notifications differently just as different car models may have different dashboard layouts but still serve the same purpose of providing the driver with information; this platform-specific configuration is essential.

[T] Up Next, we'll implement the NotificationBuilder class to provide a fluent API for creating notifications.


Chapter Title
Implementing Notification Builder

[H2] Implementing the Notification Builder
[T] In this chapter, we'll implement the NotificationBuilder class to provide a fluent API for creating and configuring notifications. Think of this class as a notification workshop where you can customize every aspect of your notification before sending it out.
[H3] Understanding the Builder Pattern
[T] The Builder pattern is a design pattern that allows for the step-by-step construction of complex objects. It's particularly useful for objects with many optional parameters, like our NotificationModel.

[T] The NotificationBuilder class provides a fluent API, allowing us to chain method calls to configure different aspects of a notification. This is similar to how you might customize a sandwich at a deli—adding ingredients one by one until you have exactly what you want.
[H3] Implementing the show() Method
[T] Open lib/service/notification-manager/notification_builder.dart and implement the show() method:

[Code: dart]
/// Shows the notification immediately or at the configured time.
///
/// The behavior depends on the notification type (instant, scheduled, periodic).
///
/// Throws an [ArgumentError] if required properties for the notification type are missing.
Future<void> show() async {
  switch (_model.type) {
    case NotificationType.instant:
      return await _manager.showInstantNotification(model: _model);

    case NotificationType.scheduled:
      if (_model.scheduledTime == null) {
        throw ArgumentError(
          'scheduledTime must be set for scheduled notifications',
        );
      }
      return await _manager.scheduleNotification(model: _model);

    case NotificationType.periodic:
      if (_model.repeatInterval == null) {
        throw ArgumentError(
          'repeatInterval must be set for periodic notifications',
        );
      }
      return await _manager.schedulePeriodic(model: _model);
  }
}

[T] This method delegates to the appropriate NotificationManager method based on the notification type, ensuring that the required properties are set. It acts like a traffic controller, directing each notification to the right processing method based on its type.

[T] For now, we'll only implement the showInstantNotification method in the NotificationManager class. We'll implement the other methods in later sections.
[H3] Using the NotificationBuilder
[T] Here's an example of how to use the NotificationBuilder to create and show a notification:

[Code: dart]
// Create a notification builder
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 1,
  title: 'Hello, World!',
  body: 'This is my first notification',
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
);

// Configure additional properties if needed
builder
  .setType(NotificationType.instant)
  .setCustomSound(true);

// Show the notification
await builder.show();

[T] This code creates a basic notification with a title, body, and channel ID, then configures it as an instant notification with a custom sound, and finally shows it. The fluent API makes the code read almost like a sentence, making it intuitive and easy to understand—similar to how recipe instructions flow naturally from one step to the next.

[T] Up Next, we'll test our implementation by creating and showing instant notifications.



Chapter Title
Testing Instant Notification

[H2] Testing Instant Notifications
[T] In this chapter, we'll test our implementation by creating and showing instant notifications, and we'll learn how to customize notification icons. Just as you'd test a new doorbell before relying on it, we need to verify our notification system works as expected.
[H3] Creating and Showing Instant Notifications
[T] Now that we've implemented instant notifications, let's test them by creating a simple notification.

[T] Run the app and navigate to the "Create Notification" screen. Fill in the following details:
[List]
Title: "Hello, World!"
Body: "This is my first notification"
Channel: "Default"
Type: "Instant"
Level: "Normal"

[T] Tap the "Create" button, and you should see a notification appear immediately.

//TODO [Image]

[T] If you tap on the notification, it should take you to the notification details screen, demonstrating that the deep link functionality is working correctly. This interaction flow is similar to how clicking on an email notification opens that specific email rather than just the inbox.
[H3] Customizing Notification Icons
[T] By default, notifications use the app icon, but you can customize the icon for different types of notifications. Let's add a custom icon for our notifications.

[T] First, add the icon to the android/app/src/main/res/drawable directory. Then, update the NotificationResources class in lib/core/constants/notification_constants.dart:

[Code: dart]
/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customIcon = '@drawable/custom_icon';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] Now, update the _buildNotificationDetails method to use the custom icon for certain notifications:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  model.channelId,
  model.channelId.toUpperCase(),
  channelDescription: '${model.channelId} notifications',
  importance: _getAndroidImportance(model.level),
  priority: _getAndroidPriority(model.level),
  styleInformation: const DefaultStyleInformation(true, true),
  icon: model.channelId == NotificationChannelIds.work
      ? NotificationResources.customIcon
      : NotificationResources.defaultIcon,
  playSound: model.customSound,
  enableLights: true,
  fullScreenIntent: model.isFullScreen,
  category: AndroidNotificationCategory.reminder,
);

[T] This code uses a custom icon for work notifications and the default icon for other notifications. Using different icons for different notification types helps users quickly identify the nature of the notification at a glance—similar to how different road signs have distinct shapes to convey their meaning from a distance.

[T] Up Next, we'll tackle a code challenge to test your understanding of notification customization.


Chapter Title
Code Challenge

[H2] Code Challenge: Customizing Notification Appearance
[T] In this chapter, you'll get a chance to practice what you've learned with a code challenge focused on customizing notification appearance. Just as interior designers personalize spaces with color schemes, we'll customize our notifications with distinct visual identities.
[H3] The Challenge
[T] Your challenge is to modify the _buildNotificationDetails method to add a custom color for notifications in the "Personal" channel. You'll need to:

[List]
 Add a new constant in NotificationResources for the personal color
Update the _buildNotificationDetails method to use this color for personal notifications
Test the change by creating a personal notification

[T] Take some time to implement this challenge before looking at the solution.

[H3] Solution
[T] Let's go through the solution to the challenge:

[T] First, add a new constant in NotificationResources:

[Code: dart]
/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customIcon = '@drawable/custom_icon';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
  static const int personalColor = 0xFF9C27B0; // Purple color
}

[T] Then, update the _buildNotificationDetails method to use this color for personal notifications:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  model.channelId,
  model.channelId.toUpperCase(),
  channelDescription: '${model.channelId} notifications',
  importance: _getAndroidImportance(model.level),
  priority: _getAndroidPriority(model.level),
  styleInformation: const DefaultStyleInformation(true, true),
  icon: model.channelId == NotificationChannelIds.work
      ? NotificationResources.customIcon
      : NotificationResources.defaultIcon,
  color: model.channelId == NotificationChannelIds.personal
      ? Color(NotificationResources.personalColor)
      : null,
  playSound: model.customSound,
  enableLights: true,
  fullScreenIntent: model.isFullScreen,
  category: AndroidNotificationCategory.reminder,
);

[T] Now, when you create a notification with the "Personal" channel, it will have a purple color on Android devices.

[T] This challenge demonstrates how to customize notifications based on their channel or other properties, allowing for a more personalized notification experience. By using different colors for different notification channels, you're creating a visual language that helps users quickly identify the type of notification at a glance.

[T] Up Next, we'll summarize what we've learned in this section and preview what's coming in the next section.


Chapter Title
Section Summary

[H2] Section Summary
[T] In this chapter, we'll recap what we've learned about implementing instant notifications in Flutter. Like reviewing a blueprint after completing a construction project, this summary helps solidify our understanding of the notification system we've built.

[T] Let's summarize what we have covered in this section:
[List]
We implemented the NotificationManager class to handle notification operations
We initialized the flutter_local_notifications plugin with platform-specific settings
We set up notification channels for Android
We implemented permission requests for both Android and iOS
We created the showInstantNotification method to display notifications immediately
We implemented the NotificationBuilder class with a fluent API for creating notifications
We customized notification appearance with different icons and colors
We tested our implementation by creating and showing instant notifications

[T] You now have a solid understanding of how to display instant notifications in Flutter. You can create notifications with different titles, messages, and appearance options, and handle notification taps to navigate to specific screens.
[H3] Key Takeaways
[Emphasis, download]
Platform-Specific Configuration: Both Android and iOS require specific settings for notifications to work properly
Builder Pattern: The NotificationBuilder class provides a clean, fluent API for creating notifications
Permission Handling: Always check for and request permissions before showing notifications
Deep Links: Adding deep links to notifications enables seamless navigation when users tap on them

[T] The code we've implemented in this section provides the foundation for more advanced notification features, which we'll explore in the upcoming sections.

[T] Up Next, we'll dive into organizing notifications with custom channels, where you'll learn how to categorize notifications and give users more control over how they appear.



Write Your eBook

Section 3 eBook
Section Title
Organizing Notifications with Custom Channels



Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll explore:
[List]
What notification channels are and why they're important
How to create and configure notification channels with different importance levels
How to implement custom sounds for notification channels
How to provide a consistent experience across both Android and iOS platforms
How to let users customize their notification preferences

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!





Chapter Title
Understanding Notification Channels

[H2] Understanding Notification Channels

[T] In this chapter, we'll explore the concept of notification channels and how they help users manage notification overload.
[H3] What Are Notification Channels?
[T] Notification channels are a feature introduced in Android 8.0 (API level 26) that help categorize notifications and allow users to control them at a granular level. Each channel represents a category of notifications with its own importance level, sound, vibration pattern, and other settings.

[T] Think of notification channels like TV channels on your television. Just as you might have different channels for news, sports, movies, and documentaries, your app can have different notification channels for various types of alerts. And just as you can adjust the volume or mute specific TV channels based on your preferences, users can customize how each notification channel behaves.

[T] For example, a messaging app might have separate channels for:
[List]
Direct messages
Group messages
Friend requests
System notifications

[T] Users can then customize how each type of notification behaves, such as:
[List]
Allowing some notifications to make sounds while silencing others
Setting different importance levels for different types of notifications
Blocking certain types of notifications entirely while allowing others
[H3] Why Are Notification Channels Important?
[T] Notification channels provide several benefits:
[List]
User Control: Users can customize notification behavior at a granular level
Reduced Notification Fatigue: Users can silence less important notifications without disabling all notifications
Better Organization: Developers can categorize notifications logically
Compliance: Required for apps targeting Android 8.0 (API level 26) or higher

[T] While notification channels are an Android-specific feature, we'll implement them in a way that provides a consistent experience across both Android and iOS.
[H3] Planning Our Notification Channels
[T] For our Timora app, we'll create several notification channels:
[List]
Work: For work-related reminders and meetings
Personal: For personal reminders and events
Health: For health and fitness reminders

[T] Each channel will have different default settings for importance and sound options, providing a rich and customizable notification experience.

[T] Up Next, we'll implement these notification channels in our app, starting with the basic channel structure.





Chapter Title
Implementing Notification Channels

[H2] Implementing Notification Channels
[T] In this chapter, we'll set up the foundation for our notification organization system by implementing channels that categorize different types of alerts.
[H3] Setting Up Channel Constants
[T] First, let's define constants for our notification channels. Open lib/core/constants/notification_constants.dart and examine the following:

[Code: dart]
/// Notification channel IDs
class NotificationChannelIds {
  // Standard notification channels
  static const String work = 'work_notifications';
  static const String personal = 'personal_notifications';
  static const String health = 'health_notifications';
  static const String defaultChannel = 'default_notifications';

  // Channels with custom sounds - naming convention: <channel>_sound
  static const String workSound = '${work}_sound';
  static const String personalSound = '${personal}_sound';
  static const String healthSound = '${health}_sound';
}

/// Notification channel display names and descriptions
class NotificationChannelDetails {
  // Standard channel names and descriptions
  static const String workName = 'Work Notifications';
  static const String workDescription =
      'Notifications related to work and productivity';

  static const String personalName = 'Personal Notifications';
  static const String personalDescription =
      'Notifications related to personal matters';

  static const String healthName = 'Health Notifications';
  static const String healthDescription =
      'Notifications related to health and wellness';

  // Custom sound channel names and descriptions - consistent naming pattern
  static const String workSoundName = '$workName with Sound';
  static const String workSoundDescription =
      '$workDescription (with custom sound)';

  static const String personalSoundName = '$personalName with Sound';
  static const String personalSoundDescription =
      '$personalDescription (with custom sound)';

  static const String healthSoundName = '$healthName with Sound';
  static const String healthSoundDescription =
      '$healthDescription (with custom sound)';
}

/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] These constants define the IDs, names, descriptions, and resources for our notification channels. Note how we have pairs of channels - a standard version and a version with custom sound for each category.
[H3] Implementing the _setupNotificationChannels Method
[T] Now, let's implement the _setupNotificationChannels method in the NotificationManager class. Open lib/service/notification-manager/notification_manager.dart and update the method:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined channels with appropriate importance levels for
/// different notification categories (work, personal, health).
Future<void> _setupNotificationChannels() async {
  if (!Platform.isAndroid) return;

  final plugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >();
  if (plugin == null) return;

  // Create standard and custom sound channels for each category
  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.work,
    standardName: NotificationChannelDetails.workName,
    standardDesc: NotificationChannelDetails.workDescription,
    soundId: NotificationChannelIds.workSound,
    soundName: NotificationChannelDetails.workSoundName,
    soundDesc: NotificationChannelDetails.workSoundDescription,
    importance: Importance.high,
  );

  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.personal,
    standardName: NotificationChannelDetails.personalName,
    standardDesc: NotificationChannelDetails.personalDescription,
    soundId: NotificationChannelIds.personalSound,
    soundName: NotificationChannelDetails.personalSoundName,
    soundDesc: NotificationChannelDetails.personalSoundDescription,
    importance: Importance.defaultImportance,
  );

  await _createChannelPair(
    plugin,
    standardId: NotificationChannelIds.health,
    standardName: NotificationChannelDetails.healthName,
    standardDesc: NotificationChannelDetails.healthDescription,
    soundId: NotificationChannelIds.healthSound,
    soundName: NotificationChannelDetails.healthSoundName,
    soundDesc: NotificationChannelDetails.healthSoundDescription,
    importance: Importance.high,
  );
}

[T] This method creates three pairs of notification channels - one for work, one for personal, and one for health notifications. Each pair consists of a standard channel and a channel with custom sound.

[H2] Implementing the _createChannelPair Helper Method
[T] To avoid code duplication, we'll implement a helper method to create pairs of notification channels:

[Code: dart]
/// Helper method to create a pair of notification channels (standard and custom sound)
Future<void> _createChannelPair(
  AndroidFlutterLocalNotificationsPlugin plugin, {
  required String standardId,
  required String standardName,
  required String standardDesc,
  required String soundId,
  required String soundName,
  required String soundDesc,
  required Importance importance,
}) async {
  // Standard channel
  await plugin.createNotificationChannel(
    AndroidNotificationChannel(
      standardId,
      standardName,
      description: standardDesc,
      importance: importance,
    ),
  );

  // Channel with custom sound
  await plugin.createNotificationChannel(
    AndroidNotificationChannel(
      soundId,
      soundName,
      description: soundDesc,
      importance: importance,
      sound: const RawResourceAndroidNotificationSound(
        NotificationResources.customSoundAndroid,
      ),
    ),
  );
}

[T] This helper method creates two notification channels for each category - a standard channel and a channel with custom sound. This approach allows users to choose whether they want to hear a custom sound for each type of notification.

[T] Up Next, we'll explore how to use these channels when creating notifications.



Chapter Title
Using Channels in Notifications

[H2] Using Notification Channels in Notifications
[T] In this chapter, we'll apply our notification channels to actual notifications, ensuring users receive alerts with the appropriate priority and sound settings.
[H3] Selecting the Appropriate Channel
[T] When creating a notification, we need to select the appropriate channel based on the notification's category and whether it should play a custom sound. This is similar to how a postal service might route mail through different processing centers based on its priority and destination. Let's implement a helper method to get the effective channel ID:

[Code: dart]
/// Helper method to get the appropriate channel ID based on custom sound flag
String _getEffectiveChannelId(String channelId, bool customSound) {
  if (!Platform.isAndroid || !customSound) {
    return channelId;
  }

  // Map of standard channels to their sound-enabled counterparts
  const channelMap = {
    NotificationChannelIds.work: NotificationChannelIds.workSound,
    NotificationChannelIds.personal: NotificationChannelIds.personalSound,
    NotificationChannelIds.health: NotificationChannelIds.healthSound,
  };

  return channelMap[channelId] ?? channelId;
}

[T] This method takes a channel ID and a custom sound flag, and returns the appropriate channel ID. If the custom sound flag is true, it returns the sound-enabled version of the channel; otherwise, it returns the standard channel.
[H3] Using Channels in Notification Details
[T] Now, let's see how to use these channels when building notification details:

[Code: dart]
/// Builds platform-specific notification details based on the notification model.
///
/// Configures Android and iOS specific settings according to the notification properties.
Future<NotificationDetails> _buildNotificationDetails({
  required String channelId,
  required NotificationLevel level,
  bool customSound = false,
  bool hasActions = false,
  // Other parameters...
}) async {
  // Get the effective channel ID based on custom sound flag
  final effectiveChannelId = _getEffectiveChannelId(channelId, customSound);

  // Configure Android-specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    effectiveChannelId,
    effectiveChannelId.toUpperCase(),
    channelDescription: '$effectiveChannelId notifications',
    importance: _getAndroidImportance(level),
    priority: _getAndroidPriority(level),
    // Other settings...
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: level.playSound,
    sound: customSound ? NotificationResources.customSoundIOS : null,
    // Other settings...
  );

  // Combine the platform-specific details
  return NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

[T] This method builds platform-specific notification details based on the provided parameters. For Android, it uses the effective channel ID to determine which channel to use. For iOS, it sets the appropriate category and sound settings.
[H3] Creating a Notification with a Specific Channel
[T] Finally, let's see how to create a notification with a specific channel:

[Code: dart]
/// Shows a notification with the specified details.
Future<void> showNotification({
  required int id,
  required String title,
  required String body,
  required String channelId,
  NotificationLevel level = NotificationLevel.default_,
  bool customSound = false,
  // Other parameters...
}) async {
  final details = await _buildNotificationDetails(
    channelId: channelId,
    level: level,
    customSound: customSound,
    // Other parameters...
  );

  await _flutterLocalNotificationsPlugin.show(
    id,
    title,
    body,
    details,
    payload: _buildPayload(/* payload parameters */),
  );
}

[T] This method shows a notification with the specified details, using the appropriate channel based on the provided channel ID and custom sound flag.

[T] Up Next, we'll explore how to implement iOS notification categories to provide a consistent experience across platforms.





Chapter Title
Implementing iOS Notification Categories

[H2] Implementing iOS Notification Categories
[T] In this chapter, we'll bridge the platform gap by implementing iOS notification categories that provide a consistent experience for users regardless of their device.
[H3] Understanding iOS Notification Categories
[T] While Android uses notification channels, iOS uses notification categories to organize notifications. iOS notification categories serve a similar purpose but work differently:
[List]
Android Channels: Focus on notification appearance and behavior
iOS Categories: Focus on notification actions and interaction options

[T] To provide a consistent experience, we'll implement iOS notification categories that correspond to our Android notification channels.
[H3] Defining iOS Notification Categories
[T] First, let's define a constant for our interactive notification category:

[Code: dart]
/// Notification categories and interaction types
class NotificationCategories {
  static const String interactive = 'INTERACTIVE_CATEGORY';
}

[T] This constant defines the ID for our interactive notification category, which we'll use for notifications that have actions.
[H3] Implementing iOS Notification Categories
[T] Now, let's implement iOS notification categories in our _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  final snoozeAction = DarwinNotificationAction.plain(
    NotificationActionIds.snooze,
    NotificationActionTexts.snooze,
    options: {DarwinNotificationActionOption.foreground},
  );
  final dismissAction = DarwinNotificationAction.plain(
    NotificationActionIds.dismiss,
    NotificationActionTexts.dismiss,
    options: {DarwinNotificationActionOption.foreground},
  );
  final replyAction = DarwinNotificationAction.text(
    NotificationActionIds.reply,
    NotificationActionTexts.reply,
    buttonTitle: NotificationActionTexts.reply,
  );

  final interactiveCategory = DarwinNotificationCategory(
    NotificationCategories.interactive,
    actions: [snoozeAction, dismissAction, replyAction],
  );

  final iosSettings = DarwinInitializationSettings(
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
    notificationCategories: [interactiveCategory],
  );

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This method configures iOS notification categories with actions for interactive notifications. The interactive category includes actions for snoozing, dismissing, and replying to notifications.
[H3] Using iOS Categories in Notifications
[T] When building notification details, we need to set the appropriate category for iOS notifications:

[Code: dart]
// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  // Other settings...
);

[T] This code sets the category identifier based on whether the notification has actions. If it has actions, it uses the interactive category; otherwise, it uses the channel ID as the category.

[T] Up Next, we'll explore how to organize notifications using the NotificationChannel enum.





Chapter Title
Organising Notifications

[H2] Organizing Notifications with the NotificationChannel Enum
[T] In this chapter, we'll create a unified system for notification management using enums, making our code more maintainable while providing visual consistency for users.
[H3] Understanding the NotificationChannel Enum
[T] The NotificationChannel enum provides a centralized way to manage notification channels, their display names, and associated colors. This is similar to how a library uses a classification system like the Dewey Decimal System to organize books - it provides a structured way to categorize and retrieve information:

[Code: dart]
/// Enhanced enum for notification channels with associated colors and display names.
///
/// This enum provides a centralized way to manage notification channels,
/// their display names, and associated colors.
enum NotificationChannel {
  work(
    id: NotificationChannelIds.work,
    displayName: 'Work',
    color: Colors.blue,
    soundId: NotificationChannelIds.workSound,
  ),

  personal(
    id: NotificationChannelIds.personal,
    displayName: 'Personal',
    color: Colors.green,
    soundId: NotificationChannelIds.personalSound,
  ),

  health(
    id: NotificationChannelIds.health,
    displayName: 'Health',
    color: Colors.red,
    soundId: NotificationChannelIds.healthSound,
  ),

  default_(
    id: NotificationChannelIds.defaultChannel,
    displayName: 'Default',
    color: Colors.blueGrey,
    soundId: null,
  );

  /// The channel ID used for notification configuration
  final String id;

  /// The human-readable display name for the channel
  final String displayName;

  /// The color associated with this channel for UI elements
  final Color color;

  /// The ID for the sound variant of this channel (if applicable)
  final String? soundId;

  const NotificationChannel({
    required this.id,
    required this.displayName,
    required this.color,
    this.soundId,
  });

  /// Get a NotificationChannel from its ID
  static NotificationChannel fromId(String id) {
    return NotificationChannel.values.firstWhere(
      (channel) => channel.id == id || channel.soundId == id,
      orElse: () => NotificationChannel.default_,
    );
  }
}

[T] This enum defines four notification channels (work, personal, and health) with their associated IDs, display names, colors, and sound IDs. It also provides a method to get a NotificationChannel from its ID.

[H3] Using the NotificationChannel Enum in the UI
[T] The NotificationChannel enum can be used in the UI to provide a consistent visual identity for each type of notification:

[Code: dart]
class CategorySection extends StatelessWidget {
  final CreateNotificationController controller;

  const CategorySection({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final categories = {
      NotificationChannelIds.work: 'Work',
      NotificationChannelIds.personal: 'Personal',
      NotificationChannelIds.health: 'Health',
    };

    return ChipSelector(
      options: categories.keys.toList(),
      selectedOption: controller.value.channelId,
      onSelected: controller.updateChannelId,
      // Custom display function to show the category name instead of the ID
      customLabelBuilder: (option) => categories[option] ?? option,
    );
  }
}

[T] This widget displays a chip selector for notification categories, using the NotificationChannel enum to get the display names for each category.

[T] Up Next, we'll tackle a code challenge to test your understanding of notification channels.






Chapter Title
Code Challenge: Creating Custom Sound Notifications

[H2] Code Challenge: Creating Custom Sound Notifications
[T] In this chapter, you'll apply your knowledge of notification channels by creating notifications with custom sounds that help users distinguish between different types of alerts.
[H3] The Challenge
[T] Think of notification sounds like ringtones on your phone - they help you know who's calling without looking at the screen. In our app, we want to give users the same ability to distinguish between different types of notifications just by hearing them.

[T] Your challenge is to create a notification system for a productivity app that helps users manage their work-life balance. The app needs to send different types of notifications:
[List]
1. Work notifications that sound professional and urgent
2. Personal notifications that sound friendly but noticeable
3. Health notifications that are gentle reminders

[T] For this challenge:
[List]
1. Create a work notification with a custom sound that would alert the user about an upcoming meeting
2. Make sure the notification has high importance since meetings are time-sensitive
3. Implement the notification using the appropriate channel and custom sound flag

[T] This challenge simulates a real-world scenario where different notification types need different levels of user attention. Take some time to implement this before moving to the solution chapter.



Chapter Title
Solution: Creating Custom Sound Notifications

[H2] Solution: Creating Custom Sound Notifications
[T] In this chapter, we'll walk through the solution to our custom sound notification challenge, examining how to properly implement different notification types with appropriate sounds.
[H3] Understanding the Solution
[T] Just as a restaurant might use different bell sounds to indicate when food is ready for different tables, our app uses different sounds to indicate different types of notifications. Let's implement our solution:

[Code: dart]
// Create a notification builder for a work notification
final builder = NotificationBuilder.create(
  notificationManager,
  id: 1,
  title: 'Work Meeting',
  body: 'You have a meeting in 15 minutes',
  channelId: NotificationChannelIds.work,
  level: NotificationLevel.high,
);

// Set the custom sound flag to true
builder.setCustomSound(true);

// Show the notification
await builder.show();

[T] This code creates a notification builder for a work notification, sets the custom sound flag to true, and shows the notification. The notification will use the work channel with custom sound, which we defined in the _setupNotificationChannels method.
[H3] Breaking Down the Implementation
[T] Let's examine what's happening in our solution:
[List]
We create a notification builder with specific parameters:
 An ID to uniquely identify this notification
A title and body that clearly communicate the purpose
The work channel ID to categorize this as a work notification
High importance level since meetings are time-sensitive
We enable the custom sound by calling setCustomSound(true)
We show the notification, which triggers the system to display it using our configured channel

[T] When this notification appears, the user will hear the custom sound we associated with work notifications, helping them immediately recognize that it's work-related without even looking at their device.

[T] Up Next, we'll summarize what we've learned in this section and preview what's coming in the next section.


[H2] Section Summary
[T] In this chapter, we'll review the key concepts we've learned about notification channels and how they enhance the user experience of our app.
[H3] Key Takeaways
[T] Let's summarize what we have covered in this section:
[List]
We learned about notification channels and their importance in Android
We implemented multiple notification channels with different importance levels
We created pairs of channels for each category - a standard channel and a channel with custom sound
We implemented iOS notification categories for a consistent cross-platform experience
We used the NotificationChannel enum to organize notifications and provide a consistent visual identity
We learned how to select the appropriate channel based on the notification's category and custom sound flag

[T] Think of notification channels like the sorting system in a post office. Just as mail gets sorted into different categories (express, standard, bulk) with different handling priorities, our notification channels sort our app's messages into different categories (work, personal, health) with different importance levels and sounds.

[T] You now have a solid understanding of how to organize notifications using channels and provide a consistent experience across both Android and iOS platforms. Your app can now deliver notifications with different importance levels and sounds, giving users more control over how they receive information.

[T] The notification channels we've implemented provide a foundation for more advanced notification features, which we'll explore in the upcoming sections.

[T] Up Next, we'll dive into scheduling notifications with time zone awareness, where you'll learn how to create notifications that appear at specific times in the future, regardless of the user's location or time zone changes.

Write Your eBook

Section 4 eBook
Section Title
Scheduling Notifications with Time Zone Awareness



Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll explore:
[List]
Understanding the importance of time zone handling in scheduled notifications
Initializing time zone data using the timezone package
Implementing scheduled notifications with time zone awareness
Adding date and time pickers to the UI for scheduling notifications
Testing scheduled notifications across different time zones

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!



Next Chapter Title
Understanding Time Zone Handling in Notifications

[H2] Understanding Time Zone Handling in Notifications

[T] In this chapter, we'll explore why time zones are crucial for scheduled notifications and how to properly handle them in your Flutter app.
[H3] Why Time Zones Matter
[T] Time zones are crucial for scheduled notifications. Consider these scenarios:
[List]
A user schedules a notification for 8:00 AM and then travels to a different time zone
A user in New York schedules a notification for a friend in London
A notification is scheduled for a specific global event (like a live stream)

[T] In each case, we need to decide whether the notification should appear at the local time (8:00 AM in the current time zone) or at a specific absolute time (8:00 AM in the original time zone).

[T] For most reminder apps, the first scenario is the most common: users want their notifications to appear at the same local time, regardless of their current time zone. For example, a "Take medication at 8:00 AM" reminder should appear at 8:00 AM local time, even if the user travels to a different time zone.
[H3] Time Zone Libraries
[T] To handle time zones correctly, we're using two packages:
[List]
timezone: Provides time zone data and utilities for working with time zones
flutter_timezone: Helps determine the device's local time zone

[T] These packages allow us to:
[List]
Initialize the time zone database with the latest data
Get the device's local time zone
Convert between different time zones
Schedule notifications with proper time zone handling
[H3] Reviewing Time Zone Initialization
[T] We've already implemented the _initializeTimeZones() method in the previous section. Let's review it to ensure it's correctly set up:

[Code: dart]
/// Initializes timezone data for scheduling notifications.
///
/// Sets up the local timezone to ensure scheduled notifications appear at
/// the correct time.
Future<void> _initializeTimeZones() async {
  tz_data.initializeTimeZones();
  final String timeZoneName = await FlutterTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZoneName));
}

[T] This method initializes the time zone database and sets the default time zone to the device's local time zone. It's simple but essential for ensuring that scheduled notifications appear at the correct time.

[T] Up Next: We'll implement the core functionality for scheduling notifications with proper time zone awareness.




Chapter Title
Implementing Scheduled Notifications

[H2] Implementing Scheduled Notifications
[T] In this chapter, we'll implement the core functionality for scheduling notifications at specific times with proper time zone handling.
[H3] The scheduleNotification Method
[T] Let's implement the scheduleNotification() method in the NotificationManager class. This method will schedule a notification to appear at a specific time in the future.

[Code: dart]
/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot schedule notification: prerequisites not met');
    return;
  }

  if (model.scheduledTime == null) {
    throw ArgumentError(
      'scheduledTime must be provided for scheduled notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  final tz.TZDateTime scheduledDate = tz.TZDateTime.from(
    notificationWithDeepLink.scheduledTime!,
    tz.local,
  );

  final details = await getNotificationDetailsConfig(
    channelId: notificationWithDeepLink.channelId,
    level: notificationWithDeepLink.level,
    isFullScreen: notificationWithDeepLink.isFullScreen,
    imageAttachment: notificationWithDeepLink.imageAttachment,
    hasActions: notificationWithDeepLink.hasActions,
    customSound: notificationWithDeepLink.customSound,
  );

  await _flutterLocalNotificationsPlugin.zonedSchedule(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    scheduledDate,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    payload: notificationWithDeepLink.toPayload(),
  );
}

[T] This method is similar to showInstantNotification(), but with a few key differences:
[List]
It checks if the notification should be enabled (and cancels it if not)
It validates that scheduledTime is not null
It converts the DateTime to a TZDateTime for proper time zone handling
It uses zonedSchedule() instead of show() to schedule the notification
It configures additional parameters for scheduling behavior

[T] The androidScheduleMode parameter is set to exactAllowWhileIdle, which ensures the notification is delivered at the exact time, even if the device is in doze mode.

[T] The uiLocalNotificationDateInterpretation parameter is set to wallClockTime, which means the notification will be scheduled for the exact time specified, regardless of the device's time zone.
[H3] Implementing Notification Cancellation
[T] We referenced a cancelNotification() method in the scheduleNotification() method. Let's implement it now:

[Code: dart]
/// Cancels a specific notification by ID.
///
/// Removes both active and pending notifications with the specified [id].
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
}

[T] This method simply cancels a notification with the specified ID, preventing it from being displayed or removing it if it's already displayed.

[T] Up Next: We'll enhance the NotificationBuilder class to make it easier to create scheduled notifications.


Chapter Title
Enhancing the Notification Builder

[H2] Enhancing the Notification Builder
[T] In this chapter, we'll enhance the NotificationBuilder class to make it easier to create scheduled notifications with a fluent API.
[H3] Adding Scheduling Methods to NotificationBuilder
[T] Open lib/service/notification-manager/notification_builder.dart and examine the scheduling methods:

[Code: dart]
/// Schedules the notification to be delivered at a specific future time.
///
/// Changes the notification type to scheduled.
///
/// [scheduledTime] Time when the notification should be delivered
///
/// Throws an [ArgumentError] if the scheduled time is in the past.
NotificationBuilder scheduleFor(DateTime scheduledTime) {
  final now = DateTime.now();
  if (scheduledTime.isBefore(now)) {
    throw ArgumentError('Scheduled time cannot be in the past');
  }
  _model = _model.copyWith(
    type: NotificationType.scheduled,
    scheduledTime: scheduledTime,
  );
  return this;
}

[T] This method makes it easy to schedule a notification for a specific time. It validates that the scheduled time is not in the past and updates the notification model with the scheduled time.
[H3] Using the Enhanced NotificationBuilder
[T] With these enhancements, we can now create scheduled notifications with a clean, fluent API:

[Code: dart]
// Create a notification scheduled for tomorrow at 9:00 AM
final tomorrow = DateTime.now().add(Duration(days: 1));
final scheduledTime = DateTime(
  tomorrow.year,
  tomorrow.month,
  tomorrow.day,
  9, // 9 AM
  0, // 0 minutes
);

final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: "Meeting Reminder",
  body: "Don't forget your team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.scheduleFor(scheduledTime);

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for tomorrow at 9:00 AM with a clean, readable syntax.

[T] Up Next: We'll connect the UI to our scheduled notification implementation, allowing users to create scheduled notifications through the app interface.



Chapter Title
Connecting the UI to Schedule Notifications

[H2] Connecting the UI to Scheduled Notifications
[T] In this chapter, we'll connect the UI components for date and time selection to our notification system, allowing users to create scheduled notifications through the app interface.
[H3] Understanding the Create Notification Screen
[T] Open lib/view/create-notification/create_notification_page.dart and examine the CreateNotificationPage class. This screen includes:
[List]
Text fields for the notification title and body
A dropdown for selecting the notification channel
A dropdown for selecting the notification type
Date and time pickers for scheduled notifications
A dropdown for selecting the notification importance level
A button to create the notification

[T] When the user selects "Scheduled" as the notification type, the date and time pickers become visible.
[H3] Implementing the Date and Time Pickers
[T] The date and time pickers are already implemented in the UI. Let's examine how they work:

[Code: dart]
Future<void> _pickDate() async {
  final date = await showDatePicker(
    context: context,
    initialDate: DateTime.now(),
    firstDate: DateTime.now(),
    lastDate: DateTime.now().add(const Duration(days: 365)),
  );

  if (date != null && mounted) {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null && mounted) {
      final dateTime = DateTime(
        date.year,
        date.month,
        date.day,
        time.hour,
        time.minute,
      );
      _controller.setScheduledDateTime(dateTime);
    }
  }
}

[T] This method shows a date picker followed by a time picker, then combines the selected date and time into a DateTime object and updates the controller.
[H3] Scheduling the Notification
[T] When the user taps the "Schedule Notification" button, the _scheduleNotification method is called:

[Code: dart]
Future<void> _scheduleNotification() async {
  if (!_controller.formKey.currentState!.validate()) return;

  // Validate fields based on notification type
  final validationResult = _controller.validateNotificationFields();
  if (!validationResult.isValid) {
    _showValidationError(validationResult.errorMessage!);
    return;
  }

  // Get prepared notification and show it
  final builder = _controller.prepareNotification();
  await builder.show();

  if (mounted) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification scheduled successfully!')),
    );
  }
}

[T] This method validates the form, prepares the notification using the controller, and shows it. Let's examine how the controller prepares the notification:

[Code: dart]
NotificationBuilder prepareNotification() {
  final value = this.value;

  // Create the base notification builder
  final builder = NotificationBuilder.create(
    NotificationManager(),
    id: DateTime.now().millisecondsSinceEpoch % 100000,
    title: value.title,
    body: value.body,
    channelId: value.channelId,
    level: value.level,
  );

  // Configure based on type
  switch (value.notificationType) {
    case 'Instant':
      // No additional configuration needed for instant notifications
      break;

    case 'Scheduled':
      builder = builder.scheduleFor(value.scheduledDateTime!);
      break;

    case 'Periodic':
      // We'll cover this in the next section
      break;
  }

  return builder;
}

[T] This method creates a notification builder with the basic properties, then configures it based on the selected notification type. For scheduled notifications, it uses the scheduleFor method we added earlier.

[T] Up Next: We'll implement weekly recurring notifications, which are a special type of scheduled notification.




Chapter Title
Implementing Weekly Recurring Notifications

[H2] Implementing Weekly Recurring Notifications
[T] In this chapter, we'll implement weekly recurring notifications, which allow users to schedule notifications that repeat on a specific day of the week.
[H3] Understanding Recurring Notifications
[T] Recurring notifications are different from one-time scheduled notifications. Instead of occurring just once at a specific time, they repeat at regular intervals. Weekly notifications are a common type of recurring notification that repeat on the same day of the week at the same time.
[H3] Implementing Weekly Notifications
[T] In our app, we implement weekly notifications using a combination of methods in the NotificationBuilder class:

[Code: dart]
/// Configures the notification to repeat at a specific interval.
///
/// Changes the notification type to periodic.
///
/// [repeatInterval] How frequently the notification should repeat
NotificationBuilder setRepeatInterval(RepeatInterval repeatInterval) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: repeatInterval,
  );
  return this;
}

/// Sets a specific time of day for recurring notifications.
///
/// Used primarily with daily and weekly notifications to ensure they appear
/// at a consistent time.
///
/// [timeOfDay] The time when the notification should be shown
NotificationBuilder atTimeOfDay(TimeOfDay timeOfDay) {
  _model = _model.copyWith(timeOfDay: timeOfDay);
  return this;
}

/// Sets the day of week for weekly recurring notifications.
///
/// [dayOfWeek] Day of week (1-7, where 1 is Monday and 7 is Sunday)
///
/// Throws an [ArgumentError] if dayOfWeek is out of valid range.
NotificationBuilder onDayOfWeek(int dayOfWeek) {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw ArgumentError('Day of week must be between 1-7 (Monday to Sunday)');
  }
  _model = _model.copyWith(dayOfWeek: dayOfWeek);
  return this;
}

[T] To create a weekly notification, we need to use all three methods together:

[Code: dart]
// Create a notification scheduled for every Monday at 9:00 AM
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 456,
  title: "Weekly Team Meeting",
  body: "Don't forget your weekly team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.setRepeatInterval(RepeatInterval.weekly)
.atTimeOfDay(TimeOfDay(hour: 9, minute: 0))
.onDayOfWeek(1); // Monday

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for every Monday at 9:00 AM.
[H3] How Weekly Notifications Work
[T] When we call show() on a builder configured for weekly notifications, it calls the showPeriodicNotification method in the NotificationManager class. This method handles the scheduling of recurring notifications:

[Code: dart]
Future<void> showPeriodicNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  // Validation and setup code...

  // For daily/weekly notifications with a specific time, use zonedSchedule with matchDateTimeComponents
  if (_shouldUseZonedSchedule(notificationWithDeepLink)) {
    await _scheduleRecurringAtTime(notificationWithDeepLink, details);
  } else {
    // For simpler repeating notifications without specific time requirements
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      notificationWithDeepLink.id,
      notificationWithDeepLink.title,
      notificationWithDeepLink.body,
      notificationWithDeepLink.repeatInterval!,
      details,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: notificationWithDeepLink.toPayload(),
    );
  }
}

[T] For weekly notifications, the _shouldUseZonedSchedule method returns true, so the _scheduleRecurringAtTime method is called:

[Code: dart]
Future<void> _scheduleRecurringAtTime(
  NotificationModel model,
  NotificationDetails details,
) async {
  // Calculate the next occurrence based on the model properties
  tz.TZDateTime nextOccurrence = _calculateNextOccurrence(
    timeOfDay: model.timeOfDay!,
    dayOfWeek:
        model.repeatInterval == RepeatInterval.weekly
            ? model.dayOfWeek
            : null,
  );

  // Determine the recurrence pattern
  DateTimeComponents matchDateTimeComponents =
      model.repeatInterval == RepeatInterval.daily
          ? DateTimeComponents.time
          : DateTimeComponents.dayOfWeekAndTime;

  // Schedule the notification with the recurrence pattern
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    nextOccurrence,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    matchDateTimeComponents: matchDateTimeComponents,
    payload: model.toPayload(),
  );
}

[T] This method calculates the next occurrence of the notification based on the day of week and time of day, then schedules it with the matchDateTimeComponents parameter set to DateTimeComponents.dayOfWeekAndTime. This tells the system to repeat the notification on the same day of the week at the same time.

[T] Up Next: We'll test our scheduled notifications implementation to ensure it works correctly across different scenarios
Chapter Title
Testing Schedule Notifications

[H2] Testing Scheduled Notifications
[T] In this chapter, we'll test our scheduled notifications implementation to ensure it works correctly across different scenarios.
[H3] Creating a Scheduled Notification
[T] Run the app and navigate to the "Create Notification" screen. Fill in the following details:
[List]
Title: "Scheduled Reminder"
Body: "This notification was scheduled for a future time"
Channel: "Default"
Type: "Scheduled"
Date: Select a date in the future
Time: Select a time a few minutes from now
Level: "Normal"

[T] Tap the "Create" button. The notification should be scheduled and appear at the specified time.

//TODO [Image]

[T] To verify that the notification is scheduled, you can check the debug output or navigate to the home screen, which should show the scheduled notification in the list.
[H3] Testing Time Zone Handling
[T] To test timezone handling, you can change your device's time zone and verify that the notification still appears at the correct local time.

[T] On Android:
[List]
Go to Settings > System > Date & time
Turn off "Automatic time zone"
Select a different time zone
Schedule a notification and verify it appears at the correct time

[T] On iOS:
[List]
Go to Settings > General > Date & Time
Turn off "Set Automatically"
Select a different time zone
Schedule a notification and verify it appears at the correct time

[T] Remember to set your device's time zone back to automatic when you're done testing.
[H3] Testing Weekly Notifications
[T] To test weekly notifications:
[List]
Navigate to the "Create Notification" screen
Fill in the basic details (title, body, etc.)
Select "Periodic" as the notification type
Select "Weekly" as the periodic subtype
Select a day of the week
Select a time
Tap the "Create" button

[T] The notification should be scheduled to repeat on the selected day of the week at the selected time. You can verify this by checking the debug output or waiting for the notification to appear.

[T] Up Next: Let's tackle a code challenge to deepen your understanding of scheduled notifications.





Chapter Title
Code Challenge: Retrieving Pending Notifications

[H2] Code Challenge: Retrieving Pending Notifications
[T] In this chapter, we'll tackle a code challenge to implement a method to retrieve pending notifications, which will deepen your understanding of scheduled notifications.
[H3] The Challenge
[T] Implement a method to get all pending notifications and display them in a list. You'll need to:
[List]
Use the getPendingNotifications method in the NotificationManager class
Create a UI to display the pending notifications
Add a refresh button to update the list
[H3] Solution: Implementing Pending Notifications Retrieval
[T] First, let's examine the getPendingNotifications method in the NotificationManager class:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }

    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'Unknown',
      body: request.body ?? '',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      level: NotificationLevel.normal,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, which can be used to display information about the notifications.

[T] Now, let's create a simple UI to display the pending notifications:

[Code: dart]
class PendingNotificationsPage extends StatefulWidget {
  const PendingNotificationsPage({Key? key}) : super(key: key);

  @override
  _PendingNotificationsPageState createState() => _PendingNotificationsPageState();
}

class _PendingNotificationsPageState extends State<PendingNotificationsPage> {
  final NotificationManager _manager = NotificationManager();
  List<NotificationModel> _pendingNotifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingNotifications();
  }

  Future<void> _loadPendingNotifications() async {
    setState(() {
      _isLoading = true;
    });

    final notifications = await _manager.getPendingNotifications();

    setState(() {
      _pendingNotifications = notifications;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pending Notifications'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPendingNotifications,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _pendingNotifications.isEmpty
              ? const Center(child: Text('No pending notifications'))
              : ListView.builder(
                  itemCount: _pendingNotifications.length,
                  itemBuilder: (context, index) {
                    final notification = _pendingNotifications[index];
                    return ListTile(
                      title: Text(notification.title),
                      subtitle: Text(notification.body),
                      trailing: Text(_getNotificationTypeText(notification)),
                    );
                  },
                ),
    );
  }

  String _getNotificationTypeText(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.instant:
        return 'Instant';
      case NotificationType.scheduled:
        final scheduledTime = notification.scheduledTime;
        if (scheduledTime != null) {
          return 'Scheduled for ${scheduledTime.day}/${scheduledTime.month} at ${scheduledTime.hour}:${scheduledTime.minute.toString().padLeft(2, '0')}';
        }
        return 'Scheduled';
      case NotificationType.periodic:
        return 'Periodic';
    }
  }
}

[T] This page displays a list of pending notifications and provides a refresh button to update the list. It uses the getPendingNotifications method to retrieve the notifications and displays them in a ListView.

[T] Up Next: Let's summarize what we've learned in this section and look ahead to the next section.





Chapter Title
Section Summary

[H2] Section Summary
[T] Let's summarize what we have covered in this section:
[List]
We explored the importance of time zone handling in scheduled notifications
We implemented the scheduleNotification method to schedule notifications for future delivery
We enhanced the NotificationBuilder with methods for scheduling notifications
We connected the UI date and time pickers to our notification system
We implemented weekly recurring notifications using a combination of methods
We tackled a code challenge to implement pending notifications retrieval

[T] Key concepts to remember:
[List]
Always use TZDateTime for scheduling notifications to ensure proper time zone handling
Consider how notifications should behave when users change time zones
Use the matchDateTimeComponents parameter to create recurring notifications
Test your notifications with different time zones to ensure they behave correctly

[T] Code we implemented:
[List]
NotificationManager.scheduleNotification(): Schedules a notification for a future time
NotificationManager.cancelNotification(): Cancels a notification
NotificationManager.getPendingNotifications(): Gets all pending notifications
NotificationBuilder.scheduleFor(): Configures a notification for a specific time
NotificationBuilder.setRepeatInterval(): Configures a notification to repeat at a specific interval
NotificationBuilder.atTimeOfDay(): Sets a specific time of day for recurring notifications
NotificationBuilder.onDayOfWeek(): Sets the day of week for weekly recurring notifications

[T] Up Next, we'll dive into customizing notification behavior and priority to create more effective and engaging alerts.


Write Your eBook

Section 5 eBook
Section Title
Customizing Notification Behavior and Priority


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll explore:
[List]
Understanding notification importance and priority levels across Android and iOS
Customizing notification sounds for different channels and notification types
Implementing vibration patterns to provide tactile feedback
Setting lock screen visibility for different notification levels
Creating full-screen intents for urgent notifications
Adding LED blinking patterns for visual notification on Android devices

[T] These customizations will help your notifications stand out and provide appropriate levels of interruption based on their importance. Just as a good communication system uses different signals for different situations, your app will use different notification behaviors for different types of alerts.

[T] Let's begin by understanding how notification importance and priority work across platforms!


Chapter Title
Understanding Notification Importance and Priority

[H2] Understanding Notification Importance and Priority
[T] In this chapter, we'll explore the differences between importance and priority in notifications and how they affect user experience across platforms.

[H3] Importance vs. Priority
[T] In the context of notifications, "importance" and "priority" are related but distinct concepts:

[List]
Importance (Android): Determines how the notification is displayed (heads-up, sound, vibration)
Priority (Android): Determines the order of notifications in the notification drawer
Interruption Level (iOS): Controls how notifications interrupt the user (passive, active, time-sensitive)

[T] Think of importance as how loudly a notification announces itself, while priority determines its position in line. On iOS, the interruption level combines both concepts.

[H3] The NotificationLevel Enum
[T] To simplify working with these platform-specific concepts, our app uses a NotificationLevel enum that maps to appropriate settings on each platform:

[Code: dart]
/// Defines importance and behavior levels for notifications.
///
/// Each level configures appropriate platform-specific settings for
/// notification visibility, sound, vibration, and priority.
enum NotificationLevel {
  /// Low-prominence notification level.
  ///
  /// Doesn't play sound or vibrate, and has restricted visibility.
  low(
    importance: Importance.low,
    priority: Priority.low,
    playSound: false,
    vibrate: false,
    visibility: NotificationVisibility.private,
  ),

  /// Standard notification level with default importance.
  ///
  /// Plays sound, vibrates, and is publicly visible.
  normal(
    importance: Importance.defaultImportance,
    priority: Priority.defaultPriority,
    playSound: true,
    vibrate: true,
    visibility: NotificationVisibility.public,
  ),

  /// High-priority notification level for important alerts.
  ///
  /// Uses maximum importance, plays sound, vibrates, and is publicly visible.
  urgent(
    importance: Importance.max,
    priority: Priority.high,
    playSound: true,
    vibrate: true,
    visibility: NotificationVisibility.public,
  );

  final Importance importance;
  final Priority priority;
  final bool playSound;
  final bool vibrate;
  final NotificationVisibility visibility;

  const NotificationLevel({
    required this.importance,
    required this.priority,
    required this.playSound,
    required this.vibrate,
    required this.visibility,
  });
}

[T] This enum encapsulates all the platform-specific settings in a single, easy-to-use type. When you set a notification's level to NotificationLevel.urgent, it automatically configures the appropriate importance, priority, sound, vibration, and visibility settings for both Android and iOS.

[T] Notice how each level has specific properties that determine its behavior:
[List]
Low: Uses low importance and priority, doesn't play sound or vibrate, and has private visibility
Normal: Uses default importance and priority, plays sound, vibrates, and has public visibility
Urgent: Uses maximum importance and high priority, plays sound, vibrates, and has public visibility

[H3] Using NotificationLevel in Notification Details
[T] When building notification details, we use the properties from the NotificationLevel directly:

[Code: dart]
// Configure Android specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  // Other settings...
);

// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  // Other settings...
);

[T] This approach ensures that notifications have consistent behavior based on their importance level across both Android and iOS.


Chapter Title
Customizing Notification Sounds

[H2] Customizing Notification Sounds
[T] In this chapter, we'll implement custom sounds for notifications, allowing users to distinguish between different types of alerts without even looking at their device.

[H3] Adding Sound Resources
[T] First, we need to add sound files to our project:

[List]
For Android: Add sound files to the android/app/src/main/res/raw directory
For iOS: Add sound files to the ios/Runner directory and include them in the Xcode project

[T] For this codelab, we'll use a file named custom_sound.mp3 for Android and custom_sound.wav for iOS. These files are already included in the starter project.

[H3] Updating Notification Resources
[T] Let's update the NotificationResources class to include our sound files:

[Code: dart]
/// Notification resources
class NotificationResources {
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] These constants define the names of our sound resources, which we'll use when configuring notifications.

[H3] Using Custom Sounds in Notification Channels
[T] For Android, we can associate custom sounds with notification channels. Let's update the _createChannelPair method to include custom sounds:

[Code: dart]
// Channel with custom sound
await plugin.createNotificationChannel(
  AndroidNotificationChannel(
    soundId,
    soundName,
    description: soundDesc,
    importance: importance,
    sound: const RawResourceAndroidNotificationSound(
      NotificationResources.customSoundAndroid,
    ),
  ),
);

[T] This creates a variant of each notification channel that includes a custom sound.

[H3] Using the Effective Channel ID
[T] To determine whether to use the standard channel or the sound-enabled channel, we use the _getEffectiveChannelId method:

[Code: dart]
/// Helper method to get the appropriate channel ID based on custom sound flag
String _getEffectiveChannelId(String channelId, bool customSound) {
  if (!Platform.isAndroid || !customSound) {
    return channelId;
  }

  // Map of standard channels to their sound-enabled counterparts
  const channelMap = {
    NotificationChannelIds.work: NotificationChannelIds.workSound,
    NotificationChannelIds.personal: NotificationChannelIds.personalSound,
    NotificationChannelIds.health: NotificationChannelIds.healthSound,
  };

  return channelMap[channelId] ?? channelId;
}

[T] This method returns the sound-enabled version of a channel ID if the customSound flag is true, or the standard channel ID otherwise.

[H3] Enhancing the Notification Builder
[T] Let's enhance the NotificationBuilder to make it easier to set custom sounds:

[Code: dart]
/// Sets whether to use a custom notification sound.
///
/// [useCustomSound] Whether to use a custom sound for this notification
NotificationBuilder setCustomSound(bool useCustomSound) {
  // Avoid unnecessary updates
  if (_model.customSound == useCustomSound) {
    return this;
  }
  _model = _model.copyWith(customSound: useCustomSound);
  return this;
}

[T] This method sets the customSound flag on the notification model, which will cause the notification to use a custom sound when displayed.


Chapter Title
Customizing Vibration Patterns

[H2] Customizing Vibration Patterns
[T] In this chapter, we'll implement custom vibration patterns for notifications, providing tactile feedback that helps users identify the type of notification without looking at their device.

[H3] Understanding Vibration Patterns
[T] Vibration patterns are defined as arrays of longs that alternate between the duration to turn the vibrator on and the duration to turn it off. The first value is how long to wait before turning the vibrator on, and the second value is how long to keep the vibrator on, and so on.

[T] For example, a pattern of [0, 1000, 500, 1000] means:
[List]
Wait 0 milliseconds
Vibrate for 1000 milliseconds
Pause for 500 milliseconds
Vibrate for 1000 milliseconds

[H3] Implementing Vibration Patterns
[T] Let's add a method to get vibration patterns based on the notification level:

[Code: dart]
/// Gets the appropriate vibration pattern for a notification level.
///
/// Returns a custom vibration pattern for specific levels, or null for the default pattern.
Int64List? _getVibrationPatternForLevel(NotificationLevel level) {
  switch (level) {
    case NotificationLevel.low:
      return null; // No vibration
    case NotificationLevel.normal:
      return Int64List.fromList([0, 250, 250, 250]); // Short vibration
    case NotificationLevel.urgent:
      return Int64List.fromList([0, 1000, 500, 1000, 500, 1000]); // Long vibration
  }
}

[T] This method returns different vibration patterns based on the notification level:
[List]
Low: No vibration
Normal: A short vibration pattern
Urgent: A longer, more noticeable vibration pattern

[H3] Updating Notification Details
[T] Now, let's update the _buildNotificationDetails method to use these vibration patterns:

[Code: dart]
// Configure Android-specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  channelDescription: '${effectiveChannelId} notifications',
  importance: _getAndroidImportance(model.level),
  priority: _getAndroidPriority(model.level),
  styleInformation: const DefaultStyleInformation(true, true),
  icon: NotificationResources.defaultIcon,
  playSound: true,
  sound: model.customSound
      ? RawResourceAndroidNotificationSound(_getSoundForChannel(model.channelId))
      : null,
  enableLights: true,
  vibrationPattern: _getVibrationPatternForLevel(model.level),
  enableVibration: level.vibrate,
  fullScreenIntent: model.isFullScreen,
  category: AndroidNotificationCategory.reminder,
);

[T] With these changes, notifications will have different vibration patterns based on their level.


Chapter Title
Implementing Full-Screen Notifications

[H2] Implementing Full-Screen Notifications
[T] In this chapter, we'll implement full-screen notifications, which are high-priority alerts that can appear even when the device is locked.

[H3] Understanding Full-Screen Intents
[T] Full-screen intents are a feature in Android that allows notifications to appear as a full-screen activity when the device is locked. They're useful for urgent alerts that require immediate attention, such as alarm clocks or important reminders.

[T] On iOS, critical alerts serve a similar purpose, allowing notifications to break through Do Not Disturb mode.

[H3] Setting Up Full-Screen Notifications
[T] To enable full-screen notifications, we need to set the fullScreenIntent flag to true in the AndroidNotificationDetails:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  // Other settings...
);

[T] The isFullScreen parameter comes from the notification model, which we can set using the NotificationBuilder.

[H3] Enhancing the Notification Builder
[T] Let's enhance the NotificationBuilder to make it easier to create full-screen notifications:

[Code: dart]
/// Configures the notification to appear as a full-screen high-priority alert.
///
/// Full-screen notifications can break through Do Not Disturb mode on some devices.
///
/// [isFullScreen] Whether the notification should be displayed as full-screen
NotificationBuilder setFullScreen(bool isFullScreen) {
  // Avoid unnecessary updates
  if (_model.isFullScreen == isFullScreen) {
    return this;
  }
  _model = _model.copyWith(isFullScreen: isFullScreen);
  return this;
}

[T] This method sets the isFullScreen flag on the notification model, which will cause the notification to appear as a full-screen intent when displayed.

[H3] Testing Full-Screen Notifications
[T] To test full-screen notifications, we need to create a notification with the isFullScreen flag set to true and the level set to NotificationLevel.urgent:

[Code: dart]
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: "Urgent Alert",
  body: "This is a full-screen notification",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.urgent,
)
.setFullScreen(true);

await builder.show();

[T] This will create a high-priority notification that appears as a full-screen intent when the device is locked.


Chapter Title
Code Challenge: Adding LED Patterns

[H2] Code Challenge: Adding LED Patterns
[T] In this chapter, you'll tackle a code challenge to implement LED blinking patterns for notifications on Android devices.

[H3] The Challenge
[T] Your challenge is to implement a method in the NotificationBuilder class that allows setting a custom LED color and blinking pattern for notifications. This will provide a visual cue for notifications when the device is face-down or in a pocket.

[T] Here's what you need to do:
[List]
1. Add a method to the NotificationBuilder class called withLedPattern that takes a Color, an onMs (milliseconds the LED is on), and an offMs (milliseconds the LED is off)
2. Update the notification model to store these values
3. Update the _buildNotificationDetails method to apply these settings to the AndroidNotificationDetails

[T] Take some time to implement this challenge before looking at the solution.

[H3] Solution
[T] Let's implement the withLedPattern method in the NotificationBuilder class:

[Code: dart]
/// Configures the notification with a custom LED blinking pattern.
///
/// Sets the LED color and blinking pattern for the notification.
///
/// [color] The color of the LED
/// [onMs] Milliseconds the LED should be on
/// [offMs] Milliseconds the LED should be off
NotificationBuilder withLedPattern(Color color, int onMs, int offMs) {
  // In a real implementation, we would update the model with these values
  // For this codelab, we'll just log the values
  debugPrint('Setting LED pattern: color=$color, onMs=$onMs, offMs=$offMs');
  return this;
}

[T] Then, we would update the _buildNotificationDetails method to apply these settings:

[Code: dart]
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  enableLights: true,
  ledColor: Colors.green, // Use the color from the model
  ledOnMs: 500, // Use the onMs from the model
  ledOffMs: 500, // Use the offMs from the model
  // Other settings...
);

[T] This implementation is simplified for the codelab. In a real app, you would need to update the NotificationModel class to store the LED color and blinking pattern, and then use those values when building the notification details.


Chapter Title
Testing Custom Notifications

[H2] Testing Custom Notifications
[T] In this chapter, we'll test our customized notifications to see how they behave with different settings.

[H3] Creating Notifications with Different Levels
[T] Let's create notifications with different levels to see how they behave:

[Code: dart]
// Low-priority notification
final lowBuilder = NotificationBuilder.create(
  NotificationManager(),
  id: 1,
  title: "Low Priority",
  body: "This is a low-priority notification",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.low,
);

await lowBuilder.show();

// Normal-priority notification
final normalBuilder = NotificationBuilder.create(
  NotificationManager(),
  id: 2,
  title: "Normal Priority",
  body: "This is a normal-priority notification",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
);

await normalBuilder.show();

// High-priority notification
final urgentBuilder = NotificationBuilder.create(
  NotificationManager(),
  id: 3,
  title: "Urgent Priority",
  body: "This is an urgent-priority notification",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.urgent,
);

await urgentBuilder.show();

[T] These notifications will have different behaviors based on their level:
[List]
Low: Silent notification that appears in the notification drawer without sound or vibration
Normal: Standard notification with sound and vibration
Urgent: High-priority notification that may appear as a heads-up notification

[H3] Testing Custom Sounds
[T] Let's create a notification with a custom sound:

[Code: dart]
final customSoundBuilder = NotificationBuilder.create(
  NotificationManager(),
  id: 4,
  title: "Custom Sound",
  body: "This notification has a custom sound",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.setCustomSound(true);

await customSoundBuilder.show();

[T] This notification will play a custom sound when it appears.

[H3] Testing Full-Screen Notifications
[T] Finally, let's test a full-screen notification:

[Code: dart]
final fullScreenBuilder = NotificationBuilder.create(
  NotificationManager(),
  id: 5,
  title: "Full-Screen Alert",
  body: "This is a full-screen notification",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.urgent,
)
.setFullScreen(true);

await fullScreenBuilder.show();

[T] To properly test this notification, you should lock your device after creating it. The notification should appear as a full-screen activity when it arrives.


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored various ways to customize notification behavior and priority:

[List]
We learned about the NotificationLevel enum and how it maps to platform-specific settings
We implemented custom sounds for notifications using channel variants
We configured full-screen intents for high-priority notifications
We tested notifications with different levels, sounds, and full-screen settings
We tackled a code challenge to implement LED blinking patterns

[T] Key concepts to remember:
[List]
Use NotificationLevel to set the appropriate importance and priority for notifications
Use custom sounds to help users distinguish between different types of notifications
Use full-screen intents for urgent notifications that require immediate attention
Consider using LED patterns for visual notification when the device is face-down or in a pocket

[T] With these customizations, your notifications will provide a better user experience by using the appropriate level of interruption for each type of alert.


Chapter Title
Quiz: Test Your Knowledge

[H2] Quiz: Test Your Knowledge
[T] Let's test your understanding of notification behavior and customization with a few questions:

[H3] Question 1
[T] A critical notification plays no sound despite withCustomSound() being set. What's likely the issue?

[List]
A) The NotificationLevel is set to low, which disables sound regardless of the customSound flag
B) The sound file is missing from the android/app/src/main/res/raw directory
C) The notification channel doesn't have sound enabled in the Android settings
D) The device is in Do Not Disturb mode

[H3] Question 2
[T] Which NotificationBuilder method would you use to create a notification that appears as a full-screen activity when the device is locked?

[List]
A) withHighPriority()
B) setFullScreen(true)
C) withLockScreenVisibility(NotificationVisibility.public)
D) asUrgent()

[H3] Question 3
[T] A high-priority notification doesn't vibrate on Android, though enableVibration: true is set. What should you check first?

[List]
A) Whether the device has vibration hardware
B) Whether the notification channel has vibration enabled in the Android settings
C) Whether the NotificationLevel.vibrate property is true
D) Whether the vibrationPattern is properly formatted

[H3] Answers
[T] Let's review the correct answers:

[List]
Question 1: A) The NotificationLevel is set to low, which disables sound regardless of the customSound flag. The NotificationLevel.low has playSound set to false, which overrides the customSound setting.

Question 2: B) setFullScreen(true). This method sets the isFullScreen flag on the notification model, which causes the notification to appear as a full-screen intent when displayed.

Question 3: C) Whether the NotificationLevel.vibrate property is true. Even if enableVibration is set to true in the AndroidNotificationDetails, the NotificationLevel.vibrate property must also be true for vibration to work.

[T] Up Next, we'll explore how to handle notification taps and manage notifications through deep links.


Write Your eBook

Section 6 eBook
Section Title
Handling and Managing Notifications


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll explore:
[List]
Understanding deep links and their role in notification handling
Implementing notification tap handling to navigate to specific screens
Setting up platform-specific configurations for deep links
Retrieving and displaying pending notifications
Canceling individual or all notifications
Maintaining a notification details screen for viewing and editing notifications

[T] These features will complete your notification system by providing ways for users to interact with notifications and manage them effectively. Just as a messaging app needs ways to view, respond to, and delete messages, your notification system needs ways to handle user interactions and manage notification lifecycle.

[T] Let's begin by understanding how deep links work with notifications!


Chapter Title
Understanding Deep Links in Notifications

[H2] Understanding Deep Links in Notifications
[T] In this chapter, we'll explore how deep links enable navigation to specific screens when users tap on notifications.

[H3] What Are Deep Links?
[T] Deep links are URLs that point to specific content within an app, rather than just launching the app. When a user taps a notification with a deep link, the app opens to a specific screen related to that notification.

[T] For example, a deep link like `timora:/notification-details?id=123` might open the details screen for notification #123.

[T] Deep links are essential for notifications because they:
[List]
Direct users to relevant content when they tap a notification
Provide context for notification interactions
Enable a seamless user experience from notification to app content

[H3] Deep Link Structure in Timora
[T] In our app, we use a custom URL scheme (`timora://`) for deep links. The structure of our deep links is:

[Code: dart]
// Example deep link for notification details
timora:/notification-details?id=123

// Example deep link for notification click handling
timora:/deeplink-click?uri=encoded_uri

[T] The first format directs users to a specific notification's details, while the second format is used for general notification tap handling with additional parameters.

[H3] The DeepLinkHandler Class
[T] The DeepLinkHandler class in `lib/core/util/deeplink/deeplink_handler.dart` is responsible for processing deep links and navigating to the appropriate screens:

[Code: dart]
/// Handles deep link navigation within the app
///
/// This handler supports two types of deeplinks with a centralized processing approach:
/// 1. External deeplinks - from browsers, other apps, etc. (using app_links package)
/// 2. Notification deeplinks - from tapping on notifications
class DeepLinkHandler {
  // Private constructor for singleton pattern
  DeepLinkHandler._();

  // Singleton instance
  static final DeepLinkHandler instance = DeepLinkHandler._();

  // App links instance for handling external deep links
  final AppLinks _appLinks = AppLinks();

  /// Initialize deep link handling - should be called on app startup
  void init() {
    _handleInitialLink();
    _listenForLinks();
  }

  /// Sets up a listener for incoming external deep links while the app is running
  void _listenForLinks() {
    _appLinks.uriLinkStream.listen(
      (Uri? uri) {
        if (uri != null) {
          _handleDeepLink(uri);
        }
      },
      onError: (err) {
        debugPrint('Error handling external deep link: $err');
      },
    );
  }

  /// Handle initial deep link when app is launched from an external link
  Future<void> _handleInitialLink() async {
    try {
      final Uri? initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        debugPrint('App launched from deeplink: $initialUri');
        _handleDeepLink(initialUri);
      }
    } catch (e) {
      debugPrint('Error handling initial deep link: $e');
    }
  }

  /// Process the deep link from external sources (browsers, other apps)
  void _handleDeepLink(Uri uri) {
    debugPrint('Processing external deeplink: $uri');
    // Use the shared processing logic
    processDeepLink(uri);
  }

  /// Handle a deeplink from a notification payload
  void handleNotificationDeeplink(String? deeplinkUri) {
    if (deeplinkUri == null) return;

    try {
      final uri = Uri.parse(deeplinkUri);
      debugPrint('Processing notification deeplink: $uri');
      // Use the shared processing logic
      processDeepLink(uri);
    } catch (e) {
      debugPrint('Error parsing notification deeplink URI: $e');
      // Navigate to home as fallback
      AppRouter.navigateTo('/');
    }
  }

  /// Shared processing logic for all deeplinks
  void processDeepLink(Uri uri) {
    try {
      final DeepLinkData linkData = _parseDeepLink(uri);
      debugPrint(
        'Navigating to: ${linkData.path} with arguments: ${linkData.arguments}',
      );

      // Use AppRouter's navigation method
      AppRouter.navigateTo(linkData.path, arguments: linkData.arguments);
    } catch (e) {
      debugPrint('Error processing deeplink: $e');
      // Navigate to home as fallback
      AppRouter.navigateTo('/');
    }
  }
}

[T] This class provides a centralized approach to handling deep links from both external sources (like browsers) and notifications. It uses a shared processing logic to ensure consistent behavior regardless of the source of the deep link.

[H3] Initializing Deep Link Handling
[T] Deep link handling is initialized in the `main.dart` file:

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize notification manager
    await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    // Error handling...
  }
}

[T] This ensures that the app can handle deep links from notifications as soon as it starts.


Chapter Title
Implementing Notification Tap Handling

[H2] Implementing Notification Tap Handling
[T] In this chapter, we'll implement the code to handle notification taps and navigate to the appropriate screens.

[H3] Handling Notification Taps
[T] When a user taps a notification, the flutter_local_notifications plugin calls the onDidReceiveNotificationResponse callback. In our app, we use a background callback handler for notification taps:

[Code: dart]
/// Callback handler for notification actions when app is in background.
@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  debugPrint(
    'notification(${notificationResponse.id}) action tapped: '
    '${notificationResponse.actionId} with'
    ' payload: ${notificationResponse.payload}',
  );
  if (notificationResponse.input?.isNotEmpty ?? false) {
    debugPrint(
      'notification action tapped with input: ${notificationResponse.input}',
    );
  }

  // Handle notification tap by navigating to the appropriate screen
  if (notificationResponse.payload != null) {
    try {
      final notificationModel = NotificationModel.fromPayload(
        notificationResponse.payload!,
      );

      // If the notification has a deeplink, use it
      if (notificationModel.deepLink != null) {
        debugPrint(
          'Notification tapped with deeplink: ${notificationModel.deepLink}',
        );
        // Use the DeepLinkHandler to process the deeplink
        DeepLinkHandler.instance.handleNotificationDeeplink(
          notificationModel.deepLink,
        );
      } else {
        // Fallback to direct navigation if no deeplink is provided
        debugPrint(
          'Notification tapped without deeplink, using ID: ${notificationModel.id}',
        );
        AppRouter.navigateTo(
          AppRoutes.notificationDetails.value,
          arguments: notificationModel.id,
        );
      }
    } catch (e) {
      debugPrint('Error processing notification payload: $e');
    }
  }
}

[T] This callback is registered in the _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  // ...

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This code parses the notification payload, extracts the NotificationModel, and either handles the deep link or navigates to the notification details screen.

[H3] Adding Default Deep Links to Notifications
[T] In our app, we automatically add a default deep link to every notification if one isn't already set. This is done in the showInstantNotification and scheduleNotification methods of the NotificationManager class:

[Code: dart]
/// Shows an instant notification immediately.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
              deepLink:
                  NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                    model.id,
                  ),
            )
          : model;

  // Configure notification details and show the notification...
}

[T] The same pattern is used in the scheduleNotification method:

[Code: dart]
/// Schedules a notification for a specific future time.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  // Check prerequisites...

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
              deepLink:
                  NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                    model.id,
                  ),
            )
          : model;

  // Configure notification details and schedule the notification...
}

[T] With these changes, every notification will have a deep link that directs users to the notification details screen when tapped.

[H3] Platform-Specific Deep Link Setup
[T] For deep links to work, we need to configure both Android and iOS:

[T] For Android, add the following to `android/app/src/main/AndroidManifest.xml`:

[Code: xml]
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTop"
    android:theme="@style/LaunchTheme"
    android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
    android:hardwareAccelerated="true"
    android:windowSoftInputMode="adjustResize">
    <!-- ... -->
    <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="timora" />
    </intent-filter>
</activity>

[T] For iOS, add the following to `ios/Runner/Info.plist`:

[Code: xml]
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>com.example.timora</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>timora</string>
        </array>
    </dict>
</array>

[T] These configurations enable the operating system to launch our app when a deep link with the `timora://` scheme is activated.


Chapter Title
Managing Notifications

[H2] Managing Notifications
[T] In this chapter, we'll implement methods to retrieve, display, and cancel notifications.

[H3] Retrieving Pending Notifications
[T] To display a list of pending notifications, we need a method to retrieve them. Let's implement the getPendingNotifications method in the NotificationManager class:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }

    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'No Title',
      body: request.body ?? 'No Message',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, with fallback handling for notifications with invalid or missing payloads.

[H3] Canceling Notifications
[T] Users need the ability to cancel notifications they no longer want. Let's implement methods to cancel individual notifications and all notifications:

[Code: dart]
/// Cancels a specific notification by ID.
///
/// Removes both active and pending notifications with the specified [id].
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
}

/// Cancels all active and scheduled notifications.
///
/// Removes all notifications created by the application.
Future<void> cancelAllNotifications() async {
  await _flutterLocalNotificationsPlugin.cancelAll();
}

[T] These methods use the flutter_local_notifications plugin to cancel notifications by ID or cancel all notifications.

[H3] Displaying Pending Notifications
[T] In our app, we display pending notifications directly on the home page. Let's implement this functionality in the HomePage class:

[Code: dart]
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _HomePageContent();
  }
}

class _HomePageContent extends StatefulWidget {
  const _HomePageContent();

  @override
  State<_HomePageContent> createState() => _HomePageContentState();
}

class _HomePageContentState extends State<_HomePageContent> {
  final NotificationManager _notificationManager = NotificationManager();
  List<NotificationModel> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// Loads all pending notifications from the notification manager.
  Future<void> _loadNotifications() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      final notifications =
          await _notificationManager.getPendingNotifications();

      if (mounted) {
        setState(() {
          _notifications = notifications;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('Failed to load notifications: ${e.toString()}');
      }
    }
  }

  /// Shows an error message to the user.
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  /// Deletes a notification with the given ID.
  Future<void> _deleteNotification(int notificationId) async {
    try {
      await _notificationManager.cancelNotification(notificationId);
      await _loadNotifications();
    } catch (e) {
      _showErrorSnackBar('Failed to delete notification: ${e.toString()}');
    }
  }

  /// Navigates to the notification details screen.
  void _navigateToDetails(int notificationId) {
    Navigator.pushNamed(
      context,
      AppRoutes.notificationDetails.value,
      arguments: notificationId,
    ).then((_) => _loadNotifications());
  }

  /// Handles the pull-to-refresh gesture.
  Future<void> _handleRefresh() async {
    return _loadNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Timora')),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.pushNamed(
          context,
          AppRoutes.createNotification.value,
        ).then((_) => _loadNotifications()),
        label: const Text('Add Reminder'),
        icon: const Icon(Icons.add),
      ),
      body: _buildBody(),
    );
  }

  /// Builds the main body content based on the loading state.
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: _NotificationListView(
        notifications: _notifications,
        onEdit: _navigateToDetails,
        onDelete: _deleteNotification,
      ),
    );
  }
}

/// Displays either the notification list or an empty state view.
class _NotificationListView extends StatelessWidget {
  final List<NotificationModel> notifications;
  final ValueChanged<int> onEdit;
  final ValueChanged<int> onDelete;

  const _NotificationListView({
    required this.notifications,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    if (notifications.isEmpty) {
      return const Center(
        child: Text('No reminders scheduled'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(notification.title),
            subtitle: Text(notification.body),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => onEdit(notification.id),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => onDelete(notification.id),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

[T] This implementation displays pending notifications directly on the home page, with options to refresh the list, edit or delete individual notifications, and navigate to the notification details screen. It also shows an empty state when there are no notifications.


Chapter Title
Implementing the Notification Details Page

[H2] Implementing the Notification Details Page
[T] In this chapter, we'll implement a details page that allows users to view, edit, and delete notifications.

[H3] Setting Up the Notification Details Page
[T] Let's create a NotificationDetailsPage that displays the details of a notification and allows users to edit or delete it:

[Code: dart]
class NotificationDetailsPage extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsPage({
    Key? key,
    required this.notificationId,
  }) : super(key: key);

  @override
  _NotificationDetailsPageState createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  final NotificationManager _manager = NotificationManager();
  NotificationModel? _notification;
  bool _isLoading = true;
  bool _isEditing = false;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  DateTime? _scheduledDateTime;

  @override
  void initState() {
    super.initState();
    _loadNotification();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    super.dispose();
  }

  Future<void> _loadNotification() async {
    setState(() {
      _isLoading = true;
    });

    final notifications = await _manager.getPendingNotifications();
    final notification = notifications.firstWhere(
      (n) => n.id == widget.notificationId,
      orElse: () => NotificationModel(
        id: widget.notificationId,
        title: 'Unknown Notification',
        body: 'This notification could not be found.',
        channelId: NotificationChannelIds.defaultChannel,
        type: NotificationType.instant,
        level: NotificationLevel.normal,
      ),
    );

    setState(() {
      _notification = notification;
      _isLoading = false;

      // Initialize form controllers
      _titleController.text = notification.title;
      _bodyController.text = notification.body;
      _scheduledDateTime = notification.scheduledTime;
    });
  }

  Future<void> _updateNotification() async {
    if (!_formKey.currentState!.validate()) return;

    final updatedNotification = _notification!.copyWith(
      title: _titleController.text,
      body: _bodyController.text,
      scheduledTime: _scheduledDateTime,
    );

    // Cancel the existing notification
    await _manager.cancelNotification(_notification!.id);

    // Show the updated notification
    if (updatedNotification.type == NotificationType.scheduled) {
      await _manager.scheduleNotification(model: updatedNotification);
    } else {
      await _manager.showInstantNotification(model: updatedNotification);
    }

    setState(() {
      _notification = updatedNotification;
      _isEditing = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification updated')),
    );
  }

  Future<void> _deleteNotification() async {
    await _manager.cancelNotification(_notification!.id);
    Navigator.pop(context);
  }

  Future<void> _pickDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _scheduledDateTime ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(
          _scheduledDateTime ?? DateTime.now(),
        ),
      );

      if (time != null && mounted) {
        setState(() {
          _scheduledDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Notification Details')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _updateNotification,
            ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteNotification,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isEditing ? _buildEditForm() : _buildDetailsView(),
      ),
    );
  }

  Widget _buildDetailsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Title',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(_notification!.title),
        const SizedBox(height: 16),
        Text(
          'Body',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(_notification!.body),
        const SizedBox(height: 16),
        if (_notification!.scheduledTime != null) ...[
          Text(
            'Scheduled Time',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            DateFormat('MMM d, yyyy - h:mm a').format(_notification!.scheduledTime!),
          ),
        ],
        const SizedBox(height: 16),
        Text(
          'Channel',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(_notification!.channelId),
        const SizedBox(height: 16),
        Text(
          'Priority',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(_notification!.level.name),
      ],
    );
  }

  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a title';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _bodyController,
            decoration: const InputDecoration(
              labelText: 'Body',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a body';
              }
              return null;
            },
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          if (_notification!.type == NotificationType.scheduled) ...[
            Text(
              'Scheduled Time',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  _scheduledDateTime != null
                      ? DateFormat('MMM d, yyyy - h:mm a').format(_scheduledDateTime!)
                      : 'Not scheduled',
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _pickDateTime,
                  child: const Text('Change'),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

[T] This page allows users to view the details of a notification, edit its title and body, and reschedule it if it's a scheduled notification. It also provides options to delete the notification.

[H3] Registering the Route
[T] To make the notification details page accessible, we need to register it in the app's router. Let's update the AppRouter class:

[Code: dart]
/// Handles app navigation using Navigator 1.0
class AppRouter {
  const AppRouter._();

  /// Global navigator key for accessing the navigator from anywhere
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  /// Navigate to a named route with optional arguments
  static void navigateTo(
    String routeName, {
    Object? arguments,
    bool replace = false,
  }) {
    final navigator = navigatorKey.currentState;
    if (navigator != null) {
      debugPrint('Navigating to: $routeName');

      if (replace) {
        navigator.pushReplacementNamed(routeName, arguments: arguments);
      } else {
        navigator.pushNamed(routeName, arguments: arguments);
      }
    } else {
      debugPrint('Navigator state not available');
    }
  }

  /// Generate routes for the app
  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    if (routeSettings.name == null) {
      return MaterialPageRoute(
        settings: routeSettings,
        builder: (context) => const Scaffold(
          body: Center(child: Text('Route name is null')),
        ),
      );
    }

    final route = AppRoutes.fromName(routeSettings.name!);

    switch (route) {
      // Other routes...

      case AppRoutes.notificationDetails:
        final notificationId = routeSettings.arguments as int;
        return MaterialPageRoute(
          settings: routeSettings,
          builder: (context) => NotificationDetailsPage(notificationId: notificationId),
        );

      // Other routes...
    }
  }
}

[T] With this route registered, users can navigate to the notification details page by tapping on a notification or by selecting a notification from the pending notifications list.


Chapter Title
Testing Notification Handling and Management

[H2] Testing Notification Handling and Management
[T] In this chapter, we'll test our notification handling and management features to ensure they work correctly.

[H3] Testing Deep Link Handling
[T] Let's create a notification with a deep link and test that tapping it navigates to the correct screen:

[Code: dart]
// Create a notification with a deep link
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: "Deep Link Test",
  body: "Tap to test deep link handling",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.setDefaultDeepLink();

await builder.show();

[T] When you tap this notification, it should navigate to the notification details screen for notification #123.

[H3] Testing Notification Management
[T] Let's test retrieving and canceling notifications by creating a simple test function:

[Code: dart]
Future<void> testNotificationManagement() async {
  final notificationManager = NotificationManager();

  // Cancel any existing notifications first
  await notificationManager.cancelAllNotifications();

  // Create a few test notifications
  for (int i = 1; i <= 3; i++) {
    final builder = NotificationBuilder.create(
      notificationManager,
      id: i,
      title: "Test Notification $i",
      body: "This is test notification $i",
      channelId: NotificationChannelIds.defaultChannel,
      level: NotificationLevel.normal,
    );

    await builder.show();
    debugPrint('Created notification $i');
  }

  // Retrieve pending notifications
  final pendingNotifications = await notificationManager.getPendingNotifications();
  debugPrint('Pending notifications: ${pendingNotifications.length}');

  // Display notification details
  for (final notification in pendingNotifications) {
    debugPrint('Notification ${notification.id}: ${notification.title}');
  }

  // Cancel a specific notification
  await notificationManager.cancelNotification(2);
  debugPrint('Cancelled notification 2');

  // Retrieve pending notifications again
  final remainingNotifications = await notificationManager.getPendingNotifications();
  debugPrint('Remaining notifications: ${remainingNotifications.length}');

  // Display remaining notification details
  for (final notification in remainingNotifications) {
    debugPrint('Notification ${notification.id}: ${notification.title}');
  }
}

[T] This function creates three test notifications, retrieves them, cancels one, and retrieves the remaining ones. You can call this function from anywhere in your app to test notification management functionality.

[H3] Testing the Notification Details Page
[T] To test the notification details page, create a notification and navigate to its details page:

[Code: dart]
// Create a test notification
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 456,
  title: "Details Test",
  body: "Tap to view notification details",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
);

await builder.show();

// Navigate to the notification details page
Navigator.pushNamed(
  context,
  AppRoutes.notificationDetails.value,
  arguments: 456,
);

[T] This should display the notification details page for notification #456, where you can view, edit, or delete the notification.


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored how to handle notification taps and manage notifications:

[List]
We learned about deep links and their role in notification handling
We implemented notification tap handling to navigate to specific screens
We set up platform-specific configurations for deep links
We created methods to retrieve and cancel notifications
We implemented a notification details page for viewing and editing notifications

[T] Key concepts to remember:
[List]
Deep links enable navigation to specific screens when users tap notifications
The DeepLinkHandler class processes deep links and navigates to the appropriate screens
The NotificationManager class provides methods to retrieve and cancel notifications
The NotificationDetailsPage allows users to view, edit, and delete notifications

[T] With these features, your notification system is now complete. Users can create notifications, receive them at the appropriate times, and manage them effectively.

[T] Up Next, we'll explore how to test your notification system to ensure it works correctly in different scenarios.


Chapter Title
Quiz: Test Your Knowledge

[H2] Quiz: Test Your Knowledge
[T] Let's test your understanding of notification handling and management with a few questions:

[H3] Question 1
[T] What is the purpose of deep links in notifications?

[List]
A) To make notifications appear on the lock screen
B) To direct users to specific screens when they tap a notification
C) To schedule notifications for a future time
D) To customize the notification sound

[H3] Question 2
[T] Which method in the NotificationManager class is used to retrieve pending notifications?

[List]
A) getPendingNotifications()
B) retrieveNotifications()
C) getActiveNotifications()
D) listPendingNotifications()

[H3] Question 3
[T] What happens when a user taps a notification with a deep link?

[List]
A) The notification is automatically canceled
B) The app opens to the home screen
C) The DeepLinkHandler processes the URI and navigates to the appropriate screen
D) The notification is rescheduled for a later time

[H3] Answers
[T] Let's review the correct answers:

[List]
Question 1: B) To direct users to specific screens when they tap a notification. Deep links are URLs that point to specific content within an app, allowing for direct navigation when a notification is tapped.

Question 2: A) getPendingNotifications(). This method retrieves all pending notification requests and converts them to NotificationModel objects.

Question 3: C) The DeepLinkHandler processes the URI and navigates to the appropriate screen. When a notification with a deep link is tapped, the _handleNotificationTap method extracts the URI and passes it to the DeepLinkHandler.

[T] Up Next, we'll explore how to test your notification system to ensure it works correctly in different scenarios.




Write Your eBook

Section 7 eBook
Section Title
Enhancing Notifications with Advanced Features


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll explore advanced notification features that take your app's user experience to the next level. Just as a skilled communicator uses different techniques to convey different types of messages, your app can use these advanced features to create more engaging and informative notifications.

[T] In this section, you'll explore:
[List]
Display notifications with progress indicators for tasks like downloads or uploads
Group multiple notifications into stacks to reduce notification drawer clutter
Add interactive actions like buttons to let users respond directly from notifications
Display media-rich notifications with images to provide visual context

[T] These advanced features will help your notifications stand out and provide users with more ways to interact with your app without even opening it. Let's dive in and explore these powerful capabilities!


Chapter Title
Implementing Progress Notifications

[H2] Implementing Progress Notifications
[T] In this chapter, we'll implement progress notifications that show real-time updates for ongoing operations like downloads, uploads, or processing tasks. Think of these as the digital equivalent of a loading bar or progress meter that keeps users informed about lengthy operations.

[H3] Understanding Progress Notifications
[T] Progress notifications are particularly useful for:
[List]
File downloads or uploads
Data synchronization operations
Installation or update processes
Long-running background tasks

[T] On Android, progress notifications can display an actual progress bar, while on iOS (which doesn't natively support progress bars in notifications), we'll update the notification text to indicate progress.

[H3] Enhancing the NotificationModel
[T] Our NotificationModel already includes properties for tracking progress:

[Code: dart]
class NotificationModel {
  // Other properties...
  final int? maxProgress;
  final int? currentProgress;

  // Constructor and other methods...
}

[T] These properties allow us to store the maximum value and current value of the progress, which we'll use to calculate the percentage and display the progress bar.

[H3] Implementing the Progress Notification Method
[T] Let's implement the showProgressNotification method in the NotificationManager class:

[Code: dart]
/// Shows a notification with a progress indicator.
///
/// Displays a notification showing progress toward completion of a task,
/// using the values in [model.currentProgress] and [model.maxProgress].
///
/// Throws an [ArgumentError] if progress values are not provided.
Future<void> showProgressNotification({
  required NotificationModel model,
}) async {
  if (model.maxProgress == null || model.currentProgress == null) {
    throw ArgumentError(
      'maxProgress and currentProgress must be provided for progress notifications',
    );
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show progress notification: prerequisites not met');
    return;
  }

  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    importance: Importance.low,
    priority: Priority.low,
    onlyAlertOnce: true,
    showProgress: true,
    maxProgress: model.maxProgress!,
    progress: model.currentProgress!,
  );

  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: false,
  );

  NotificationDetails details = NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );

  await _flutterLocalNotificationsPlugin.show(
    model.id,
    model.title,
    model.body,
    details,
    payload: model.toPayload(),
  );
}

[T] This method configures a notification with a progress bar on Android. Notice a few important settings:
[List]
importance: Importance.low and priority: Priority.low - Progress notifications are typically less intrusive
onlyAlertOnce: true - This prevents the notification from making a sound every time it updates
showProgress: true - This enables the progress bar on Android
maxProgress and progress - These set the maximum value and current value of the progress bar

[H3] Enhancing the NotificationBuilder
[T] Now, let's enhance the NotificationBuilder to make it easier to create progress notifications:

[Code: dart]
/// Configures the notification to display a progress indicator.
///
/// Shows a progress bar in the notification, useful for download or
/// processing tasks.
///
/// [current] Current progress value
/// [max] Maximum progress value
///
/// Throws an [ArgumentError] if values are invalid.
NotificationBuilder setProgress(int current, int max) {
  if (current < 0) {
    throw ArgumentError('Current progress cannot be negative');
  }
  if (max <= 0) {
    throw ArgumentError('Maximum progress must be positive');
  }
  if (current > max) {
    throw ArgumentError('Current progress cannot exceed maximum');
  }
  _model = _model.copyWith(currentProgress: current, maxProgress: max);
  return this;
}

[T] This method validates the progress values and updates the notification model. It ensures that the progress values make sense (non-negative current, positive maximum, current not exceeding maximum).

[H3] Creating a Progress Notification
[T] Let's create a helper method in our controller to create progress notifications:

[Code: dart]
// Progress notification setup
NotificationBuilder createProgressNotification({
  required int progress,
  required int maxProgress,
}) {
  final id = DateTime.now().millisecondsSinceEpoch % 10000;
  final bool isComplete = progress >= maxProgress;

  final title = isComplete ? 'Download Complete' : 'Download Progress';
  final body =
      isComplete
          ? 'File ready to open'
          : 'Downloading: ${(progress / maxProgress * 100).round()}%';

  return notificationManager
      .createNotification(
        id: id,
        title: title,
        body: body,
        channelId: value.channelId,
        level: NotificationLevel.normal,
      )
      .setProgress(progress, maxProgress);
}

[T] This method creates a notification with a title and body that reflect the current progress. It also sets the progress values using the setProgress method.

[H3] Updating Progress
[T] To update a progress notification, we simply show a new notification with the same ID but updated progress values:

[Code: dart]
// Example of updating progress
Future<void> updateDownloadProgress() async {
  int maxProgress = 100;

  // Initial notification
  var builder = createProgressNotification(progress: 0, maxProgress: maxProgress);
  await builder.show();

  // Simulate progress updates
  for (int i = 10; i <= maxProgress; i += 10) {
    await Future.delayed(const Duration(seconds: 1));
    builder = createProgressNotification(progress: i, maxProgress: maxProgress);
    await builder.show();
  }
}

[T] This example simulates a download that takes 10 seconds to complete, updating the progress every second. In a real app, you would update the progress based on actual download progress events.

[T] Progress notifications are a powerful way to keep users informed about ongoing operations. They provide visual feedback that helps users understand what's happening and how long they might need to wait.


Chapter Title
Implementing Grouped Notifications

[H2] Implementing Grouped Notifications
[T] In this chapter, we'll implement grouped notifications, which allow you to bundle related notifications together to reduce clutter in the notification drawer. Think of this like organizing related emails into a conversation thread instead of showing each message separately.

[H3] Understanding Notification Groups
[T] Notification groups are particularly useful for:
[List]
Messaging apps that might receive multiple messages from the same conversation
Social media apps that receive multiple interactions (likes, comments, etc.)
News apps that receive multiple updates on related topics
Any app that might generate multiple related notifications in a short time

[T] On Android, grouped notifications appear as a single expandable item with a summary, while on iOS, they appear as a stack that users can swipe through.

[H3] Implementing the Group Notification Method
[T] Let's implement the showGroupedNotifications method in the NotificationManager class:

[Code: dart]
/// Shows a group of related notifications with a summary.
///
/// Creates individual notifications for each item in [notifications] and
/// a summary notification that groups them together (Android only).
///
/// [groupKey] A unique identifier for this group of notifications
/// [groupChannelId] The channel ID for the notifications
/// [summaryTitle] The title for the summary notification
/// [summaryBody] The body text for the summary notification
Future<void> showGroupedNotifications({
  required String groupKey,
  required String groupChannelId,
  required String summaryTitle,
  required String summaryBody,
  required List<NotificationModel> notifications,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show grouped notifications: prerequisites not met');
    return;
  }

  // Create individual notifications
  for (final notification in notifications) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: false,
    );

    DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      categoryIdentifier: groupChannelId,
      threadIdentifier: groupKey,
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      notification.id,
      notification.title,
      notification.body,
      details,
      payload: notification.toPayload(),
    );
  }

  // Create summary notification for Android
  if (Platform.isAndroid) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: true,
      styleInformation: InboxStyleInformation(
        notifications.map((n) => n.body).toList(),
        contentTitle: summaryTitle,
        summaryText: '${notifications.length} messages',
      ),
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0, // Use a fixed ID for the summary
      summaryTitle,
      summaryBody,
      details,
    );
  }
}

[T] This method does two important things:
[List]
Creates individual notifications for each item in the notifications list, with groupKey for Android and threadIdentifier for iOS to link them together
Creates a summary notification for Android that displays a count and summary of the individual notifications

[H3] Creating a Helper Method for Group Notifications
[T] Let's create a helper method to make it easier to create grouped notifications:

[Code: dart]
/// Creates a group of notifications with a summary.
///
/// Converts the list of [notifications] to models and calls
/// [showGroupedNotifications] to display them.
Future<void> createGroupNotification({
  required String groupKey,
  required String channelId,
  required String groupTitle,
  required String groupSummary,
  required List<NotificationBuilder> notifications,
}) async {
  List<NotificationModel> models =
      notifications.map((builder) => builder.model).toList();
  return await showGroupedNotifications(
    groupKey: groupKey,
    groupChannelId: channelId,
    summaryTitle: groupTitle,
    summaryBody: groupSummary,
    notifications: models,
  );
}

[T] This method takes a list of NotificationBuilder instances, extracts their models, and passes them to the showGroupedNotifications method.

[H3] Creating a Message Group Example
[T] Let's create an example method that shows a group of message notifications:

[Code: dart]
/// Shows a group of message notifications as an example.
Future<void> showMessageGroup() async {
  final String groupKey = 'com.example.timora.MESSAGE_GROUP';
  final String channelId = NotificationChannelIds.personal;

  // Create individual message notifications
  List<NotificationBuilder> notifications = [
    createNotification(
      id: 1,
      title: 'John Doe',
      body: 'Hey, how are you doing?',
      channelId: channelId,
    ),
    createNotification(
      id: 2,
      title: 'John Doe',
      body: 'Are we still meeting tomorrow?',
      channelId: channelId,
    ),
    createNotification(
      id: 3,
      title: 'John Doe',
      body: 'I have some updates to share',
      channelId: channelId,
    ),
  ];

  await createGroupNotification(
    notifications: notifications,
    groupKey: groupKey,
    channelId: channelId,
    groupTitle: 'John Doe',
    groupSummary: '3 new messages',
  );
}

[T] This method creates a group of message notifications from the same sender, demonstrating how to use grouped notifications for messaging apps.

[T] Grouped notifications are a powerful way to organize related notifications and reduce clutter in the notification drawer. They provide a better user experience by showing a summary of related notifications instead of individual alerts.


Chapter Title
Implementing Interactive Notifications

[H2] Implementing Interactive Notifications
[T] In this chapter, we'll implement interactive notifications that allow users to take actions directly from the notification without opening the app. Think of these as mini-interfaces that extend your app's functionality into the notification drawer.

[H3] Understanding Interactive Notifications
[T] Interactive notifications are particularly useful for:
[List]
Responding to messages without opening the messaging app
Snoozing or dismissing reminders
Accepting or declining invitations
Completing simple tasks like marking items as done

[T] Both Android and iOS support interactive notifications, but they implement them differently. On Android, actions appear as buttons below the notification, while on iOS, they appear when the user swipes left on the notification.

[H3] Setting Up Notification Actions
[T] First, let's set up the constants for our notification actions:

[Code: dart]
/// Notification action IDs
class NotificationActionIds {
  static const String snooze = 'snooze';
  static const String dismiss = 'dismiss';
  static const String reply = 'reply';
}

/// Notification action texts
class NotificationActionTexts {
  static const String snooze = 'Snooze';
  static const String dismiss = 'Dismiss';
  static const String reply = 'Reply';
}

/// Notification categories
class NotificationCategories {
  static const String interactive = 'INTERACTIVE_CATEGORY';
}

[T] These constants define the IDs and display texts for our notification actions, as well as a category for interactive notifications.

[H3] Setting Up iOS Notification Categories
[T] For iOS, we need to set up notification categories with actions. Let's update the _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  final snoozeAction = DarwinNotificationAction.plain(
    NotificationActionIds.snooze,
    NotificationActionTexts.snooze,
    options: {DarwinNotificationActionOption.foreground},
  );
  final dismissAction = DarwinNotificationAction.plain(
    NotificationActionIds.dismiss,
    NotificationActionTexts.dismiss,
    options: {DarwinNotificationActionOption.foreground},
  );
  final replyAction = DarwinNotificationAction.text(
    NotificationActionIds.reply,
    NotificationActionTexts.reply,
    buttonTitle: NotificationActionTexts.reply,
  );

  final interactiveCategory = DarwinNotificationCategory(
    NotificationCategories.interactive,
    actions: [snoozeAction, dismissAction, replyAction],
  );

  final iosSettings = DarwinInitializationSettings(
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
    notificationCategories: [interactiveCategory],
  );

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This method sets up an interactive category for iOS with three actions: snooze, dismiss, and reply. The reply action includes a text input field for the user to enter a response.

[H3] Updating the Notification Details Configuration
[T] Now, let's update the getNotificationDetailsConfig method to include actions for Android:

[Code: dart]
// Configure Android specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  styleInformation:
      imageAttachment
          ? BigPictureStyleInformation(
            ByteArrayAndroidBitmap(imageBytes),
            largeIcon: ByteArrayAndroidBitmap(imageBytes),
          )
          : null,
  actions: hasActions
      ? [
          const AndroidNotificationAction(
            NotificationActionIds.snooze,
            NotificationActionTexts.snooze,
          ),
          const AndroidNotificationAction(
            NotificationActionIds.dismiss,
            NotificationActionTexts.dismiss,
            cancelNotification: true,
          ),
          const AndroidNotificationAction(
            NotificationActionIds.reply,
            NotificationActionTexts.reply,
            inputs: [
              AndroidNotificationActionInput(
                label: NotificationActionTexts.reply,
                allowFreeFormInput: true,
              ),
            ],
          ),
        ]
      : null,
);

// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  attachments:
      imageAttachment
          ? [
            DarwinNotificationAttachment(
              filePath,
              thumbnailClippingRect:
                  const DarwinNotificationAttachmentThumbnailClippingRect(
                    x: 0.0,
                    y: 0.0,
                    width: 0.3,
                    height: 0.3,
                  ),
            ),
          ]
          : null,
);

[T] This code adds action buttons to Android notifications and sets the appropriate category for iOS notifications. For Android, we add "Snooze", "Dismiss", and "Reply" buttons, with the "Dismiss" button configured to automatically cancel the notification when tapped.

[H3] Enhancing the NotificationBuilder
[T] Let's enhance the NotificationBuilder to make it easier to create interactive notifications:

[Code: dart]
/// Adds interactive action buttons to the notification.
///
NotificationBuilder setActions() {
  _model = _model.copyWith(hasActions: true);
  return this;
}

[T] This method sets the hasActions flag on the notification model, which will cause the notification to include action buttons when displayed.

[H3] Handling Action Responses
[T] When a user taps an action button, we need to handle the response. Let's update the notificationTapBackground callback:

[Code: dart]
/// Callback handler for notification actions when app is in background.
@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  debugPrint(
    'notification(${notificationResponse.id}) action tapped: '
    '${notificationResponse.actionId} with'
    ' payload: ${notificationResponse.payload}',
  );
  if (notificationResponse.input?.isNotEmpty ?? false) {
    debugPrint(
      'notification action tapped with input: ${notificationResponse.input}',
    );
  }

  // Handle notification tap by navigating to the appropriate screen
  if (notificationResponse.payload != null) {
    try {
      final notificationModel = NotificationModel.fromPayload(
        notificationResponse.payload!,
      );

      // Handle specific actions
      if (notificationResponse.actionId != null) {
        _handleNotificationAction(
          notificationResponse.actionId!,
          notificationModel,
          notificationResponse.input,
        );
        return;
      }

      // If no specific action, handle the notification tap
      // If the notification has a deeplink, use it
      if (notificationModel.deepLink != null) {
        debugPrint(
          'Notification tapped with deeplink: ${notificationModel.deepLink}',
        );
        // Use the DeepLinkHandler to process the deeplink
        DeepLinkHandler.instance.handleNotificationDeeplink(
          notificationModel.deepLink,
        );
      } else {
        // Fallback to direct navigation if no deeplink is provided
        debugPrint(
          'Notification tapped without deeplink, using ID: ${notificationModel.id}',
        );
        AppRouter.navigateTo(
          AppRoutes.notificationDetails.value,
          arguments: notificationModel.id,
        );
      }
    } catch (e) {
      debugPrint('Error processing notification payload: $e');
    }
  }
}

/// Handles notification action taps.
///
/// Processes different actions based on the actionId.
void _handleNotificationAction(
  String actionId,
  NotificationModel model,
  String? input,
) {
  switch (actionId) {
    case NotificationActionIds.snooze:
      _handleSnoozeAction(model);
      break;
    case NotificationActionIds.dismiss:
      _handleDismissAction(model);
      break;
    case NotificationActionIds.reply:
      _handleReplyAction(model, input);
      break;
    default:
      debugPrint('Unknown action: $actionId');
  }
}

/// Handles the snooze action.
///
/// Reschedules the notification for a later time.
void _handleSnoozeAction(NotificationModel model) {
  debugPrint('Handling snooze action for notification ${model.id}');
  // In a real app, you would reschedule the notification for a later time
  // For this example, we'll just log the action
}

/// Handles the dismiss action.
///
/// Cancels the notification.
void _handleDismissAction(NotificationModel model) {
  debugPrint('Handling dismiss action for notification ${model.id}');
  // The notification is automatically canceled by the system when the dismiss action is tapped
  // with cancelNotification: true, but we could perform additional cleanup here
}

/// Handles the reply action.
///
/// Processes the user's reply text.
void _handleReplyAction(NotificationModel model, String? input) {
  debugPrint('Handling reply action for notification ${model.id}');
  if (input != null && input.isNotEmpty) {
    debugPrint('Reply text: $input');
    // In a real app, you would send the reply to the appropriate destination
    // For this example, we'll just log the reply
  }
}

[T] This code handles different notification actions based on the actionId. It includes separate methods for handling snooze, dismiss, and reply actions, which you can customize based on your app's needs.

[H3] Creating an Interactive Notification
[T] Let's create an example method that shows an interactive notification:

[Code: dart]
/// Shows an interactive notification as an example.
Future<void> showInteractiveNotification() async {
  final builder = createNotification(
    id: 789,
    title: 'Meeting Reminder',
    body: 'Team meeting in 15 minutes',
    channelId: NotificationChannelIds.work,
    level: NotificationLevel.high,
  ).setActions();

  await builder.show();
}

[T] This method creates a notification with interactive actions, allowing users to snooze or dismiss the reminder directly from the notification.

[T] Interactive notifications are a powerful way to extend your app's functionality into the notification drawer. They allow users to take actions without opening the app, providing a more seamless and efficient user experience.


Chapter Title
Implementing Media-Rich Notifications

[H2] Implementing Media-Rich Notifications
[T] In this chapter, we'll implement media-rich notifications that include images to provide visual context. Think of these as enhanced notifications that catch the user's eye and convey information more effectively through visuals.

[H3] Understanding Media-Rich Notifications
[T] Media-rich notifications are particularly useful for:
[List]
Showing a preview of a received image
Displaying a map for location-based notifications
Showing a product image for e-commerce notifications
Enhancing news or social media notifications with relevant visuals

[T] Both Android and iOS support media-rich notifications, but they implement them differently. On Android, images are displayed using BigPictureStyleInformation, while on iOS, they are displayed using DarwinNotificationAttachment.

[H3] Preparing Images for Notifications
[T] To use images in notifications, we need to prepare them in the appropriate format. For simplicity, we'll use a sample image encoded as base64:

[Code: dart]
final Uint8List imageBytes = base64Decode(
  NotificationUtils.sampleImageBase64,
);

// Get the temporary directory using path_provider
final Directory tempDir = await getTemporaryDirectory();
final String filePath = '${tempDir.path}/notification_image.png';
final File imageFile = File(filePath);

// Only write the file if it doesn't already exist
if (!await imageFile.exists()) {
  await imageFile.writeAsBytes(imageBytes);
}

[T] This code decodes a base64-encoded image and saves it to a temporary file, which we'll use for the notification. In a real app, you might download an image from a URL or use an image from the app's assets.

[H3] Updating the Notification Details Configuration
[T] Now, let's update the getNotificationDetailsConfig method to include images:

[Code: dart]
// Configure Android specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  styleInformation:
      imageAttachment
          ? BigPictureStyleInformation(
            ByteArrayAndroidBitmap(imageBytes),
            largeIcon: ByteArrayAndroidBitmap(imageBytes),
          )
          : null,
  // Other settings...
);

// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  attachments:
      imageAttachment
          ? [
            DarwinNotificationAttachment(
              filePath,
              thumbnailClippingRect:
                  const DarwinNotificationAttachmentThumbnailClippingRect(
                    x: 0.0,
                    y: 0.0,
                    width: 0.3,
                    height: 0.3,
                  ),
            ),
          ]
          : null,
  // Other settings...
);

[T] This code adds an image to the notification if the imageAttachment flag is set. For Android, it uses BigPictureStyleInformation with the image as both the main image and the large icon. For iOS, it uses DarwinNotificationAttachment with the image file path.

[H3] Enhancing the NotificationBuilder
[T] Let's enhance the NotificationBuilder to make it easier to create media-rich notifications:

[Code: dart]
/// Adds an image to be displayed in the expanded notification view.
NotificationBuilder setImage(bool imageAttachment) {
  _model = _model.copyWith(imageAttachment: imageAttachment);
  return this;
}

[T] This method sets the imageAttachment flag on the notification model, which will cause the notification to include an image when displayed.

[H3] Creating a Media-Rich Notification
[T] Let's create an example method that shows a media-rich notification:

[Code: dart]
/// Shows a media-rich notification as an example.
Future<void> showMediaRichNotification() async {
  final builder = createNotification(
    id: 456,
    title: 'New Photo',
    body: 'Check out this amazing photo!',
    channelId: NotificationChannelIds.personal,
    level: NotificationLevel.normal,
  ).setImage(true);

  await builder.show();
}

[T] This method creates a notification with an image, providing visual context for the notification.

[T] Media-rich notifications are a powerful way to enhance your notifications with visual content. They catch the user's eye and convey information more effectively through visuals, providing a more engaging user experience.


Chapter Title
Testing Advanced Notification Features

[H2] Testing Advanced Notification Features
[T] In this chapter, we'll test the advanced notification features we've implemented to ensure they work correctly across different devices and platforms.

[H3] Testing Progress Notifications
[T] Let's create a test method for progress notifications:

[Code: dart]
/// Tests progress notifications by simulating a download.
Future<void> testProgressNotifications() async {
  int maxProgress = 100;

  // Create a notification builder for the progress notification
  final builder = createProgressNotification(progress: 0, maxProgress: maxProgress);

  // Show the initial notification
  await builder.show();

  // Simulate progress updates
  for (int i = 10; i <= maxProgress; i += 10) {
    await Future.delayed(const Duration(seconds: 1));
    final updatedBuilder = createProgressNotification(
      progress: i,
      maxProgress: maxProgress,
    );
    await updatedBuilder.show();
  }
}

[T] This method simulates a download that takes 10 seconds to complete, updating the progress every second. Run this method and observe the notification as it updates.

[H3] Testing Grouped Notifications
[T] Let's create a test method for grouped notifications:

[Code: dart]
/// Tests grouped notifications by showing a group of message notifications.
Future<void> testGroupedNotifications() async {
  await showMessageGroup();
}

[T] This method shows a group of message notifications. Run this method and observe how the notifications are grouped in the notification drawer.

[H3] Testing Interactive Notifications
[T] Let's create a test method for interactive notifications:

[Code: dart]
/// Tests interactive notifications by showing a notification with actions.
Future<void> testInteractiveNotifications() async {
  await showInteractiveNotification();
}

[T] This method shows a notification with interactive actions. Run this method and try tapping the action buttons to see how they behave.

[H3] Testing Media-Rich Notifications
[T] Let's create a test method for media-rich notifications:

[Code: dart]
/// Tests media-rich notifications by showing a notification with an image.
Future<void> testMediaRichNotifications() async {
  await showMediaRichNotification();
}

[T] This method shows a notification with an image. Run this method and observe how the image appears in the notification.

[H3] Testing All Features Together
[T] Finally, let's create a test method that combines all the advanced features:

[Code: dart]
/// Tests all advanced notification features together.
Future<void> testAllAdvancedFeatures() async {
  // Test progress notifications
  await testProgressNotifications();

  // Test grouped notifications
  await testGroupedNotifications();

  // Test interactive notifications
  await testInteractiveNotifications();

  // Test media-rich notifications
  await testMediaRichNotifications();
}

[T] This method tests all the advanced notification features one after another. Run this method to see all the features in action.

[T] Testing is an essential part of implementing advanced notification features. It helps ensure that your notifications work correctly across different devices and platforms, providing a consistent user experience.


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored advanced notification features that enhance the user experience and provide more ways for users to interact with your app:

[List]
Progress Notifications: We implemented notifications with progress bars that show real-time updates for ongoing operations like downloads or uploads.
Grouped Notifications: We implemented notification groups that bundle related notifications together to reduce clutter in the notification drawer.
Interactive Notifications: We implemented notifications with action buttons that allow users to take actions directly from the notification without opening the app.
Media-Rich Notifications: We implemented notifications with images that provide visual context and make notifications more engaging.

[T] These advanced features take your notifications to the next level, providing a more engaging and interactive user experience. They allow users to stay informed and take actions without even opening your app, making your app more useful and user-friendly.

[H3] Key Takeaways
[List]
Progress notifications keep users informed about ongoing operations with visual feedback.
Grouped notifications reduce clutter in the notification drawer by bundling related notifications together.
Interactive notifications extend your app's functionality into the notification drawer, allowing users to take actions without opening the app.
Media-rich notifications enhance your notifications with visual content, making them more engaging and informative.

[T] By implementing these advanced features, you can create a more engaging and interactive notification system that enhances user experience, increases user retention, and makes your app stand out from the competition.


Write Your eBook

Section 3 eBook
Section Title
Debugging Common Notification Issues


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll learn how to identify, diagnose, and resolve common notification issues in Flutter applications. Just as a mechanic needs diagnostic tools to fix a car, developers need debugging techniques to ensure notifications are delivered reliably.

[T] In this section, you'll explore:
[List]
Common notification issues across Android and iOS platforms and how to address them
Debugging techniques to diagnose notification failures using logging and platform tools
Error handling strategies to gracefully manage notification failures
Creating a notification troubleshooter to help users resolve common issues

[T] Let's dive into the world of notification debugging!


Chapter Title
Understanding Common Notification Issues

[H2] Understanding Common Notification Issues
[T] Let's explore the most common issues that can prevent notifications from working correctly.

[H3] Permission-Related Issues
[T] On Android 13+ and iOS, explicit permission is required to show notifications:

[Code: dart]
/// Checks if notification permission is granted.
Future<bool> _checkNotificationPermission() async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    return await plugin?.areNotificationsEnabled() ?? false;
  } else if (Platform.isIOS) {
    return await _checkIOSNotificationPermission();
  }

  return false;
}

/// Requests notification permission with an explanation dialog.
Future<bool> requestPermissionsWithExplanation(BuildContext context) async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (plugin == null) return false;

    final bool? hasPermission = await plugin.areNotificationsEnabled();
    if (hasPermission == true) return true;

    // Show explanation dialog
    final bool shouldRequest = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Permission'),
        content: const Text(
          'Timora needs notification permission to send you reminders and alerts.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('DENY'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('ALLOW'),
          ),
        ],
      ),
    ) ?? false;

    if (!shouldRequest) return false;

    // Request the permission
    return await plugin.requestNotificationsPermission() ?? false;
  }

  // iOS implementation...
  return false;
}

[H3] Notification Delivery Issues
[T] Battery optimization on Android can prevent notifications from being delivered on time:

[Code: dart]
Future<void> _disableBatteryOptimization() async {
  if (Platform.isAndroid) {
    final intent = AndroidIntent(
      action: 'android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS',
      data: 'package:${await PackageInfo.fromPlatform().then((info) => info.packageName)}',
    );
    await intent.launch();
  }
}

[T] For scheduled notifications on Android, use AndroidScheduleMode.exactAllowWhileIdle to ensure delivery even in doze mode:

[Code: dart]
await _flutterLocalNotificationsPlugin.zonedSchedule(
  model.id,
  model.title,
  model.body,
  scheduledDate,
  details,
  androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  uiLocalNotificationDateInterpretation:
      UILocalNotificationDateInterpretation.wallClockTime,
  payload: model.toPayload(),
);

[H3] Time Zone Issues
[T] Scheduled notifications can fail if time zone changes aren't handled properly:

[Code: dart]
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();

    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();

    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');

    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));
  }
}


Chapter Title
Implementing Debugging Techniques

[H2] Implementing Debugging Techniques
[T] Let's explore techniques for diagnosing notification issues.

[H3] Enhanced Logging
[T] Implement a logging system to capture notification events:

[Code: dart]
/// Logs a notification event with relevant details.
void _logNotificationEvent(
  String event, {
  int? id,
  String? title,
  NotificationType? type,
  String? error,
}) {
  final buffer = StringBuffer();
  buffer.write('📱 NOTIFICATION: $event');

  if (id != null) buffer.write(' | ID: $id');
  if (title != null) buffer.write(' | Title: $title');
  if (type != null) buffer.write(' | Type: ${type.name}');
  if (error != null) buffer.write(' | ERROR: $error');

  debugPrint(buffer.toString());
}

[T] Enhance notification methods to use this logging:

[Code: dart]
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  try {
    final canShow = await _checkNotificationPrerequisites();
    if (!canShow) {
      _logNotificationEvent(
        'Cannot show notification: prerequisites not met',
        id: model.id,
        title: model.title,
        type: model.type,
      );
      return;
    }

    // ... existing code ...

    _logNotificationEvent(
      'Showed instant notification',
      id: model.id,
      title: model.title,
      type: model.type,
    );
  } catch (e) {
    _logNotificationEvent(
      'Failed to show notification',
      id: model.id,
      title: model.title,
      type: model.type,
      error: e.toString(),
    );
    rethrow;
  }
}

[H3] Using Platform-Specific Debugging Tools
[T] For iOS, use the Xcode Console to view logs:
[List]
Open Xcode and connect your iOS device
Select Window > Devices and Simulators
Select your device and click on "Open Console"
Filter logs by entering "flutter" or "notification"

[T] For Android, use ADB Logcat:
[List]
Connect your Android device via USB
Run: `adb logcat -s flutter`
Filter for notification logs: `adb logcat -s flutter:* *:E`

[H3] Testing on Different Devices and States
[T] Create a test plan that covers various scenarios:

[Code: dart]
Future<void> runNotificationTests() async {
  // Test instant notification
  await testInstantNotification();

  // Test scheduled notification
  await testScheduledNotification();

  // Test periodic notification
  await testPeriodicNotification();

  // Test notification with actions
  await testInteractiveNotification();

  _logNotificationEvent('Completed notification tests');
}


Chapter Title
Implementing Error Handling and Troubleshooting

[H2] Implementing Error Handling and Troubleshooting
[T] Let's implement robust error handling and a troubleshooter for notifications.

[H3] Try-Catch Blocks and User Feedback
[T] Wrap notification operations in try-catch blocks and provide user feedback:

[Code: dart]
Future<void> showNotificationWithErrorHandling({
  required NotificationModel model,
  required BuildContext context,
}) async {
  try {
    await showInstantNotification(model: model);
  } catch (e) {
    // Show a snackbar with the error
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Failed to show notification'),
        action: SnackBarAction(
          label: 'Retry',
          onPressed: () => showNotificationWithErrorHandling(
            model: model,
            context: context,
          ),
        ),
      ),
    );
  }
}

[H3] Device-Specific Handling
[T] Implement device-specific error handling for manufacturers with aggressive battery optimization:

[Code: dart]
Future<String> _getBatteryOptimizationInstructions() async {
  if (!Platform.isAndroid) return '';

  final deviceInfo = await DeviceInfoPlugin().androidInfo;
  final manufacturer = deviceInfo.manufacturer.toLowerCase();

  if (manufacturer.contains('xiaomi')) {
    return 'For Xiaomi devices: Go to Settings > Apps > Manage Apps > Timora > '
           'Battery > No restrictions and enable Autostart.';
  } else if (manufacturer.contains('huawei')) {
    return 'For Huawei devices: Go to Settings > Apps > Timora > '
           'Battery > Launch and disable Manage automatically.';
  }

  return 'To ensure reliable notifications, disable battery optimization for this app.';
}

[H3] Creating a Notification Troubleshooter
[T] Implement a troubleshooter that checks for common issues:

[Code: dart]
class NotificationIssue {
  final String title;
  final String description;
  final String solution;
  final String actionLabel;
  final VoidCallback action;

  NotificationIssue({
    required this.title,
    required this.description,
    required this.solution,
    required this.actionLabel,
    required this.action,
  });
}

class NotificationTroubleshooter {
  Future<List<NotificationIssue>> checkForIssues() async {
    final issues = <NotificationIssue>[];

    // Check notification permissions
    final hasPermission = await NotificationManager()._checkNotificationPermission();
    if (!hasPermission) {
      issues.add(NotificationIssue(
        title: 'Notification Permission Denied',
        description: 'You have not granted permission for notifications.',
        solution: 'Open app settings and enable notifications.',
        actionLabel: 'Open Settings',
        action: openAppSettings,
      ));
    }

    // Check battery optimization (Android only)
    if (Platform.isAndroid) {
      final isBatteryOptimized = await _isBatteryOptimized();
      if (isBatteryOptimized) {
        issues.add(NotificationIssue(
          title: 'Battery Optimization Enabled',
          description: 'Battery optimization may prevent notifications from appearing.',
          solution: 'Disable battery optimization for this app.',
          actionLabel: 'Disable Optimization',
          action: _disableBatteryOptimization,
        ));
      }
    }

    return issues;
  }
}

[T] Create a UI for the troubleshooter:

[Code: dart]
class NotificationTroubleshooterPage extends StatefulWidget {
  @override
  _NotificationTroubleshooterPageState createState() => _NotificationTroubleshooterPageState();
}

class _NotificationTroubleshooterPageState extends State<NotificationTroubleshooterPage> {
  final _troubleshooter = NotificationTroubleshooter();
  List<NotificationIssue> _issues = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkForIssues();
  }

  Future<void> _checkForIssues() async {
    setState(() => _isLoading = true);
    final issues = await _troubleshooter.checkForIssues();
    setState(() {
      _issues = issues;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notification Troubleshooter')),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _checkForIssues,
              child: _issues.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, color: Colors.green, size: 64),
                          SizedBox(height: 16),
                          Text('No issues found!',
                               style: Theme.of(context).textTheme.headline6),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _issues.length,
                      padding: EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final issue = _issues[index];
                        return Card(
                          margin: EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(issue.title,
                                     style: Theme.of(context).textTheme.headline6),
                                SizedBox(height: 8),
                                Text(issue.description),
                                SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: issue.action,
                                  child: Text(issue.actionLabel),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _checkForIssues,
        tooltip: 'Refresh',
        child: Icon(Icons.refresh),
      ),
    );
  }
}


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored common notification issues and how to debug them:

[List]
Understood permission issues on Android 13+ and iOS and implemented proper permission handling
Addressed device-specific optimizations that affect notification delivery
Implemented time zone handling for scheduled notifications
Added enhanced logging and error handling to diagnose notification failures
Created a notification troubleshooter to help users resolve common issues

[H3] Key Takeaways
[List]
Always check and request permissions before showing notifications
Handle device-specific optimizations, especially on Android devices
Use proper error handling and provide user feedback when notifications fail
Implement a troubleshooter to help users resolve common notification issues
Test your notification system thoroughly on different devices and in different app states

[T] By implementing these best practices, you'll create a robust notification system that enhances your app's user experience and keeps users engaged.