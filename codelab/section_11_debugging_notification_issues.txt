Write Your eBook

Section 3 eBook
Section Title
Debugging Common Notification Issues


Chapter Title
Section Overview

[H2] Section Overview
[T] In this section, you'll learn how to identify, diagnose, and resolve common notification issues that can occur in Flutter applications. Just as a doctor needs diagnostic tools to identify health problems, developers need debugging techniques to ensure notifications are delivered reliably.

[T] In this section, you'll explore:
[List]
Identify common notification issues across Android and iOS platforms, including permission problems, delivery failures, and time zone complications
Apply systematic debugging techniques to diagnose notification failures using logging and platform-specific tools
Implement robust error handling to gracefully manage notification failures and provide user feedback
Create a notification troubleshooter to help users resolve common issues themselves

[T] By the end of this section, you'll have a comprehensive toolkit for ensuring your notifications work reliably across different devices and platforms, even in challenging scenarios.

[T] Let's dive into the world of notification debugging!


Chapter Title
Understanding Common Notification Issues

[H2] Understanding Common Notification Issues
[T] In this chapter, we'll explore the most common issues that can prevent notifications from working correctly. Understanding these issues is the first step toward resolving them, just as identifying the cause of a car breakdown is essential before attempting repairs.

[H3] Permission-Related Issues
[T] One of the most common reasons notifications fail is permission problems. Both Android and iOS require explicit permission to show notifications, and the permission model has become more restrictive in recent versions.

[T] On Android 13 (API level 33) and higher, apps must request the POST_NOTIFICATIONS runtime permission:

[Code: xml]
<!-- Required for Android 13+ -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

[T] Let's implement a method to check and request this permission on Android:

[Code: dart]
/// Checks if notification permission is granted.
///
/// Returns `true` if permission is granted, `false` otherwise.
Future<bool> _checkNotificationPermission() async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    return await plugin?.areNotificationsEnabled() ?? false;
  } else if (Platform.isIOS) {
    return await _checkIOSNotificationPermission();
  }

  return false;
}

/// Requests notification permission with an explanation dialog.
///
/// Shows a dialog explaining why the app needs notification permission
/// before making the system permission request.
Future<bool> requestPermissionsWithExplanation(BuildContext context) async {
  if (Platform.isAndroid) {
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (plugin == null) return false;

    final bool? hasPermission = await plugin.areNotificationsEnabled();
    if (hasPermission == true) return true;

    // Show explanation dialog
    final bool shouldRequest = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Permission'),
        content: const Text(
          'Timora needs notification permission to send you reminders and alerts. '
          'Without this permission, you won\'t receive any notifications.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('DENY'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('ALLOW'),
          ),
        ],
      ),
    ) ?? false;

    if (!shouldRequest) return false;

    // Request the permission
    return await plugin.requestNotificationsPermission() ?? false;
  } else if (Platform.isIOS) {
    // For iOS, show explanation and then request permission
    final bool shouldRequest = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Permission'),
        content: const Text(
          'Timora needs notification permission to send you reminders and alerts. '
          'Without this permission, you won\'t receive any notifications.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('DENY'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('ALLOW'),
          ),
        ],
      ),
    ) ?? false;

    if (!shouldRequest) return false;

    // Request iOS permissions
    final plugin = _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>();

    return await plugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    ) ?? false;
  }

  return false;
}

[T] For iOS, we need to check and request permissions differently:

[Code: dart]
/// Checks if notification permission is granted on iOS.
///
/// Returns `true` if permission is granted, `false` otherwise.
Future<bool> _checkIOSNotificationPermission() async {
  final plugin = _flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();

  return await plugin?.requestPermissions(
    alert: true,
    badge: true,
    sound: true,
  ) ?? false;
}

/// Opens the app settings page.
///
/// On iOS, this allows users to enable notifications from the system settings.
Future<void> _openAppSettings() async {
  final plugin = _flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();

  await plugin?.requestPermissions(
    alert: true,
    badge: true,
    sound: true,
  );
}

[T] To provide a better user experience, let's implement a method that combines permission checking with user guidance:

[Code: dart]
Future<void> showNotificationWithPermissionHandling({
  required NotificationModel model,
  required BuildContext context,
}) async {
  final hasPermission = await requestPermissionsWithExplanation(context);

  if (!hasPermission) {
    // Show a message explaining the consequences of denying permission
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Notification permission denied. You won\'t receive reminders.',
        ),
        action: SnackBarAction(
          label: 'Settings',
          onPressed: () {
            // Open app settings
            openAppSettings();
          },
        ),
      ),
    );
    return;
  }

  // Show the notification
  await showInstantNotification(model: model);
}

[H3] Notification Delivery Issues
[T] Even with permissions granted, notifications might not be delivered due to battery optimization, background restrictions, or scheduling issues.

[T] On Android, battery optimization can prevent notifications from being delivered on time. Let's implement a method to disable battery optimization:

[Code: dart]
Future<void> _disableBatteryOptimization() async {
  if (Platform.isAndroid) {
    final intent = AndroidIntent(
      action: 'android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS',
      data: 'package:${await PackageInfo.fromPlatform().then((info) => info.packageName)}',
    );
    await intent.launch();
  }
}

[T] This opens the battery optimization settings page where the user can disable optimization for your app.

[T] You can also direct users to dontkillmyapp.com, which provides device-specific instructions for disabling battery optimization:

[Code: dart]
void _openDontKillMyApp() async {
  final url = 'https://dontkillmyapp.com';
  if (await canLaunch(url)) {
    await launch(url);
  }
}

[T] For scheduled notifications on Android, use AndroidScheduleMode.exactAllowWhileIdle to ensure they're delivered even when the device is in doze mode:

[Code: dart]
await _flutterLocalNotificationsPlugin.zonedSchedule(
  model.id,
  model.title,
  model.body,
  scheduledDate,
  details,
  androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  uiLocalNotificationDateInterpretation:
      UILocalNotificationDateInterpretation.wallClockTime,
  payload: model.toPayload(),
);

[H3] Time Zone Issues
[T] Scheduled notifications can fail if time zone changes aren't handled properly. Let's implement methods to handle time zone changes:

[Code: dart]
Future<void> _initializeTimeZones() async {
  try {
    // Initialize the time zone database
    tz_data.initializeTimeZones();

    // Get the local timezone
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();

    // Set the default timezone to the local timezone
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    debugPrint('Initialized time zones. Local timezone: $timeZoneName');
  } catch (e) {
    debugPrint('Failed to initialize time zones: $e');

    // Fall back to UTC
    tz.setLocalLocation(tz.getLocation('UTC'));

    debugPrint('Falling back to UTC timezone');
  }
}

[T] To handle time zone changes while the app is running, you can listen for time zone changes and reschedule notifications:

[Code: dart]
void _listenForTimeZoneChanges() {
  // This is a simplified example. In a real app, you would use a platform channel
  // to listen for time zone changes from the native side.
  Stream.periodic(const Duration(hours: 1)).listen((_) async {
    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
    final String configuredTimeZone = tz.local.name;

    if (currentTimeZone != configuredTimeZone) {
      debugPrint('Time zone changed from $configuredTimeZone to $currentTimeZone');

      // Reinitialize time zones
      await _initializeTimeZones();

      // Reschedule all notifications
      await _rescheduleAllNotifications();
    }
  });
}

Future<void> _rescheduleAllNotifications() async {
  final notifications = await getPendingNotifications();

  // Cancel all existing notifications
  await cancelAllNotifications();

  // Reschedule each notification
  for (final notification in notifications) {
    if (notification.type == NotificationType.scheduled) {
      await scheduleNotification(model: notification);
    } else if (notification.type == NotificationType.periodic) {
      await showPeriodicNotification(model: notification);
    }
  }
}


Chapter Title
Implementing Debugging Techniques

[H2] Implementing Debugging Techniques
[T] In this chapter, we'll explore techniques for debugging notification issues. Just as a detective uses various tools to solve a mystery, we'll use logging, error handling, and platform-specific tools to diagnose notification problems.

[H3] Enhanced Logging
[T] Logging is a crucial tool for debugging notifications. Let's implement a logging system that captures important notification events:

[Code: dart]
/// Logs a notification event with relevant details.
///
/// Records information about notification operations for debugging purposes.
void _logNotificationEvent(
  String event, {
  int? id,
  String? title,
  String? body,
  String? channelId,
  NotificationType? type,
  String? error,
}) {
  final buffer = StringBuffer();
  buffer.write('📱 NOTIFICATION: $event');

  if (id != null) buffer.write(' | ID: $id');
  if (title != null) buffer.write(' | Title: $title');
  if (channelId != null) buffer.write(' | Channel: $channelId');
  if (type != null) buffer.write(' | Type: ${type.name}');
  if (error != null) buffer.write(' | ERROR: $error');

  debugPrint(buffer.toString());
}

[T] Now, let's enhance our notification methods to use this logging system:

[Code: dart]
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  try {
    final canShow = await _checkNotificationPrerequisites();
    if (!canShow) {
      _logNotificationEvent(
        'Cannot show notification: prerequisites not met',
        id: model.id,
        title: model.title,
        channelId: model.channelId,
        type: model.type,
      );
      return;
    }

    // ... existing code ...

    _logNotificationEvent(
      'Showed instant notification',
      id: model.id,
      title: model.title,
      body: model.body,
      channelId: model.channelId,
      type: model.type,
    );
  } catch (e) {
    _logNotificationEvent(
      'Failed to show notification',
      id: model.id,
      title: model.title,
      channelId: model.channelId,
      type: model.type,
      error: e.toString(),
    );
    rethrow;
  }
}

[H3] Using Platform-Specific Debugging Tools
[T] Both Android and iOS provide platform-specific tools for debugging notifications.

[T] For iOS, you can use the Xcode Console to view logs:
[List]
Open Xcode and connect your iOS device
Select Window > Devices and Simulators
Select your device and click on the "Open Console" button
Filter the logs by entering "flutter" or "notification" in the search field

[T] For Android, you can use ADB Logcat:
[List]
Connect your Android device via USB
Open a terminal and run: `adb logcat -s flutter`
Filter for notification-related logs by adding additional filters: `adb logcat -s flutter:* *:E`

[T] You can also create a custom logging function that writes to a file for easier debugging:

[Code: dart]
Future<void> _writeLogToFile(String log) async {
  try {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/notification_logs.txt');
    await file.writeAsString('$log\n', mode: FileMode.append);
  } catch (e) {
    debugPrint('Failed to write log to file: $e');
  }
}

[H3] Testing on Different Devices and States
[T] To ensure your notifications work reliably, test them on various devices and in different app states:
[List]
Test on different Android manufacturers (especially Xiaomi, Huawei, and OnePlus)
Test on different iOS versions (especially iOS 10.0+ for rich notifications)
Test on different Android versions (especially Android 8.0+ for notification channels)
Test when the app is in the foreground, background, and terminated states
Test after device restart to verify that scheduled notifications are restored

[T] Create a test plan that covers these scenarios:

[Code: dart]
Future<void> runNotificationTests() async {
  // Test instant notification
  await testInstantNotification();

  // Test scheduled notification
  await testScheduledNotification();

  // Test periodic notification
  await testPeriodicNotification();

  // Test notification with actions
  await testInteractiveNotification();

  // Test notification with image
  await testMediaRichNotification();

  // Log test results
  _logNotificationEvent('Completed notification tests');
}

Future<void> testInstantNotification() async {
  try {
    final model = NotificationModel(
      id: 1001,
      title: 'Test Instant',
      body: 'This is a test instant notification',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.instant,
    );

    await showInstantNotification(model: model);
    _logNotificationEvent('Instant notification test passed');
  } catch (e) {
    _logNotificationEvent('Instant notification test failed', error: e.toString());
  }
}


Chapter Title
Implementing Error Handling

[H2] Implementing Error Handling
[T] In this chapter, we'll implement robust error handling for notifications. Just as a safety net protects acrobats, good error handling protects your app from crashes and provides a better user experience when things go wrong.

[H3] Try-Catch Blocks
[T] Wrap notification operations in try-catch blocks to prevent crashes:

[Code: dart]
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  try {
    final canShow = await _checkNotificationPrerequisites();
    if (!canShow) {
      debugPrint('Cannot show notification: prerequisites not met');
      return;
    }

    // ... existing code ...

  } catch (e) {
    debugPrint('Failed to show notification: $e');
    // Optionally, report the error to an analytics service
  }
}

[H3] User Feedback
[T] Provide feedback to users when notifications fail:

[Code: dart]
Future<void> showNotificationWithErrorHandling({
  required NotificationModel model,
  required BuildContext context,
}) async {
  try {
    await showInstantNotification(model: model);
  } catch (e) {
    // Show a snackbar with the error
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Failed to show notification: ${e.toString()}'),
        action: SnackBarAction(
          label: 'Retry',
          onPressed: () {
            showNotificationWithErrorHandling(
              model: model,
              context: context,
            );
          },
        ),
      ),
    );
  }
}

[H3] Fallback Mechanisms
[T] Implement fallback mechanisms for when notifications fail:

[Code: dart]
Future<void> showNotificationWithFallback({
  required NotificationModel model,
}) async {
  try {
    await showInstantNotification(model: model);
  } catch (e) {
    debugPrint('Notification failed, using in-app fallback: $e');

    // Store the notification in a local database
    await _storeNotificationLocally(model);

    // Show an in-app alert next time the app is opened
    _showInAppAlert = true;
  }
}

Future<void> _storeNotificationLocally(NotificationModel model) async {
  // Implementation depends on your local storage solution
  // This is a simplified example
  final prefs = await SharedPreferences.getInstance();
  final notifications = prefs.getStringList('pending_notifications') ?? [];
  notifications.add(jsonEncode(model.toJson()));
  await prefs.setStringList('pending_notifications', notifications);
}

[H3] Device-Specific Handling
[T] Implement device-specific error handling for manufacturers with aggressive battery optimization:

[Code: dart]
Future<String> _getBatteryOptimizationInstructions() async {
  if (!Platform.isAndroid) return '';

  final deviceInfo = await DeviceInfoPlugin().androidInfo;
  final manufacturer = deviceInfo.manufacturer.toLowerCase();

  if (manufacturer.contains('xiaomi') || manufacturer.contains('redmi')) {
    return 'For Xiaomi devices: Go to Settings > Apps > Manage Apps > Timora > '
           'Battery > No restrictions and enable Autostart.';
  } else if (manufacturer.contains('huawei')) {
    return 'For Huawei devices: Go to Settings > Apps > Timora > '
           'Battery > Launch and disable Manage automatically.';
  } else if (manufacturer.contains('oneplus')) {
    return 'For OnePlus devices: Go to Settings > Battery > Battery Optimization > '
           'Timora > Don\'t optimize.';
  }

  return 'To ensure reliable notifications, disable battery optimization for this app.';
}

Future<void> _showBatteryOptimizationInstructions(BuildContext context) async {
  final instructions = await _getBatteryOptimizationInstructions();

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Improve Notification Reliability'),
      content: Text(instructions),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('LATER'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _disableBatteryOptimization();
          },
          child: const Text('OPEN SETTINGS'),
        ),
      ],
    ),
  );
}


Chapter Title
Implementing a Notification Troubleshooter

[H2] Implementing a Notification Troubleshooter
[T] In this chapter, we'll implement a notification troubleshooter that helps users diagnose and resolve common notification issues. This is like providing a self-service diagnostic tool that empowers users to solve problems themselves.

[H3] Creating the NotificationIssue Class
[T] First, let's create a class to represent notification issues:

[Code: dart]
/// Represents a notification issue with a solution.
///
/// Contains information about a notification problem and how to resolve it.
class NotificationIssue {
  final String title;
  final String description;
  final String solution;
  final String actionLabel;
  final VoidCallback action;

  NotificationIssue({
    required this.title,
    required this.description,
    required this.solution,
    required this.actionLabel,
    required this.action,
  });
}

[H3] Implementing the NotificationTroubleshooter
[T] Now, let's implement the troubleshooter that checks for common issues:

[Code: dart]
/// Utility class for diagnosing and resolving notification issues.
///
/// Provides methods to check for common notification problems and suggest solutions.
class NotificationTroubleshooter {
  /// Checks for common notification issues and returns a list of problems.
  ///
  /// Identifies issues that might prevent notifications from working properly.
  Future<List<NotificationIssue>> checkForIssues() async {
    final issues = <NotificationIssue>[];

    // Check notification permissions
    final hasPermission = await NotificationManager()._checkNotificationPermission();
    if (!hasPermission) {
      issues.add(NotificationIssue(
        title: 'Notification Permission Denied',
        description: 'You have not granted permission for notifications.',
        solution: 'Open app settings and enable notifications.',
        actionLabel: 'Open Settings',
        action: openAppSettings,
      ));
    }

    // Check battery optimization (Android only)
    if (Platform.isAndroid) {
      final isBatteryOptimized = await _isBatteryOptimized();
      if (isBatteryOptimized) {
        issues.add(NotificationIssue(
          title: 'Battery Optimization Enabled',
          description: 'Battery optimization may prevent notifications from appearing.',
          solution: 'Disable battery optimization for this app.',
          actionLabel: 'Disable Optimization',
          action: _disableBatteryOptimization,
        ));
      }
    }

    // Check for pending notifications
    final pendingNotifications = await NotificationManager().getPendingNotifications();
    if (pendingNotifications.isEmpty) {
      issues.add(NotificationIssue(
        title: 'No Pending Notifications',
        description: 'You don\'t have any scheduled notifications.',
        solution: 'Create a notification to test if they\'re working.',
        actionLabel: 'Create Notification',
        action: () => AppRouter.navigateTo(AppRoutes.createNotification.value),
      ));
    }

    // Check for iOS background refresh (iOS only)
    if (Platform.isIOS) {
      final isBackgroundRefreshEnabled = await _isBackgroundRefreshEnabled();
      if (!isBackgroundRefreshEnabled) {
        issues.add(NotificationIssue(
          title: 'Background Refresh Disabled',
          description: 'Background refresh is needed for scheduled notifications.',
          solution: 'Enable background refresh for this app in iOS settings.',
          actionLabel: 'Show Instructions',
          action: _showBackgroundRefreshInstructions,
        ));
      }
    }

    return issues;
  }

  /// Checks if battery optimization is enabled for the app.
  Future<bool> _isBatteryOptimized() async {
    if (Platform.isAndroid) {
      final intent = AndroidIntent(
        action: 'android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS',
      );
      return await intent.canResolveActivity() ?? false;
    }

    return false;
  }

  /// Checks if background refresh is enabled on iOS.
  Future<bool> _isBackgroundRefreshEnabled() async {
    // This is a simplified example. In a real app, you would use
    // a platform channel to check the actual setting.
    return true;
  }

  /// Shows instructions for enabling background refresh on iOS.
  void _showBackgroundRefreshInstructions() {
    // Implementation would show a dialog with instructions
  }
}

[H3] Creating the Troubleshooter UI
[T] Now, let's create a UI for the notification troubleshooter:

[Code: dart]
/// A page that helps users diagnose and resolve notification issues.
class NotificationTroubleshooterPage extends StatefulWidget {
  const NotificationTroubleshooterPage({Key? key}) : super(key: key);

  @override
  _NotificationTroubleshooterPageState createState() => _NotificationTroubleshooterPageState();
}

class _NotificationTroubleshooterPageState extends State<NotificationTroubleshooterPage> {
  final _troubleshooter = NotificationTroubleshooter();
  List<NotificationIssue> _issues = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkForIssues();
  }

  Future<void> _checkForIssues() async {
    setState(() {
      _isLoading = true;
    });

    final issues = await _troubleshooter.checkForIssues();

    setState(() {
      _issues = issues;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Notification Troubleshooter'),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _checkForIssues,
              child: _issues.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 64,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No issues found!',
                            style: Theme.of(context).textTheme.headline6,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Your notification settings look good.',
                            style: Theme.of(context).textTheme.bodyText2,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _issues.length,
                      padding: EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final issue = _issues[index];
                        return Card(
                          margin: EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.warning,
                                      color: Colors.orange,
                                    ),
                                    SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        issue.title,
                                        style: Theme.of(context).textTheme.headline6,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8),
                                Text(issue.description),
                                SizedBox(height: 4),
                                Text(
                                  'Solution: ${issue.solution}',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: issue.action,
                                  child: Text(issue.actionLabel),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _checkForIssues,
        tooltip: 'Refresh',
        child: Icon(Icons.refresh),
      ),
    );
  }
}

[H3] Adding the Troubleshooter to Your App
[T] Finally, let's add the troubleshooter to our app's navigation:

[Code: dart]
// In your routes definition
static final Map<String, WidgetBuilder> routes = {
  // ... other routes
  AppRoutes.troubleshooter.value: (context) => NotificationTroubleshooterPage(),
};

// In your settings page or home page
ElevatedButton(
  onPressed: () {
    Navigator.pushNamed(context, AppRoutes.troubleshooter.value);
  },
  child: Text('Notification Troubleshooter'),
)

[T] This troubleshooter provides a user-friendly way for users to diagnose and resolve common notification issues, improving the overall user experience.


Chapter Title
Code Challenge: Testing Notification Reliability

[H2] Code Challenge: Testing Notification Reliability
[T] Now that we've implemented debugging techniques and a notification troubleshooter, let's test our notification system's reliability with a code challenge.

[H3] The Challenge
[T] Your challenge is to create a comprehensive test plan for your notification system. The test plan should:
[List]
Test notifications on different Android and iOS versions
Verify that notifications work in different app states (foreground, background, terminated)
Check that scheduled notifications survive device restarts
Ensure that notifications with different priority levels are displayed correctly
Verify that notification actions work as expected

[T] Implement a method that runs through a series of notification tests and logs the results:

[Code: dart]
Future<void> runNotificationTests() async {
  // TODO: Implement a series of notification tests
  // 1. Test instant notifications
  // 2. Test scheduled notifications
  // 3. Test periodic notifications
  // 4. Test notifications with different priority levels
  // 5. Test notifications with actions
  // 6. Log the results of each test
}

[H3] Solution
[T] Here's a solution to the challenge:

[Code: dart]
Future<void> runNotificationTests() async {
  _logNotificationEvent('Starting notification tests');

  // Test instant notifications
  await _testInstantNotification();

  // Test scheduled notifications
  await _testScheduledNotification();

  // Test periodic notifications
  await _testPeriodicNotification();

  // Test priority levels
  await _testPriorityLevels();

  // Test actions
  await _testNotificationActions();

  _logNotificationEvent('Completed notification tests');
}

Future<void> _testInstantNotification() async {
  try {
    final model = NotificationModel(
      id: 1001,
      title: 'Test Instant',
      body: 'This is a test instant notification',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.instant,
    );

    await showInstantNotification(model: model);
    _logNotificationEvent('Instant notification test passed');
  } catch (e) {
    _logNotificationEvent('Instant notification test failed', error: e.toString());
  }
}

Future<void> _testScheduledNotification() async {
  try {
    final model = NotificationModel(
      id: 1002,
      title: 'Test Scheduled',
      body: 'This is a test scheduled notification',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      scheduledTime: DateTime.now().add(Duration(seconds: 5)),
    );

    await scheduleNotification(model: model);
    _logNotificationEvent('Scheduled notification test passed');
  } catch (e) {
    _logNotificationEvent('Scheduled notification test failed', error: e.toString());
  }
}

// Similar methods for other tests...

[T] This test plan helps ensure that your notification system works reliably across different scenarios and devices.


Chapter Title
Section Summary

[H2] Section Summary
[T] In this section, we've explored common notification issues and how to debug them:

[List]
Understood permission issues on Android 13+ and iOS and implemented proper permission handling
Addressed device-specific optimizations that affect notification delivery, especially on Android
Implemented time zone handling to ensure scheduled notifications appear at the correct time
Added enhanced logging and error handling to diagnose and recover from notification failures
Created a notification troubleshooter to help users diagnose and resolve common issues
Developed a comprehensive testing strategy to ensure notification reliability

[T] With these debugging techniques and tools, you can ensure that your notifications work reliably across different devices and platforms, providing a consistent user experience.

[H3] Key Takeaways
[List]
Always check and request permissions before showing notifications
Handle device-specific optimizations, especially on Android devices from manufacturers like Xiaomi, Huawei, and OnePlus
Use proper error handling and provide user feedback when notifications fail
Implement a troubleshooter to help users resolve common notification issues
Test your notification system thoroughly on different devices and in different app states

[T] By implementing these best practices, you'll create a robust notification system that enhances your app's user experience and keeps users engaged.

[H3] What's Next
[T] Now that you've mastered local notifications in Flutter, you might want to explore related topics such as:
[List]
Firebase Cloud Messaging (FCM) for remote notifications
Advanced notification scheduling with workmanager
Deep linking from notifications to specific app screens
Analytics to track notification engagement

[T] These advanced topics build on the foundation you've established in this codelab, allowing you to create even more powerful notification experiences for your users.