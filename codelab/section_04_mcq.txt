Section 4 - Multiple Choice Questions
Scheduling Notifications with Time Zone Awareness

[H1] Knowledge Check

[T] Test your understanding of scheduled notifications with these multiple-choice questions:

[H2] Question 1: Why is time zone handling critical for scheduled notifications?

[List]
A) It ensures notifications appear at the correct local time even when users travel across time zones
B) It reduces battery consumption on mobile devices
C) It's required by both Apple App Store and Google Play Store guidelines
D) It prevents notifications from being blocked by the operating system

[T] Correct Answer: A

[T] Explanation: Time zone handling is critical for scheduled notifications because it ensures that notifications appear at the correct local time even when users travel across time zones. Without proper time zone handling, a notification scheduled for 8:00 AM might appear at an unexpected time if the user travels to a different time zone. For example, a medication reminder should appear at 8:00 AM local time regardless of where the user is located.

[H2] Question 2: What does the matchDateTimeComponents parameter in zonedSchedule() enable when set to DateTimeComponents.dayOfWeekAndTime?

[List]
A) It ensures notifications appear only on specific dates
B) It makes notifications repeat at the same time every day
C) It makes notifications repeat on the same day of the week at the same time
D) It prevents duplicate notifications from being scheduled

[T] Correct Answer: C

[T] Explanation: When the matchDateTimeComponents parameter in zonedSchedule() is set to DateTimeComponents.dayOfWeekAndTime, it configures the notification to repeat on the same day of the week at the same time. This is particularly useful for weekly recurring notifications, such as a reminder for a weekly team meeting every Monday at 9:00 AM. The system will automatically handle the repetition without requiring you to schedule multiple individual notifications.

[H2] Question 3: After a device restart, scheduled notifications no longer fire. What should you check first in Timora's setup?

[List]
A) Whether the app has the RECEIVE_BOOT_COMPLETED permission in AndroidManifest.xml
B) Whether the notification channels are properly configured
C) Whether the app is using the correct notification IDs
D) Whether the time zone initialization is working correctly

[T] Correct Answer: A

[T] Explanation: If scheduled notifications stop firing after a device restart, the first thing to check is whether the app has the RECEIVE_BOOT_COMPLETED permission in the AndroidManifest.xml file. This permission allows the app to receive the BOOT_COMPLETED broadcast, which is necessary for rescheduling notifications after the device restarts. Without this permission, the flutter_local_notifications plugin's ScheduledNotificationBootReceiver won't be able to reschedule the notifications.

[H2] Question 4: Which of the following methods is used to convert a regular DateTime to a time zone-aware DateTime in Timora?

[List]
A) DateTime.toLocal()
B) tz.TZDateTime.from(dateTime, tz.local)
C) DateTime.toUtc().toLocal()
D) TimeZoneConverter.convert(dateTime)

[T] Correct Answer: B

[T] Explanation: In Timora, we use tz.TZDateTime.from(dateTime, tz.local) to convert a regular DateTime to a time zone-aware TZDateTime. This conversion is essential for proper time zone handling in scheduled notifications. The TZDateTime class from the timezone package provides the necessary functionality to handle time zones correctly.

[H2] Question 5: In Timora, which combination of methods is used to create a weekly recurring notification?

[List]
A) scheduleWeekly(dayOfWeek, time)
B) setRepeatInterval(RepeatInterval.weekly), atTimeOfDay(time), onDayOfWeek(dayOfWeek)
C) scheduleForDateAndTime(date, time), setWeeklyRepeat(true)
D) createWeeklyNotification(dayOfWeek, time)

[T] Correct Answer: B

[T] Explanation: In Timora, we create weekly recurring notifications using a combination of three methods: setRepeatInterval(RepeatInterval.weekly) to set the repeat interval to weekly, atTimeOfDay(time) to set the time of day, and onDayOfWeek(dayOfWeek) to set the day of the week. This combination provides a flexible way to configure weekly notifications.
