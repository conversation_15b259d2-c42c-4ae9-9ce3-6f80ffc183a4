Section 5 eBook
Implementing Recurring and Periodic Notifications

Chapter Title
Section Overview

[T] In this section, you'll explore:
[List]
What recurring and periodic notifications are and how they differ
How to implement notifications that repeat at regular intervals
How to create daily, weekly, hourly, and per-minute notifications
How to handle platform differences between Android and iOS
How to manage and cancel recurring notifications

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!


Next Chapter Title
Understanding Recurring and Periodic Notifications

[T] In this chapter, we'll explore what recurring and periodic notifications are, their different types, and how they can be implemented in Flutter.

[H2] Types of Repeating Notifications

[T] There are two main approaches to implementing repeating notifications:

[List]
**Calendar-based (Recurring)**: Notifications that repeat based on calendar patterns (e.g., daily at 9:00 AM, weekly on Mondays)
**Fixed intervals (Periodic)**: Notifications that repeat at fixed time intervals (e.g., every 30 minutes, every hour)

[T] The flutter_local_notifications package supports both approaches through different methods:

[List]
**zonedSchedule() with matchDateTimeComponents**: For calendar-based repetitions
**periodicallyShow()**: For fixed interval repetitions

[H2] Repeat Intervals

[T] The RepeatInterval enum in our app defines the supported repetition patterns:

[Code: dart]
enum RepeatInterval {
  everyMinute,
  hourly,
  daily,
  weekly,
}

[T] Each interval corresponds to a different repetition pattern:

[List]
**everyMinute**: Repeats every minute (primarily for testing purposes)
**hourly**: Repeats every hour at the same minute
**daily**: Repeats every day at the same time
**weekly**: Repeats every week on the same day and time

[H2] Platform Differences

[T] It's important to note that Android and iOS handle periodic notifications differently:

[List]
**Android**: Provides direct support for periodic notifications through the periodicallyShow() method
**iOS**: Requires using zonedSchedule() with custom logic to simulate periodic behavior

[T] In our implementation, we'll handle these platform differences transparently, providing a consistent API for both platforms.

[T] Up Next, we'll implement the core functionality for periodic notifications in our NotificationManager class.


Next Chapter Title
Implementing Periodic Notifications

[T] In this chapter, we'll implement the showPeriodicNotification() method in the NotificationManager class, which will handle both calendar-based and fixed-interval notifications.

[H2] The showPeriodicNotification Method

[T] Let's implement the showPeriodicNotification() method in the NotificationManager class. This method will display a notification that repeats at regular intervals.

[Code: dart]
/// Displays a periodic notification that repeats at a fixed interval.
///
/// Configures a notification to repeat according to the [model.repeatInterval].
/// If [enabled] is `false`, cancels any existing notification with the same ID.
///
/// Throws an [ArgumentError] if [model.repeatInterval] is null.
Future<void> showPeriodicNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show periodic notification: prerequisites not met');
    return;
  }

  if (model.repeatInterval == null) {
    throw ArgumentError(
      'repeatInterval must be provided for periodic notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  // Configure platform-specific notification details
  NotificationDetails details = await _buildNotificationDetails(notificationWithDeepLink);

  // For daily/weekly notifications with a specific time, use zonedSchedule with matchDateTimeComponents
  if (_shouldUseZonedSchedule(notificationWithDeepLink)) {
    await _scheduleRecurringAtTime(notificationWithDeepLink, details);
  } else {
    // For simpler repeating notifications without specific time requirements
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      notificationWithDeepLink.id,
      notificationWithDeepLink.title,
      notificationWithDeepLink.body,
      notificationWithDeepLink.repeatInterval!,
      details,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: notificationWithDeepLink.toPayload(),
    );
  }
}

[T] This method handles different types of periodic notifications:

[List]
1. For calendar-based notifications (daily, weekly) with a specific time, it uses the _scheduleRecurringAtTime method
2. For fixed-interval notifications (hourly, every minute), it uses the periodicallyShow method

[H2] Determining the Notification Type

[T] We need to determine whether to use zonedSchedule (for calendar-based notifications) or periodicallyShow (for fixed-interval notifications). Let's implement the _shouldUseZonedSchedule method:

[Code: dart]
/// Determines if zonedSchedule should be used instead of periodicallyShow.
///
/// Returns `true` if the notification is daily or weekly and has a specific
/// time of day set.
bool _shouldUseZonedSchedule(NotificationModel model) {
  return (model.repeatInterval == RepeatInterval.daily ||
          model.repeatInterval == RepeatInterval.weekly) &&
      model.timeOfDay != null;
}

[T] This method returns true if the notification is daily or weekly and has a specific time of day set, indicating that we should use zonedSchedule with matchDateTimeComponents.

[H2] Scheduling Recurring Notifications

[T] Now, let's implement the _scheduleRecurringAtTime method to handle calendar-based notifications:

[Code: dart]
/// Schedules a recurring notification at a specific time.
///
/// Configures a notification to repeat at a specific time of day based on
/// the model's timeOfDay and optional dayOfWeek properties.
Future<void> _scheduleRecurringAtTime(
  NotificationModel model,
  NotificationDetails details,
) async {
  // Calculate the next occurrence based on the model properties
  tz.TZDateTime nextOccurrence = _calculateNextOccurrence(
    timeOfDay: model.timeOfDay!,
    dayOfWeek:
        model.repeatInterval == RepeatInterval.weekly
            ? model.dayOfWeek
            : null,
  );

  // Determine the recurrence pattern
  DateTimeComponents matchDateTimeComponents =
      model.repeatInterval == RepeatInterval.daily
          ? DateTimeComponents.time
          : DateTimeComponents.dayOfWeekAndTime;

  // Schedule the notification with the recurrence pattern
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    nextOccurrence,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    matchDateTimeComponents: matchDateTimeComponents,
    payload: model.toPayload(),
  );
}

[T] This method:

[List]
1. Calculates the next occurrence of the notification based on the time of day and optional day of week
2. Determines the recurrence pattern (time for daily notifications, dayOfWeekAndTime for weekly notifications)
3. Schedules the notification with the appropriate recurrence pattern

[T] Up Next, we'll implement the helper method to calculate the next occurrence of a notification.


Next Chapter Title
Implementing Helper Methods for Time Calculations

[T] In this chapter, we'll implement the helper method to calculate the next occurrence of a notification based on the specified time and day.

[H2] Calculating the Next Occurrence

[T] Let's implement the _calculateNextOccurrence method to determine when a notification should next appear:

[Code: dart]
/// Calculates the next occurrence time for a recurring notification.
///
/// Determines when a notification should next appear based on the specified
/// [timeOfDay] and optional [dayOfWeek].
tz.TZDateTime _calculateNextOccurrence({
  required TimeOfDay timeOfDay,
  int? dayOfWeek,
}) {
  final now = tz.TZDateTime.now(tz.local);

  // Set the time component
  tz.TZDateTime scheduledDate = tz.TZDateTime(
    tz.local,
    now.year,
    now.month,
    now.day,
    timeOfDay.hour,
    timeOfDay.minute,
  );

  // If the time today is already past, schedule for tomorrow
  if (scheduledDate.isBefore(now)) {
    scheduledDate = scheduledDate.add(const Duration(days: 1));
  }

  // If a specific day of week is requested (for weekly notifications)
  if (dayOfWeek != null) {
    // In DateTime, weekday is 1-7 where 1 is Monday and 7 is Sunday
    // Our dayOfWeek follows the same convention
    while (scheduledDate.weekday != dayOfWeek) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
  }

  return scheduledDate;
}

[T] This method calculates the next occurrence of a notification based on:

[List]
1. The specified time of day (hour and minute)
2. An optional day of the week (for weekly notifications)

[T] The method ensures that:

[List]
1. If the specified time today is already past, it schedules for tomorrow
2. For weekly notifications, it finds the next occurrence of the specified day of the week

[H2] Understanding Time Zone Handling

[T] Notice that we're using the tz package to handle time zones correctly. This ensures that notifications are scheduled at the correct time regardless of the user's time zone or daylight saving time changes.

[T] The tz.TZDateTime class is similar to the standard DateTime class but with time zone awareness. We use tz.local to get the user's local time zone, which ensures that notifications are scheduled in the user's local time.

[T] When using matchDateTimeComponents with zonedSchedule, the flutter_local_notifications plugin will automatically handle time zone changes and daylight saving time transitions, ensuring that notifications always appear at the correct local time.

[T] Up Next, we'll enhance the NotificationBuilder to make it easier to create recurring notifications.


Next Chapter Title
Enhancing the Notification Builder

[T] In this chapter, we'll enhance the NotificationBuilder class to make it easier to create recurring notifications with a fluent API.

[H2] Adding Methods for Recurring Notifications

[T] Let's add methods to the NotificationBuilder class to make it easier to create different types of recurring notifications:

[Code: dart]
/// Configures the notification to repeat at a specific interval.
///
/// Changes the notification type to periodic.
///
/// [repeatInterval] How frequently the notification should repeat
NotificationBuilder setRepeatInterval(RepeatInterval repeatInterval) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: repeatInterval,
  );
  return this;
}

/// Sets a specific time of day for recurring notifications.
///
/// Used primarily with daily and weekly notifications to ensure they appear
/// at a consistent time.
///
/// [timeOfDay] The time when the notification should be shown
NotificationBuilder atTimeOfDay(TimeOfDay timeOfDay) {
  _model = _model.copyWith(timeOfDay: timeOfDay);
  return this;
}

/// Sets the day of week for weekly recurring notifications.
///
/// [dayOfWeek] Day of week (1-7, where 1 is Monday and 7 is Sunday)
///
/// Throws an [ArgumentError] if dayOfWeek is out of valid range.
NotificationBuilder onDayOfWeek(int dayOfWeek) {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw ArgumentError('Day of week must be between 1-7 (Monday to Sunday)');
  }
  _model = _model.copyWith(dayOfWeek: dayOfWeek);
  return this;
}

[T] These methods make it easy to create different types of periodic notifications:

[List]
**setRepeatInterval()** configures a notification to repeat at a specific interval (daily, weekly, hourly, etc.)
**atTimeOfDay()** sets the time of day for daily and weekly notifications
**onDayOfWeek()** sets the day of the week for weekly notifications

[H2] Using the Enhanced NotificationBuilder

[T] With these enhancements, creating recurring notifications becomes much simpler. Here are some examples:

[Code: dart]
// Create a daily notification at 9:00 AM
final builder = NotificationBuilder.create(
  notificationManager,
  id: 1,
  title: 'Daily Reminder',
  body: 'This is your daily reminder',
  channelId: NotificationChannelIds.personal,
)
.setRepeatInterval(RepeatInterval.daily)
.atTimeOfDay(const TimeOfDay(hour: 9, minute: 0));

// Show the notification
await builder.show();

// Create a weekly notification on Monday at 10:00 AM
final builder = NotificationBuilder.create(
  notificationManager,
  id: 2,
  title: 'Weekly Meeting',
  body: 'Team meeting',
  channelId: NotificationChannelIds.work,
)
.setRepeatInterval(RepeatInterval.weekly)
.atTimeOfDay(const TimeOfDay(hour: 10, minute: 0))
.onDayOfWeek(1); // 1 = Monday

// Show the notification
await builder.show();

// Create an hourly notification
final builder = NotificationBuilder.create(
  notificationManager,
  id: 3,
  title: 'Hourly Reminder',
  body: 'Check your progress',
  channelId: NotificationChannelIds.work,
)
.setRepeatInterval(RepeatInterval.hourly);

// Show the notification
await builder.show();

[T] This fluent API makes it easy to create recurring notifications with just a few lines of code, hiding the complexity of the underlying implementation.

[H2] Updating the show() Method

[T] The show() method in the NotificationBuilder class needs to handle different types of notifications. Let's update it to support periodic notifications:

[Code: dart]
/// Shows the notification immediately or at the configured time.
///
/// The behavior depends on the notification type (instant, scheduled, periodic).
///
/// Throws an [ArgumentError] if required properties for the notification type are missing.
Future<void> show() async {
  switch (_model.type) {
    case NotificationType.instant:
      return await _manager.showInstantNotification(model: _model);

    case NotificationType.scheduled:
      if (_model.scheduledTime == null) {
        throw ArgumentError(
          'scheduledTime must be set for scheduled notifications',
        );
      }
      return await _manager.scheduleNotification(model: _model);

    case NotificationType.periodic:
      if (_model.repeatInterval == null) {
        throw ArgumentError(
          'repeatInterval must be set for periodic notifications',
        );
      }
      return await _manager.showPeriodicNotification(model: _model);
  }
}

[T] This method checks the notification type and calls the appropriate method in the NotificationManager class. For periodic notifications, it ensures that the repeatInterval property is set.

[T] Up Next, we'll update the UI to allow users to select repetition options when creating notifications.


Next Chapter Title
Updating the UI for Periodic Notifications

[T] In this chapter, we'll update the UI to allow users to select repetition options when creating notifications.

[H2] Understanding the UI Architecture

[T] Our app uses a modern UI architecture with a controller pattern. The CreateNotificationController class manages the form state, while the UI components in CreateNotificationPage display the form and handle user interactions.

[T] The form data is stored in a NotificationFormData class, which includes properties for the notification type, periodic subtype, recurring time, and day of week.

[Code: dart]
class NotificationFormData {
  String title = '';
  String body = '';
  DateTime? scheduledDateTime;
  TimeOfDay? recurringTime;
  int? dayOfWeek; // 1-7 (Monday-Sunday)
  String channelId = NotificationChannelIds.personal;
  NotificationLevel level = NotificationLevel.normal;
  bool isFullScreen = false;
  bool hasActions = false;
  bool imageAttachment = false;
  bool customSound = false;
  String notificationType = 'Instant'; // Default type
  String periodicSubtype = 'Daily'; // Default subtype
}

[H2] Implementing the Notification Type Section

[T] Let's implement the NotificationTypeSection widget, which allows users to select the notification type and configure the repetition options:

[Code: dart]
class NotificationTypeSection extends StatelessWidget {
  final CreateNotificationController controller;
  final VoidCallback onDateTimePicked;
  final VoidCallback onTimePicked;

  const NotificationTypeSection({
    super.key,
    required this.controller,
    required this.onDateTimePicked,
    required this.onTimePicked,
  });

  Widget _buildNotificationTypeSelector(BuildContext context) {
    final mainTypes = ['Instant', 'Scheduled', 'Periodic'];

    return ChipSelector(
      options: mainTypes,
      selectedOption: controller.value.notificationType,
      onSelected: controller.updateNotificationType,
    );
  }

  Widget _buildPeriodicSubtypeSelector(BuildContext context) {
    if (controller.value.notificationType != 'Periodic') {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final periodicSubtypes = ['Daily', 'Weekly', 'Hourly', 'Every Minute'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Row(
          children: [
            DecoratedBox(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Icon(
                  Icons.repeat,
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Repeat Frequency',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
                letterSpacing: -0.3,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ChipSelector(
          options: periodicSubtypes,
          selectedOption: controller.value.periodicSubtype,
          onSelected: controller.updatePeriodicSubtype,
        ),
      ],
    );
  }

  Widget _buildTimeSelector(BuildContext context) {
    final formData = controller.value;

    // For scheduled notifications
    if (formData.notificationType == 'Scheduled') {
      final dateTime = formData.scheduledDateTime;
      String? subtitle;

      if (dateTime != null) {
        subtitle =
            '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      }

      return DateTimeSelector(
        selectedTitle: 'Scheduled Date & Time',
        unselectedTitle: 'Select Date & Time',
        selectedSubtitle: subtitle,
        unselectedSubtitle: 'Tap to choose when to send the notification',
        icon: Icons.calendar_today,
        tooltip: 'Select a date and time for the notification',
        onTap: onDateTimePicked,
        hasValue: dateTime != null,
      );
    }

    // For periodic notifications
    if (formData.notificationType == 'Periodic' &&
        (formData.periodicSubtype == 'Daily' ||
            formData.periodicSubtype == 'Weekly')) {
      String buttonTitle;
      String unselectedSubtitle;
      String? selectedSubtitle;

      // Different label text based on the notification subtype
      if (formData.periodicSubtype == 'Daily') {
        buttonTitle = 'Daily Time';
        unselectedSubtitle = 'Select time of day';
        if (formData.recurringTime != null) {
          selectedSubtitle =
              'At ${formData.recurringTime!.format(context)} every day';
        }
      } else {
        buttonTitle = 'Weekly Time';
        unselectedSubtitle = 'Select time of day';
        if (formData.recurringTime != null) {
          selectedSubtitle =
              'At ${formData.recurringTime!.format(context)} weekly';
        }
      }

      return DateTimeSelector(
        selectedTitle: buttonTitle,
        unselectedTitle: buttonTitle,
        selectedSubtitle: selectedSubtitle,
        unselectedSubtitle: unselectedSubtitle,
        icon: Icons.access_time,
        tooltip: 'Select a time for the recurring notification',
        onTap: onTimePicked,
        hasValue: formData.recurringTime != null,
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildNotificationTypeSelector(context),
        _buildPeriodicSubtypeSelector(context),
        const SizedBox(height: 16),
        _buildTimeSelector(context),
        if (controller.value.notificationType == 'Periodic' &&
            controller.value.periodicSubtype == 'Weekly')
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: StyledDropdown<int>(
              label: 'Day of Week',
              prefixIcon: Icons.view_week,
              value: controller.value.dayOfWeek,
              items: const [
                DropdownMenuItem(value: 1, child: Text('Monday')),
                DropdownMenuItem(value: 2, child: Text('Tuesday')),
                DropdownMenuItem(value: 3, child: Text('Wednesday')),
                DropdownMenuItem(value: 4, child: Text('Thursday')),
                DropdownMenuItem(value: 5, child: Text('Friday')),
                DropdownMenuItem(value: 6, child: Text('Saturday')),
                DropdownMenuItem(value: 7, child: Text('Sunday')),
              ],
              onChanged: (value) {
                if (value != null) {
                  controller.updateDayOfWeek(value);
                }
              },
            ),
          ),
      ],
    );
  }
}

[T] This widget:

[List]
1. Displays a chip selector for the notification type (Instant, Scheduled, Periodic)
2. When "Periodic" is selected, displays a chip selector for the repeat interval (Daily, Weekly, Hourly, Every Minute)
3. For daily and weekly notifications, displays a time picker
4. For weekly notifications, displays a day of week dropdown

[H2] Implementing the Controller Methods

[T] Now, let's implement the controller methods that handle the UI interactions:

[Code: dart]
void updateNotificationType(String type) {
  value = value..notificationType = type;
  value.scheduledDateTime = null;
  value.recurringTime = null;
  value.dayOfWeek = null;

  if (type != 'Periodic') {
    value.periodicSubtype = 'Daily';
  }
  notifyListeners();
}

void updatePeriodicSubtype(String subtype) {
  value = value..periodicSubtype = subtype;
  value.recurringTime = null;
  value.dayOfWeek = null;
  notifyListeners();
}

void updateDayOfWeek(int dayOfWeek) {
  value = value..dayOfWeek = dayOfWeek;
  notifyListeners();
}

void setRecurringTime(TimeOfDay time) {
  value = value..recurringTime = time;
  notifyListeners();
}

[T] These methods update the form data when the user interacts with the UI components.

[H2] Preparing the Notification

[T] Finally, let's implement the prepareNotification method in the controller, which creates a NotificationBuilder with the selected options:

[Code: dart]
NotificationBuilder prepareNotification() {
  // Create the base notification builder
  NotificationBuilder builder = NotificationBuilder.create(
    notificationManager,
    id: DateTime.now().millisecondsSinceEpoch % 100000,
    title: value.title,
    body: value.body,
    channelId: value.channelId,
    level: value.level,
  );

  // Configure based on type
  switch (value.notificationType) {
    case 'Instant':
      // No additional configuration needed for instant notifications
      break;

    case 'Scheduled':
      builder = builder.scheduleFor(value.scheduledDateTime!);
      break;

    case 'Periodic':
      // Map UI selection to RepeatInterval
      late RepeatInterval repeatInterval;

      switch (value.periodicSubtype) {
        case 'Daily':
          repeatInterval = RepeatInterval.daily;
          break;
        case 'Weekly':
          repeatInterval = RepeatInterval.weekly;
          break;
        case 'Hourly':
          repeatInterval = RepeatInterval.hourly;
          break;
        case 'Every Minute':
          repeatInterval = RepeatInterval.everyMinute;
          break;
      }

      builder = builder.setRepeatInterval(repeatInterval);

      // For daily/weekly notifications, set the time of day
      if (value.periodicSubtype == 'Daily' ||
          value.periodicSubtype == 'Weekly') {
        builder = builder.atTimeOfDay(value.recurringTime!);

        // For weekly notifications, set the day of week
        if (value.periodicSubtype == 'Weekly') {
          builder = builder.onDayOfWeek(value.dayOfWeek!);
        }
      }
      break;
  }

  return builder;
}

[T] This method creates a NotificationBuilder with the basic properties, then configures it based on the selected notification type and repetition options.

[T] Up Next, we'll test our implementation by creating different types of periodic notifications.


Next Chapter Title
Testing Periodic Notifications

[T] In this chapter, we'll test our implementation by creating different types of periodic notifications.

[H2] Testing Daily Notifications

[T] Let's test our implementation by creating a daily notification:

[List]
1. Run the app and navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Daily Reminder"
   - **Body**: "This notification repeats every day"
   - **Notification Type**: "Periodic"
   - **Repeat Frequency**: "Daily"
   - **Time**: Select a time a few minutes from now
   - **Category**: "Personal"
   - **Priority**: "Normal"
3. Tap the "Schedule Notification" button

[T] The notification should appear at the specified time and repeat every day at the same time.

[Image]

[H2] Testing Weekly Notifications

[T] Now, let's create a weekly notification:

[List]
1. Navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Weekly Meeting"
   - **Body**: "Team meeting"
   - **Notification Type**: "Periodic"
   - **Repeat Frequency**: "Weekly"
   - **Time**: Select a time
   - **Day of Week**: Select a day (e.g., Monday)
   - **Category**: "Work"
   - **Priority**: "High"
3. Tap the "Schedule Notification" button

[T] The notification should appear on the specified day and time and repeat every week.

[H2] Testing Hourly Notifications

[T] For testing purposes, let's create a notification that repeats every hour:

[List]
1. Navigate to the "Create Notification" screen
2. Fill in the following details:
   - **Title**: "Hourly Reminder"
   - **Body**: "Check your progress"
   - **Notification Type**: "Periodic"
   - **Repeat Frequency**: "Hourly"
   - **Category**: "Work"
   - **Priority**: "Normal"
3. Tap the "Schedule Notification" button

[T] This notification should repeat every hour, allowing you to quickly verify that periodic notifications are working correctly.

[Emphasis, bulb] In a real app, you would use more reasonable intervals like daily or weekly for most notifications. High-frequency notifications can drain the battery and annoy users.

[H2] Verifying Notifications

[T] To verify that your periodic notifications are working correctly, you can:

[List]
1. Check the notification drawer to see if the notification appears at the scheduled time
2. Wait for the next occurrence to verify that the notification repeats
3. Check the system logs to see if the notification is scheduled correctly

[T] On Android, you can also use the notification settings to see the scheduled notifications:

[List]
1. Open the system settings
2. Navigate to "Apps & notifications" > "See all apps" > "Timora" > "Notifications"
3. You should see the notification channels and any active notifications

[T] Up Next, we'll implement methods to manage periodic notifications, including cancelling and updating them.


Next Chapter Title
Managing Periodic Notifications

[T] In this chapter, we'll implement methods to manage periodic notifications, including cancelling and updating them.

[H2] Cancelling Periodic Notifications

[T] Periodic notifications continue to repeat until they're explicitly cancelled. We've already implemented the cancelNotification() method in the NotificationManager class, which works for all types of notifications, including periodic ones:

[Code: dart]
/// Cancels a notification with the specified ID.
///
/// Removes the notification from the system and prevents it from being displayed.
/// If the notification is already displayed, it will be removed from the notification drawer.
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
  debugPrint('Cancelled notification: $id');
}

[T] To cancel a periodic notification, simply call this method with the notification's ID:

[Code: dart]
// Cancel a notification with ID 123
await notificationManager.cancelNotification(123);

[T] This will stop the notification from repeating and remove it from the notification drawer if it's currently displayed.

[H2] Updating Periodic Notifications

[T] To update a periodic notification, you can cancel the existing notification and schedule a new one with the same ID but different properties. Let's implement an updateNotification method in the NotificationManager class:

[Code: dart]
/// Updates an existing notification with new properties.
///
/// Cancels the existing notification and schedules a new one with the same ID.
Future<void> updateNotification(NotificationModel model) async {
  await cancelNotification(model.id);

  switch (model.type) {
    case NotificationType.instant:
      await showInstantNotification(model: model);
      break;

    case NotificationType.scheduled:
      await scheduleNotification(model: model);
      break;

    case NotificationType.periodic:
      await showPeriodicNotification(model: model);
      break;
  }

  debugPrint('Updated notification: ${model.id}');
}

[T] This method cancels the existing notification and schedules a new one based on the notification type. For example, to update a daily notification to a weekly notification:

[Code: dart]
// Get the existing notification model
final existingModel = await notificationManager.getNotification(123);

// Update the properties
final updatedModel = existingModel.copyWith(
  repeatInterval: RepeatInterval.weekly,
  dayOfWeek: 1, // Monday
  timeOfDay: const TimeOfDay(hour: 10, minute: 0),
);

// Update the notification
await notificationManager.updateNotification(updatedModel);

[T] This will cancel the existing daily notification and schedule a new weekly notification with the same ID.

[H2] Implementing the Notification Details Page

[T] To make it easier for users to manage their notifications, let's implement a notification details page that allows them to view, update, and cancel notifications.

[T] The NotificationDetailsPage class displays the details of a notification and provides buttons to cancel or edit it:

[Code: dart]
class NotificationDetailsPage extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsPage({
    Key? key,
    required this.notificationId,
  }) : super(key: key);

  @override
  _NotificationDetailsPageState createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends State<NotificationDetailsPage> {
  late Future<NotificationModel?> _notificationFuture;
  final _notificationManager = NotificationManager();

  @override
  void initState() {
    super.initState();
    _notificationFuture = _loadNotification();
  }

  Future<NotificationModel?> _loadNotification() async {
    return await _notificationManager.getNotification(widget.notificationId);
  }

  Future<void> _cancelNotification(NotificationModel model) async {
    await _notificationManager.cancelNotification(model.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification cancelled'),
      ),
    );
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Details'),
      ),
      body: FutureBuilder<NotificationModel?>(
        future: _notificationFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          final notification = snapshot.data;
          if (notification == null) {
            return const Center(
              child: Text('Notification not found'),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  notification.title,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(notification.body),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                _buildNotificationDetails(notification),
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () => _cancelNotification(notification),
                      child: const Text('Cancel Notification'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Navigate to edit page
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditNotificationPage(
                              notification: notification,
                            ),
                          ),
                        ).then((_) {
                          // Refresh the notification details
                          setState(() {
                            _notificationFuture = _loadNotification();
                          });
                        });
                      },
                      child: const Text('Edit Notification'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNotificationDetails(NotificationModel notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type: ${notification.type.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Channel: ${notification.channelId}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Level: ${notification.level.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (notification.type == NotificationType.scheduled)
          Text(
            'Scheduled for: ${DateFormat('MMM d, yyyy HH:mm').format(notification.scheduledTime!)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (notification.type == NotificationType.periodic)
          _buildPeriodicDetails(notification),
      ],
    );
  }

  Widget _buildPeriodicDetails(NotificationModel notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Repeat Interval: ${notification.repeatInterval.toString().split('.').last}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        if (notification.repeatInterval == RepeatInterval.daily ||
            notification.repeatInterval == RepeatInterval.weekly)
          Text(
            'Time: ${notification.timeOfDay!.hour}:${notification.timeOfDay!.minute.toString().padLeft(2, '0')}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (notification.repeatInterval == RepeatInterval.weekly &&
            notification.dayOfWeek != null)
          Text(
            'Day of Week: ${_getDayOfWeekName(notification.dayOfWeek!)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
      ],
    );
  }

  String _getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }
}

[T] This page displays the details of a notification, including its type, channel, level, and repetition settings. It also provides buttons to cancel or edit the notification.

[T] Up Next, we'll summarize what we've learned in this section.


Last Chapter Title
Section Summary

[T] Let's summarize what we have covered in this section:
[List]
We learned about recurring and periodic notifications and their different types
We implemented the showPeriodicNotification() method to handle both calendar-based and fixed-interval notifications
We created helper methods to calculate the next occurrence of a notification
We enhanced the NotificationBuilder with methods for different repetition patterns
We updated the UI to allow users to select repetition options
We implemented methods to manage periodic notifications, including cancelling and updating them
We created a notification details page to view and manage notifications

[T] You now have a solid understanding of how to implement recurring and periodic notifications in Flutter. Your app can now deliver notifications that repeat at regular intervals, with full control over the repetition pattern.

[T] The periodic notifications we've implemented provide a powerful way to keep users engaged with your app by delivering timely reminders and updates. Whether it's a daily medication reminder, a weekly meeting notification, or an hourly alert, you now have the tools to create notifications that meet your users' needs.

[T] Up Next, we'll dive into customizing notification appearance and behavior, where you'll learn how to create rich notifications with images, actions, and more.
