[H1] Getting Started with Local Notifications in Flutter

<!-- Chapter 1: Section Overview -->
[H2] Section Overview

[T] In this section, you'll explore:
[List]
What are local notifications and why they're essential for mobile apps
How to set up the flutter_local_notifications package in your Flutter project
The structure of the starter project and its notification components
How to run and test the starter project to ensure everything is working correctly

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section.

[T] Let's begin!


<!-- Chapter 2: Understanding Local Notifications and the flutter_local_notifications Package -->
[H2] Understanding Local Notifications

[T] In this chapter, we'll explore what local notifications are, their benefits, and common use cases in mobile applications.

[Image: A smartphone showing various local notifications in the notification tray, highlighting how they appear to users]

[H3] What Are Local Notifications?
[T] Local notifications are messages that appear on a user's device, initiated by the app itself rather than from a remote server. They appear in the notification tray and can include text, images, and interactive elements.

[T] There are two primary types of notifications in mobile applications:

[List]
**Local Notifications**: Generated and displayed by the app itself, without requiring an internet connection or external server. These are scheduled and triggered directly on the device.
**Remote Notifications** (Push Notifications): Sent from a server to the device, requiring an internet connection and a push notification service like Firebase Cloud Messaging (FCM) or Apple Push Notification Service (APNS).

[T] In this codelab, we'll focus exclusively on local notifications, which are perfect for:

[List]
Reminders and alarms
Task due dates
Workout or medication schedules
Progress updates for long-running operations
Offline functionality

[H3] Benefits of Local Notifications
[T] Local notifications offer several advantages for both users and developers:

[List]
**User Engagement**: Keep users informed and engaged with your app, even when they're not actively using it
**Timely Reminders**: Deliver time-sensitive information exactly when it's needed
**Offline Functionality**: Work without an internet connection, ensuring reliability
**User Control**: Allow users to manage which notifications they receive and how they appear
**Privacy**: Process all data locally, without sending information to external servers
**Battery Efficiency**: Consume less battery than constantly polling a server for updates

[H3] Common Use Cases
[T] Local notifications are used in various types of applications:

[List]
**Productivity Apps**: Task reminders, meeting notifications, deadline alerts
**Health & Fitness Apps**: Workout reminders, medication schedules, hydration alerts
**Calendar Apps**: Event reminders, appointment notifications
**Messaging Apps**: Message notifications when the app is in the background
**Media Apps**: Download completion notifications, new content alerts
**Games**: Daily rewards, energy refill notifications, event reminders

[H3] Exploring the flutter_local_notifications Package

[T] Now let's dive into the flutter_local_notifications package, which makes implementing these features straightforward in Flutter applications. We'll explore its features and how it's structured to help us implement notifications in our Flutter app.

[H3] Introduction to the Package
[T] The flutter_local_notifications package is a cross-platform plugin for displaying local notifications in Flutter applications. It provides a unified API for both Android and iOS, while still allowing platform-specific customizations.

[Image: Diagram showing how flutter_local_notifications bridges Flutter code to native notification APIs on Android and iOS]

[T] This package simplifies the complex task of handling notifications across different platforms, abstracting away many of the platform-specific details while still providing access to advanced features when needed.

[H3] Key Features of the Package
[T] The package offers a wide range of features:

[List]
**Cross-Platform Support**: Works on both Android and iOS with a unified API
**Scheduling**: Schedule notifications for future delivery
**Customization**: Customize notification appearance with icons, sounds, and vibration patterns
**Notification Channels**: Support for Android notification channels
**Rich Content**: Display images and other rich content in notifications
**Interactive Actions**: Add buttons and input fields to notifications
**Deep Linking**: Navigate to specific screens when notifications are tapped

[H3] Package Structure
[T] The flutter_local_notifications package is organized into several components:

[List]
**FlutterLocalNotificationsPlugin**: The main class for initializing and displaying notifications
**NotificationDetails**: Configures platform-specific notification settings
**AndroidNotificationDetails/DarwinNotificationDetails**: Platform-specific notification configurations
**InitializationSettings**: Settings for initializing the plugin
**NotificationAppLaunchDetails**: Information about app launches from notifications

[T] Understanding these components will help us implement a robust notification system in our app.

<!-- Chapter 3: Native Configuration and Project Setup -->
[H2] Native Configuration and Project Setup

[H3] Native Configuration Requirements

[T] In this chapter, we'll explore the platform-specific configuration requirements for Android and iOS that are necessary for the flutter_local_notifications package to work properly.

[H3] Android Configuration
[T] Android requires several permissions and configurations in the AndroidManifest.xml file to enable various notification features:

[Image: Screenshot of AndroidManifest.xml showing the required permissions and configurations for notifications]

[List]
**Required Permissions**: Several permissions must be added to the AndroidManifest.xml file
**Notification Channels**: Required for Android 8.0 (API level 26) and higher
**Service Registration**: Required for certain notification features
**Boot Receiver**: Needed to reschedule notifications after device restart

[T] Let's look at the permissions needed in the AndroidManifest.xml file:

[Code: xml]
<!-- Basic notification permissions -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

<!-- For scheduled notifications -->
<uses-permission android:name="android.permission.USE_EXACT_ALARM" />

<!-- Required for Android 13+ -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

<!-- For foreground services -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

[T] Additionally, you need to register several components in the application section of your AndroidManifest.xml:

[Code: xml]
<!-- Foreground service for notifications -->
<service
    android:name="com.dexterous.flutterlocalnotifications.ForegroundService"
    android:exported="false"
    android:stopWithTask="false"
    android:foregroundServiceType="specialUse">
    <property
        android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
        android:value="To show notifications"/>
</service>

<!-- Receivers for notification actions and scheduling -->
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
    <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED"/>
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
    </intent-filter>
</receiver>

[H3] iOS Configuration
[T] iOS also requires specific configurations in the Info.plist file and AppDelegate.swift file:

[Image: Screenshot of AppDelegate.swift showing the required code for handling notifications in iOS]

[List]
**Info.plist Entries**: Permission descriptions and background modes
**AppDelegate Configuration**: Setup for handling notification actions
**Notification Categories**: Required for interactive notifications

[T] In the AppDelegate.swift file, you need to add the following code:

[Code: swift]
import Flutter
import UIKit
import flutter_local_notifications

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    // This is required to make any communication available in the action isolate.
    FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
        GeneratedPluginRegistrant.register(with: registry)
    }

    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

[Emphasis, bulb]
These native configurations are essential for the proper functioning of notifications in your app. In the starter project, you'll find TODOs in the AndroidManifest.xml and AppDelegate.swift files reminding you to add these configurations.


[H3] Setting Up the Starter Project

[T] Now let's set up the starter project for our codelab and explore its structure to understand how the notification system will be implemented.

[H3] Cloning the Repository
[T] First, let's clone the repository containing the starter project:

[Code: bash]
git clone https://github.com/yourusername/flutter-local-notifications-ios-android.git
cd flutter-local-notifications-ios-android

[T] The starter project is located in the **source-code/starter-project/timora** directory. Navigate to this directory and open it in your preferred IDE:

[Code: bash]
cd source-code/starter-project/timora
flutter pub get

[H3] Project Structure Overview
[T] Take a moment to explore the project structure:

[Image: Screenshot of the project structure in an IDE, showing the main directories and files]

[List]
**lib/main.dart**: The entry point of the application
**lib/model/notification_model.dart**: The data model for notifications
**lib/service/notification-manager/**: Contains the notification manager and builder classes
**lib/view/**: Contains the UI screens for creating and managing notifications
**lib/core/**: Contains utility classes, constants, and routing

[T] The project follows a clean architecture pattern with clear separation of concerns:

[List]
**Models**: Data structures representing notifications
**Services**: Business logic for managing notifications
**Views**: UI components for user interaction
**Core**: Utilities, constants, and helpers

[H3] Understanding the TODOs
[T] The starter project contains several TODOs that guide you through the implementation process. Let's look at some of the key TODOs:

[List]
**main.dart**: TODO to initialize the notification manager
**notification_manager.dart**: TODOs for implementing initialization, permissions, and notification display methods
**notification_builder.dart**: TODOs for implementing the show method and other notification features
**AndroidManifest.xml**: TODOs for adding required permissions and configurations
**AppDelegate.swift**: TODOs for setting up iOS notification handling
**Info.plist**: TODOs for adding notification-related keys

[T] Let's look at the TODO in main.dart:

[Code: dart]
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // TODO: Initialize notification manager
    // This is where you'll initialize the notification system
    // Uncomment the line below when you've implemented the notification manager
    // await NotificationManager().init();

    // Initialize deep link handler
    DeepLinkHandler.instance.init();

    runApp(const App());
  } catch (e) {
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(child: Text('Failed to initialize app: $e')),
        ),
      ),
    );
  }
}

[T] In the notification_manager.dart file, you'll find TODOs for implementing various methods:

[Code: dart]
/// Initializes timezone data for scheduling notifications.
///
/// Sets up the local timezone to ensure scheduled notifications appear at
/// the correct time.
Future<void> _initializeTimeZones() async {
  // TODO: Initialize timezone data
  // 1. Initialize timezone database
  // 2. Get the local timezone
  // 3. Set the local location
}

/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  // TODO: Configure platform-specific settings
  // 1. Set up Android settings with an icon
  // 2. Set up iOS settings with permissions and categories
  // 3. Create initialization settings for both platforms
  // 4. Initialize the plugin with the settings
  // 5. Configure notification action handling
}

[H3] Understanding the Dependencies
[T] Open the **pubspec.yaml** file to see the dependencies we'll be using:

[Code: yaml]
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_local_notifications: ^18.0.1
  timezone: ^0.10.0
  flutter_timezone: ^4.1.0
  app_links: ^6.4.0
  http: ^1.3.0
  path_provider: ^2.1.5
  intl: ^0.20.2

[T] Let's understand the key dependencies:

[List]
**flutter_local_notifications**: The main package for handling local notifications
**timezone** and **flutter_timezone**: For handling scheduled notifications across different time zones
**app_links**: For handling deep links from notifications
**intl**: For date and time formatting
**path_provider**: For accessing the file system (used for storing notification images)
**http**: For network requests (used in some advanced notification features)

<!-- Chapter 4: Notification Models and Architecture -->
[H2] Notification Models and Architecture

[H3] Understanding the Notification Model

[T] In this chapter, we'll dive into the notification model that represents the data structure for our notifications, helping us understand how notifications are configured and managed in our app.

[H3] The NotificationModel Class
[T] Before we start implementing notifications, let's understand the data model that represents a notification in our app.

[Image: Diagram showing the structure of the NotificationModel class and its relationships with other classes]

[T] Open **lib/model/notification_model.dart** and examine the NotificationModel class:

[Code: dart]
class NotificationModel {
  final int id;
  final String title;
  final String body;
  final String channelId;
  final NotificationType type;
  final NotificationLevel level;
  final DateTime? scheduledTime;
  final TimeOfDay? timeOfDay;
  final int? dayOfWeek;
  final RepeatInterval? repeatInterval;
  final Duration? periodDuration;
  final int? occurrenceCount;
  final bool isFullScreen;
  final bool hasActions;
  final bool imageAttachment;
  final int? maxProgress;
  final int? currentProgress;
  final String? payload;
  final String? groupKey;
  final bool customSound;
  final String? deepLink;
  final Int64List? vibrationPattern;

  // Constructor and methods...
}

[T] This class represents all the information needed to create and display a notification:

[List]
**Basic Properties**: id, title, body, channelId, type, level
**Scheduling Properties**: scheduledTime, timeOfDay, dayOfWeek, repeatInterval, periodDuration, occurrenceCount
**Feature Flags**: isFullScreen, hasActions, imageAttachment, customSound
**Advanced Properties**: maxProgress, currentProgress, payload, groupKey, deepLink, vibrationPattern

[H3] Constructor and Methods
[T] The NotificationModel class includes a constructor with default values and several utility methods:

[Code: dart]
const NotificationModel({
  required this.id,
  required this.title,
  required this.body,
  required this.channelId,
  required this.type,
  this.level = NotificationLevel.normal,
  this.scheduledTime,
  this.timeOfDay,
  this.dayOfWeek,
  this.repeatInterval,
  this.periodDuration,
  this.occurrenceCount,
  this.isFullScreen = false,
  this.hasActions = false,
  this.imageAttachment = false,
  this.maxProgress,
  this.currentProgress,
  this.payload,
  this.groupKey,
  this.customSound = false,
  this.deepLink,
  this.vibrationPattern,
});

// Creates a copy of this model with the given fields replaced
NotificationModel copyWith({...}) {...}

// Converts the model to a JSON string for storage as a notification payload
String toPayload() {...}

// Creates a model from a payload string
factory NotificationModel.fromPayload(String payload) {...}

[Emphasis, edit]
The copyWith method is particularly important as it allows us to create new instances with modified properties while preserving immutability. This is used extensively in the NotificationBuilder class to configure notifications in a fluent API style.


[H3] Notification Types and Levels
[T] The NotificationModel uses enums to represent different types and importance levels of notifications:

[Code: dart]
enum NotificationType {
  instant,
  scheduled,
  periodic,
}

enum NotificationLevel {
  low,
  normal,
  urgent,
}

enum RepeatInterval {
  everyMinute,
  hourly,
  daily,
  weekly,
  monthly,
  custom,
}

[T] These enums help categorize notifications and determine their behavior:

[List]
**NotificationType**: Determines when the notification is displayed (immediately, at a scheduled time, or periodically)
**NotificationLevel**: Determines the importance and visibility of the notification
**RepeatInterval**: Determines how often a periodic notification repeats

[H3] Enhanced Enum Implementation
[T] The project implements NotificationLevel as an enhanced enum with properties directly included in the enum definition:

[Code: dart]
enum NotificationLevel {
  /// Standard notification level with default importance.
  ///
  /// Plays sound, vibrates, and is publicly visible.
  normal(
    importance: Importance.defaultImportance,
    priority: Priority.defaultPriority,
    playSound: true,
    vibrate: true,
    visibility: NotificationVisibility.public,
  ),

  /// Low-prominence notification level.
  ///
  /// Doesn't play sound or vibrate, and has restricted visibility.
  low(
    importance: Importance.low,
    priority: Priority.low,
    playSound: false,
    vibrate: false,
    visibility: NotificationVisibility.private,
  ),

  /// High-priority notification level for important alerts.
  ///
  /// Uses maximum importance, plays sound, vibrates, and is publicly visible.
  urgent(
    importance: Importance.max,
    priority: Priority.high,
    playSound: true,
    vibrate: true,
    visibility: NotificationVisibility.public,
  );

  final Importance importance;
  final Priority priority;
  final bool playSound;
  final bool vibrate;
  final NotificationVisibility visibility;

  const NotificationLevel({
    required this.importance,
    required this.priority,
    required this.playSound,
    required this.vibrate,
    required this.visibility,
  });
}

[T] This enhanced enum approach simplifies the code when configuring platform-specific notification details, making it easier to access the appropriate platform-specific settings directly from the enum instance.

[H3] Exploring the Notification Builder and Manager

[T] Next, let's examine the notification builder and manager classes that will be responsible for creating and displaying notifications in our app.

[H3] The NotificationBuilder Class
[T] The starter project includes a NotificationBuilder class that provides a fluent API for creating and configuring notifications. Let's examine this class.

[T] Open **lib/service/notification-manager/notification_builder.dart**:

[Code: dart]
class NotificationBuilder {
  final NotificationManager _manager;
  NotificationModel _model;

  NotificationBuilder._(this._manager, this._model);

  /// Creates a basic notification builder with required information.
  factory NotificationBuilder.create(
    NotificationManager manager, {
    required int id,
    required String title,
    required String body,
    required String channelId,
    NotificationLevel level = NotificationLevel.normal,
  }) {
    // Validation and initialization...
  }

  // Configuration methods...

  /// Shows the notification immediately or at the configured time.
  Future<void> show() async {
    // This is a placeholder - you'll implement this in the codelab
    debugPrint('Notification show method not implemented yet');
  }
}

[T] This class uses the Builder pattern to provide a fluent API for configuring notifications. It includes methods for:

[List]
**Creating notifications**: The create factory method initializes a basic notification
**Configuring properties**: Methods like setFullScreen, setImage, setActions, etc.
**Scheduling**: Methods like scheduleFor, setRepeatInterval, atTimeOfDay, etc.
**Showing and cancelling**: The show and cancel methods

[H3] Key TODOs in NotificationBuilder
[T] The NotificationBuilder class contains several TODOs that you'll need to implement during this codelab. Let's look at some of the most important ones:

[Code: dart]
/// Sets a default deeplink to the notification details page.
///
/// This is a convenience method that generates a deeplink to the notification details page
/// using the notification ID.
///
/// TODO: Implement this method to generate a deeplink to the notification details page
/// 1. Generate a deeplink URL that includes the notification ID
/// 2. Update the model with the new deeplink
/// 3. Return this builder instance for method chaining
NotificationBuilder setDefaultDeepLink() {
  // This is a placeholder - you'll implement this in the codelab
  return this;
}

[T] Another important TODO is in the show() method, which is responsible for displaying the notification based on its type:

[Code: dart]
/// Shows the notification immediately or at the configured time.
///
/// The behavior depends on the notification type (instant, scheduled, periodic).
///
/// Throws an [ArgumentError] if required properties for the notification type are missing.
///
/// TODO: Implement this method to show the notification based on its type
/// 1. Check the notification type (instant, scheduled, periodic)
/// 2. Call the appropriate method on the NotificationManager
/// 3. Handle any errors or missing properties
Future<void> show() async {
  // This is a placeholder - you'll implement this in the codelab
  debugPrint('Notification show method not implemented yet');
}

[H3] The NotificationManager Class
[T] The NotificationManager class is responsible for initializing the notification plugin and handling all notification operations. Let's examine this class.

[T] Open **lib/service/notification-manager/notification_manager.dart**:

[Code: dart]
class NotificationManager {
  // Singleton pattern implementation
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  // Initialization methods
  Future<void> init() async {
    if (_isInitialized) return;

    await _initializeTimeZones();
    await _initializeNotificationSettings();
    await _setupNotificationChannels();

    _isInitialized = true;
  }

  // Other methods...
}

[T] This class uses the Singleton pattern to ensure that only one instance exists throughout the app. It includes methods for:

[List]
**Initialization**: The init method sets up the notification plugin
**Showing notifications**: Methods for showing instant, scheduled, and periodic notifications
**Managing notifications**: Methods for cancelling and retrieving notifications

[H3] Key TODOs in NotificationManager
[T] The NotificationManager class contains several TODOs that you'll need to implement during this codelab. Let's look at some of the most important ones:

[Code: dart]
/// Shows an instant notification immediately.
///
/// Displays a notification with the configuration specified in [model].
/// Validates prerequisites before showing the notification.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  // TODO: Implement instant notification display
  // 1. Check if the plugin is initialized
  // 2. Request notification permissions if needed
  // 3. Configure notification details based on the model
  // 4. Show the notification with the plugin
  // 5. Include the model payload for handling notification taps
}

/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  // TODO: Implement scheduled notification
  // 1. Check if the notification should be enabled (cancel if not)
  // 2. Validate the scheduled time is not null
  // 3. Check notification prerequisites (initialization and permissions)
  // 4. Convert the DateTime to a TZDateTime with proper timezone handling
  // 5. Configure notification details
}

[T] Throughout this codelab, we'll implement these methods to create a complete notification system.

[H3] How the Classes Work Together
[T] The NotificationBuilder and NotificationManager classes work together to provide a clean, fluent API for creating and displaying notifications:

[Code: mermaid]
flowchart LR
    A[App Code] -->|1. Creates| B[NotificationManager]
    A -->|2. Calls| C[createNotification]
    C -->|3. Returns| D[NotificationBuilder]
    D -->|4. Configures with\nfluent API| D
    D -->|5. Calls show()| E[show method]
    E -->|6. Delegates to| F[NotificationManager methods]
    F -->|7. Displays| G[Notification on device]

[T] The diagram shows how the NotificationBuilder and NotificationManager work together. The app code creates a NotificationManager instance, then calls its createNotification method to get a NotificationBuilder. The builder allows chaining configuration methods, and when show() is called, it delegates back to the appropriate NotificationManager method to display the notification.

[List]
1. The NotificationManager provides a createNotification method that returns a NotificationBuilder instance
2. The NotificationBuilder allows chaining configuration methods to set up the notification
3. When show() is called on the builder, it delegates to the appropriate method on the NotificationManager
4. The NotificationManager handles the platform-specific details of displaying the notification

[Emphasis, tick]
This separation of concerns makes the code more maintainable and easier to understand.


[H3] Running and Exploring the Starter Project

[T] Now let's run the starter project to ensure everything is set up correctly and explore the UI that we'll be working with throughout this codelab:

[Code: bash]
cd source-code/starter-project/timora
flutter run

[T] You should see the Timora app running with a complete UI, but without any notification functionality. The app has a clean, modern interface with screens for creating and managing notifications.

[Image: Screenshot of the Timora app's home screen showing the empty notification list and create button]

[T] If you try to create a notification at this point, nothing will happen because we haven't implemented the notification functionality yet. Throughout this codelab, we'll implement all the notification features step by step.

[H3] Exploring the UI
[T] Let's explore the main screens of the app:

[List]
**Home Screen**: Displays a list of scheduled notifications and provides options to create new ones
**Create Notification Screen**: Allows users to configure and create new notifications with various options
**Notification Details Screen**: Shows details of a specific notification and allows editing or deleting it

[T] The app also includes various UI components for selecting notification options:

[List]
**Channel Selector**: For choosing the notification channel (Work, Personal, Health)
**Type Selector**: For selecting the notification type (instant, scheduled, periodic)
**Date and Time Pickers**: For scheduling notifications at specific times
**Priority Selector**: For setting the notification importance level (Low, Normal, High, Critical)
**Feature Toggles**: For enabling features like full-screen notifications, custom sounds, and actions

[H3] Examining the Home Screen
[T] The home screen is the main entry point of the app. It displays a list of scheduled notifications and provides buttons to create new ones.

[Image: Screenshot of the home screen with annotations pointing to key UI elements like the notification list, create button, and refresh button]

[T] Key components of the home screen include:

[List]
**Notification List**: Shows pending notifications with their titles, bodies, and scheduled times
**Create Button**: Opens the create notification screen
**Refresh Button**: Refreshes the list of pending notifications
**Notification Cards**: Tapping a notification card opens its details

[T] When you run the app, you'll notice that the notification list is empty because we haven't implemented the functionality to create and schedule notifications yet.

[H3] Examining the Create Notification Screen
[T] The create notification screen allows users to configure and create new notifications. It includes various form fields and options for customizing notifications.

[Image: Screenshot of the create notification screen showing the form fields and configuration options]

[T] Key components of this screen include:

[List]
**Title and Body Fields**: For entering the notification text
**Channel Selector**: For choosing the notification category
**Type Selector**: For selecting when the notification should appear
**Schedule Options**: For configuring when scheduled or periodic notifications should appear
**Priority Selector**: For setting the importance level
**Feature Toggles**: For enabling additional notification features
**Create Button**: For creating and scheduling the notification

[Emphasis, question mark]
Currently, the create button doesn't do anything because we haven't implemented the notification functionality yet. Throughout this codelab, we'll implement the missing functionality step by step.


[H3] Understanding the TODOs
[T] Throughout the codebase, you'll find TODO comments that indicate where you need to implement functionality. These comments provide guidance on what needs to be done.

[T] For example, in the NotificationManager class, you'll find TODOs for implementing the init() method, showing notifications, and handling notification taps.

[T] As we progress through this codelab, we'll implement these TODOs one by one to build a complete notification system.

[H3] Code Challenge: Exploring the Notification Model

[T] Let's tackle a code challenge to test your understanding of the concepts we've covered so far. You'll get a chance to practice what you've learned with a challenge focused on understanding the notification model.

[H3] The Challenge
[T] Your challenge is to explore the NotificationModel class and answer the following questions:

[List]
1. What properties are required to create a basic notification?
2. How would you create a scheduled notification for tomorrow at 9:00 AM?
3. What enum values would you use for a high-priority notification that repeats daily?
4. How would you configure a notification to show as a full-screen alert?
5. How would you add a deeplink to a notification?

[T] Take some time to review the NotificationModel class and its properties before answering these questions.

[H3] Solution
[T] Let's go through the answers to the challenge:

[List]
1. **Required Properties**: id, title, body, channelId, and type are required for a basic notification. The level property has a default value of NotificationLevel.normal.

2. **Scheduled Notification**: To create a scheduled notification for tomorrow at 9:00 AM, you would:
   - Set type to NotificationType.scheduled
   - Set scheduledTime to DateTime.now().add(Duration(days: 1)).copyWith(hour: 9, minute: 0, second: 0)

3. **High-Priority Daily Notification**: You would use:
   - NotificationLevel.urgent for the priority
   - NotificationType.periodic for the type
   - RepeatInterval.daily for the repeat interval

4. **Full-Screen Alert**: To configure a notification as a full-screen alert, you would:
   - Set isFullScreen to true
   - Typically, you would also set the level to NotificationLevel.urgent

5. **Adding a Deeplink**: To add a deeplink to a notification, you would:
   - Set the deepLink property to a URI string (e.g., "timora://notification/123")
   - In the final project, you can use the setDefaultDeepLink() method on NotificationBuilder to generate a deeplink to the notification details page

[T] This challenge helps you understand how to configure different types of notifications using the NotificationModel class.

[H3] Implementing the Solution
[T] In the final project, you would create these notifications using the NotificationBuilder class. For example, to create a scheduled notification for tomorrow at 9:00 AM:

[Image: Diagram showing the flow of creating a notification using the NotificationBuilder class]

[Code: dart]
// Get the notification manager instance
final notificationManager = NotificationManager();

// Create a notification builder
final builder = notificationManager.createNotification(
  id: 123,
  title: "Meeting Reminder",
  body: "Don't forget your team meeting tomorrow",
  channelId: "work",
  level: NotificationLevel.urgent,
);

// Schedule it for tomorrow at 9:00 AM
final tomorrow = DateTime.now().add(Duration(days: 1));
final scheduledTime = DateTime(
  tomorrow.year,
  tomorrow.month,
  tomorrow.day,
  9, // 9:00 AM
  0,
);

builder
  .scheduleFor(scheduledTime)
  .setFullScreen(true)
  .setDefaultDeepLink()
  .show();

<!-- Chapter 5: Section Summary -->
[H2] Section Summary

[T] Let's summarize what we have covered in this section:
[List]
We explored what local notifications are and their benefits for mobile applications
We learned about the flutter_local_notifications package and its key features
We examined the native configuration requirements for Android and iOS
We set up the starter project and examined its structure
We identified the TODOs in the starter project that we'll need to implement
We understood the NotificationModel class and how it represents notifications
We explored the NotificationBuilder and NotificationManager classes
We ran the starter project and explored its UI
We practiced configuring different types of notifications in a code challenge

[T] You now have a solid understanding of the foundation of our notification system. You understand what local notifications are, how they're represented in our app, and the structure of the classes we'll be implementing.

[H3] Key Takeaways
[T] Here are the key takeaways from this section:

[Emphasis, download]
**Platform-Specific Configuration**: Both Android and iOS require specific configuration in their native files (AndroidManifest.xml, AppDelegate.swift, Info.plist)

**Notification Model**: The NotificationModel class represents all the information needed to create and display a notification

**Builder Pattern**: The NotificationBuilder class provides a fluent API for configuring notifications

**Singleton Pattern**: The NotificationManager class uses the Singleton pattern to ensure only one instance exists

**TODOs**: The starter project contains numerous TODOs that guide you through the implementation process


[H3] What's Next
[T] In the next sections, we'll start implementing the notification functionality, beginning with instant notifications that appear immediately when triggered. We'll then progress to more advanced features like scheduled notifications, notification channels, and interactive notifications.

[T] Here's what you can expect in the upcoming sections:

[List]
**Section 2**: Implementing instant notifications and initializing the notification system
**Section 3**: Implementing scheduled and periodic notifications
**Section 4**: Adding advanced features like full-screen notifications, custom sounds, and notification actions
**Section 5**: Handling notification taps and deep links

[T] Up Next, we'll dive into implementing instant notifications, where you'll learn how to initialize the notification plugin and display your first notification.
