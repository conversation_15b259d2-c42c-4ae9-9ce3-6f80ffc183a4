Write Your eBook

Section 4 eBook
Section Title
Scheduling Notifications with Time Zone Awareness



Chapter Title 
Section Overview 

[H2] Section Overview 
[T] In this section, you'll explore: 
[List] 
Understanding the importance of time zone handling in scheduled notifications
Initializing time zone data using the timezone package
Implementing scheduled notifications with time zone awareness
Adding date and time pickers to the UI for scheduling notifications
Testing scheduled notifications across different time zones

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section. 

[T] Let's begin! 



Next Chapter Title 
Understanding Time Zone Handling in Notifications

[H2] Understanding Time Zone Handling in Notifications

[T] In this chapter, we'll explore why time zones are crucial for scheduled notifications and how to properly handle them in your Flutter app.
[H3] Why Time Zones Matter
[T] Time zones are crucial for scheduled notifications. Consider these scenarios:
[List]
A user schedules a notification for 8:00 AM and then travels to a different time zone
A user in New York schedules a notification for a friend in London
A notification is scheduled for a specific global event (like a live stream)

[T] In each case, we need to decide whether the notification should appear at the local time (8:00 AM in the current time zone) or at a specific absolute time (8:00 AM in the original time zone).

[T] For most reminder apps, the first scenario is the most common: users want their notifications to appear at the same local time, regardless of their current time zone. For example, a "Take medication at 8:00 AM" reminder should appear at 8:00 AM local time, even if the user travels to a different time zone.
[H3] Time Zone Libraries
[T] To handle time zones correctly, we're using two packages:
[List]
timezone: Provides time zone data and utilities for working with time zones
flutter_timezone: Helps determine the device's local time zone

[T] These packages allow us to:
[List]
Initialize the time zone database with the latest data
Get the device's local time zone
Convert between different time zones
Schedule notifications with proper time zone handling
[H3] Reviewing Time Zone Initialization
[T] We've already implemented the _initializeTimeZones() method in the previous section. Let's review it to ensure it's correctly set up:

[Code: dart]
/// Initializes timezone data for scheduling notifications.
///
/// Sets up the local timezone to ensure scheduled notifications appear at
/// the correct time.
Future<void> _initializeTimeZones() async {
  tz_data.initializeTimeZones();
  final String timeZoneName = await FlutterTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZoneName));
}

[T] This method initializes the time zone database and sets the default time zone to the device's local time zone. It's simple but essential for ensuring that scheduled notifications appear at the correct time.

[T] Up Next: We'll implement the core functionality for scheduling notifications with proper time zone awareness.




Chapter Title 
Implementing Scheduled Notifications

[H2] Implementing Scheduled Notifications
[T] In this chapter, we'll implement the core functionality for scheduling notifications at specific times with proper time zone handling.
[H3] The scheduleNotification Method
[T] Let's implement the scheduleNotification() method in the NotificationManager class. This method will schedule a notification to appear at a specific time in the future.

[Code: dart]
/// Schedules a notification for a specific future time.
///
/// Configures a notification to be delivered at the time specified in
/// [model.scheduledTime]. If [enabled] is `false`, cancels any existing
/// notification with the same ID.
///
/// Throws an [ArgumentError] if [model.scheduledTime] is null.
Future<void> scheduleNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot schedule notification: prerequisites not met');
    return;
  }

  if (model.scheduledTime == null) {
    throw ArgumentError(
      'scheduledTime must be provided for scheduled notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  final tz.TZDateTime scheduledDate = tz.TZDateTime.from(
    notificationWithDeepLink.scheduledTime!,
    tz.local,
  );

  final details = await getNotificationDetailsConfig(
    channelId: notificationWithDeepLink.channelId,
    level: notificationWithDeepLink.level,
    isFullScreen: notificationWithDeepLink.isFullScreen,
    imageAttachment: notificationWithDeepLink.imageAttachment,
    hasActions: notificationWithDeepLink.hasActions,
    customSound: notificationWithDeepLink.customSound,
  );

  await _flutterLocalNotificationsPlugin.zonedSchedule(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    scheduledDate,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    payload: notificationWithDeepLink.toPayload(),
  );
}

[T] This method is similar to showInstantNotification(), but with a few key differences:
[List]
It checks if the notification should be enabled (and cancels it if not)
It validates that scheduledTime is not null
It converts the DateTime to a TZDateTime for proper time zone handling
It uses zonedSchedule() instead of show() to schedule the notification
It configures additional parameters for scheduling behavior

[T] The androidScheduleMode parameter is set to exactAllowWhileIdle, which ensures the notification is delivered at the exact time, even if the device is in doze mode.

[T] The uiLocalNotificationDateInterpretation parameter is set to wallClockTime, which means the notification will be scheduled for the exact time specified, regardless of the device's time zone.
[H3] Implementing Notification Cancellation
[T] We referenced a cancelNotification() method in the scheduleNotification() method. Let's implement it now:

[Code: dart]
/// Cancels a specific notification by ID.
///
/// Removes both active and pending notifications with the specified [id].
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
}

[T] This method simply cancels a notification with the specified ID, preventing it from being displayed or removing it if it's already displayed.

[T] Up Next: We'll enhance the NotificationBuilder class to make it easier to create scheduled notifications.


Chapter Title 
Enhancing the Notification Builder 

[H2] Enhancing the Notification Builder
[T] In this chapter, we'll enhance the NotificationBuilder class to make it easier to create scheduled notifications with a fluent API.
[H3] Adding Scheduling Methods to NotificationBuilder
[T] Open lib/service/notification-manager/notification_builder.dart and examine the scheduling methods:

[Code: dart]
/// Schedules the notification to be delivered at a specific future time.
///
/// Changes the notification type to scheduled.
///
/// [scheduledTime] Time when the notification should be delivered
///
/// Throws an [ArgumentError] if the scheduled time is in the past.
NotificationBuilder scheduleFor(DateTime scheduledTime) {
  final now = DateTime.now();
  if (scheduledTime.isBefore(now)) {
    throw ArgumentError('Scheduled time cannot be in the past');
  }
  _model = _model.copyWith(
    type: NotificationType.scheduled,
    scheduledTime: scheduledTime,
  );
  return this;
}

[T] This method makes it easy to schedule a notification for a specific time. It validates that the scheduled time is not in the past and updates the notification model with the scheduled time.
[H3] Using the Enhanced NotificationBuilder
[T] With these enhancements, we can now create scheduled notifications with a clean, fluent API:

[Code: dart]
// Create a notification scheduled for tomorrow at 9:00 AM
final tomorrow = DateTime.now().add(Duration(days: 1));
final scheduledTime = DateTime(
  tomorrow.year,
  tomorrow.month,
  tomorrow.day,
  9, // 9 AM
  0, // 0 minutes
);

final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: "Meeting Reminder",
  body: "Don't forget your team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.scheduleFor(scheduledTime);

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for tomorrow at 9:00 AM with a clean, readable syntax.

[T] Up Next: We'll connect the UI to our scheduled notification implementation, allowing users to create scheduled notifications through the app interface.



Chapter Title 
Connecting the UI to Schedule Notifications

[H2] Connecting the UI to Scheduled Notifications
[T] In this chapter, we'll connect the UI components for date and time selection to our notification system, allowing users to create scheduled notifications through the app interface.
[H3] Understanding the Create Notification Screen
[T] Open lib/view/create-notification/create_notification_page.dart and examine the CreateNotificationPage class. This screen includes:
[List]
Text fields for the notification title and body
A dropdown for selecting the notification channel
A dropdown for selecting the notification type
Date and time pickers for scheduled notifications
A dropdown for selecting the notification importance level
A button to create the notification

[T] When the user selects "Scheduled" as the notification type, the date and time pickers become visible.
[H3] Implementing the Date and Time Pickers
[T] The date and time pickers are already implemented in the UI. Let's examine how they work:

[Code: dart]
Future<void> _pickDate() async {
  final date = await showDatePicker(
    context: context,
    initialDate: DateTime.now(),
    firstDate: DateTime.now(),
    lastDate: DateTime.now().add(const Duration(days: 365)),
  );

  if (date != null && mounted) {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null && mounted) {
      final dateTime = DateTime(
        date.year,
        date.month,
        date.day,
        time.hour,
        time.minute,
      );
      _controller.setScheduledDateTime(dateTime);
    }
  }
}

[T] This method shows a date picker followed by a time picker, then combines the selected date and time into a DateTime object and updates the controller.
[H3] Scheduling the Notification
[T] When the user taps the "Schedule Notification" button, the _scheduleNotification method is called:

[Code: dart]
Future<void> _scheduleNotification() async {
  if (!_controller.formKey.currentState!.validate()) return;

  // Validate fields based on notification type
  final validationResult = _controller.validateNotificationFields();
  if (!validationResult.isValid) {
    _showValidationError(validationResult.errorMessage!);
    return;
  }

  // Get prepared notification and show it
  final builder = _controller.prepareNotification();
  await builder.show();

  if (mounted) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification scheduled successfully!')),
    );
  }
}

[T] This method validates the form, prepares the notification using the controller, and shows it. Let's examine how the controller prepares the notification:

[Code: dart]
NotificationBuilder prepareNotification() {
  final value = this.value;
  
  // Create the base notification builder
  final builder = NotificationBuilder.create(
    NotificationManager(),
    id: DateTime.now().millisecondsSinceEpoch % 100000,
    title: value.title,
    body: value.body,
    channelId: value.channelId,
    level: value.level,
  );
  
  // Configure based on type
  switch (value.notificationType) {
    case 'Instant':
      // No additional configuration needed for instant notifications
      break;

    case 'Scheduled':
      builder = builder.scheduleFor(value.scheduledDateTime!);
      break;

    case 'Periodic':
      // We'll cover this in the next section
      break;
  }
  
  return builder;
}

[T] This method creates a notification builder with the basic properties, then configures it based on the selected notification type. For scheduled notifications, it uses the scheduleFor method we added earlier.

[T] Up Next: We'll implement weekly recurring notifications, which are a special type of scheduled notification.




Chapter Title 
Implementing Weekly Recurring Notifications

[H2] Implementing Weekly Recurring Notifications
[T] In this chapter, we'll implement weekly recurring notifications, which allow users to schedule notifications that repeat on a specific day of the week.
[H3] Understanding Recurring Notifications
[T] Recurring notifications are different from one-time scheduled notifications. Instead of occurring just once at a specific time, they repeat at regular intervals. Weekly notifications are a common type of recurring notification that repeat on the same day of the week at the same time.
[H3] Implementing Weekly Notifications
[T] In our app, we implement weekly notifications using a combination of methods in the NotificationBuilder class:

[Code: dart]
/// Configures the notification to repeat at a specific interval.
///
/// Changes the notification type to periodic.
///
/// [repeatInterval] How frequently the notification should repeat
NotificationBuilder setRepeatInterval(RepeatInterval repeatInterval) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: repeatInterval,
  );
  return this;
}

/// Sets a specific time of day for recurring notifications.
///
/// Used primarily with daily and weekly notifications to ensure they appear
/// at a consistent time.
///
/// [timeOfDay] The time when the notification should be shown
NotificationBuilder atTimeOfDay(TimeOfDay timeOfDay) {
  _model = _model.copyWith(timeOfDay: timeOfDay);
  return this;
}

/// Sets the day of week for weekly recurring notifications.
///
/// [dayOfWeek] Day of week (1-7, where 1 is Monday and 7 is Sunday)
///
/// Throws an [ArgumentError] if dayOfWeek is out of valid range.
NotificationBuilder onDayOfWeek(int dayOfWeek) {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw ArgumentError('Day of week must be between 1-7 (Monday to Sunday)');
  }
  _model = _model.copyWith(dayOfWeek: dayOfWeek);
  return this;
}

[T] To create a weekly notification, we need to use all three methods together:

[Code: dart]
// Create a notification scheduled for every Monday at 9:00 AM
final builder = NotificationBuilder.create(
  NotificationManager(),
  id: 456,
  title: "Weekly Team Meeting",
  body: "Don't forget your weekly team meeting",
  channelId: NotificationChannelIds.defaultChannel,
  level: NotificationLevel.normal,
)
.setRepeatInterval(RepeatInterval.weekly)
.atTimeOfDay(TimeOfDay(hour: 9, minute: 0))
.onDayOfWeek(1); // Monday

// Show the notification
await builder.show();

[T] This code creates a notification scheduled for every Monday at 9:00 AM.
[H3] How Weekly Notifications Work
[T] When we call show() on a builder configured for weekly notifications, it calls the showPeriodicNotification method in the NotificationManager class. This method handles the scheduling of recurring notifications:

[Code: dart]
Future<void> showPeriodicNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  // Validation and setup code...

  // For daily/weekly notifications with a specific time, use zonedSchedule with matchDateTimeComponents
  if (_shouldUseZonedSchedule(notificationWithDeepLink)) {
    await _scheduleRecurringAtTime(notificationWithDeepLink, details);
  } else {
    // For simpler repeating notifications without specific time requirements
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      notificationWithDeepLink.id,
      notificationWithDeepLink.title,
      notificationWithDeepLink.body,
      notificationWithDeepLink.repeatInterval!,
      details,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: notificationWithDeepLink.toPayload(),
    );
  }
}

[T] For weekly notifications, the _shouldUseZonedSchedule method returns true, so the _scheduleRecurringAtTime method is called:

[Code: dart]
Future<void> _scheduleRecurringAtTime(
  NotificationModel model,
  NotificationDetails details,
) async {
  // Calculate the next occurrence based on the model properties
  tz.TZDateTime nextOccurrence = _calculateNextOccurrence(
    timeOfDay: model.timeOfDay!,
    dayOfWeek:
        model.repeatInterval == RepeatInterval.weekly
            ? model.dayOfWeek
            : null,
  );

  // Determine the recurrence pattern
  DateTimeComponents matchDateTimeComponents =
      model.repeatInterval == RepeatInterval.daily
          ? DateTimeComponents.time
          : DateTimeComponents.dayOfWeekAndTime;

  // Schedule the notification with the recurrence pattern
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    nextOccurrence,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    matchDateTimeComponents: matchDateTimeComponents,
    payload: model.toPayload(),
  );
}

[T] This method calculates the next occurrence of the notification based on the day of week and time of day, then schedules it with the matchDateTimeComponents parameter set to DateTimeComponents.dayOfWeekAndTime. This tells the system to repeat the notification on the same day of the week at the same time.

[T] Up Next: We'll test our scheduled notifications implementation to ensure it works correctly across different scenarios
Chapter Title 
Testing Schedule Notifications

[H2] Testing Scheduled Notifications
[T] In this chapter, we'll test our scheduled notifications implementation to ensure it works correctly across different scenarios.
[H3] Creating a Scheduled Notification
[T] Run the app and navigate to the "Create Notification" screen. Fill in the following details:
[List]
Title: "Scheduled Reminder"
Body: "This notification was scheduled for a future time"
Channel: "Default"
Type: "Scheduled"
Date: Select a date in the future
Time: Select a time a few minutes from now
Level: "Normal"

[T] Tap the "Create" button. The notification should be scheduled and appear at the specified time.

//TODO [Image]

[T] To verify that the notification is scheduled, you can check the debug output or navigate to the home screen, which should show the scheduled notification in the list.
[H3] Testing Time Zone Handling
[T] To test timezone handling, you can change your device's time zone and verify that the notification still appears at the correct local time.

[T] On Android:
[List]
Go to Settings > System > Date & time
Turn off "Automatic time zone"
Select a different time zone
Schedule a notification and verify it appears at the correct time

[T] On iOS:
[List]
Go to Settings > General > Date & Time
Turn off "Set Automatically"
Select a different time zone
Schedule a notification and verify it appears at the correct time

[T] Remember to set your device's time zone back to automatic when you're done testing.
[H3] Testing Weekly Notifications
[T] To test weekly notifications:
[List]
Navigate to the "Create Notification" screen
Fill in the basic details (title, body, etc.)
Select "Periodic" as the notification type
Select "Weekly" as the periodic subtype
Select a day of the week
Select a time
Tap the "Create" button

[T] The notification should be scheduled to repeat on the selected day of the week at the selected time. You can verify this by checking the debug output or waiting for the notification to appear.

[T] Up Next: Let's tackle a code challenge to deepen your understanding of scheduled notifications.





Chapter Title 
Code Challenge: Retrieving Pending Notifications

[H2] Code Challenge: Retrieving Pending Notifications
[T] In this chapter, we'll tackle a code challenge to implement a method to retrieve pending notifications, which will deepen your understanding of scheduled notifications.
[H3] The Challenge
[T] Implement a method to get all pending notifications and display them in a list. You'll need to:
[List]
Use the getPendingNotifications method in the NotificationManager class
Create a UI to display the pending notifications
Add a refresh button to update the list
[H3] Solution: Implementing Pending Notifications Retrieval
[T] First, let's examine the getPendingNotifications method in the NotificationManager class:

[Code: dart]
/// Gets a list of all pending notification requests.
///
/// Returns a list of [NotificationModel] objects representing pending
/// notifications. For notifications with invalid or missing payloads,
/// creates fallback models with available information.
Future<List<NotificationModel>> getPendingNotifications() async {
  final requests =
      await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  return requests.map((request) {
    // Try to parse the payload into a NotificationModel
    if (request.payload != null) {
      try {
        return NotificationModel.fromPayload(request.payload!);
      } catch (e) {
        debugPrint('Failed to parse notification payload: $e');
      }
    }
    
    // If parsing fails, create a fallback model with available information
    return NotificationModel(
      id: request.id,
      title: request.title ?? 'Unknown',
      body: request.body ?? '',
      channelId: NotificationChannelIds.defaultChannel,
      type: NotificationType.scheduled,
      level: NotificationLevel.normal,
    );
  }).toList();
}

[T] This method retrieves all pending notification requests and converts them to NotificationModel objects, which can be used to display information about the notifications.

[T] Now, let's create a simple UI to display the pending notifications:

[Code: dart]
class PendingNotificationsPage extends StatefulWidget {
  const PendingNotificationsPage({Key? key}) : super(key: key);

  @override
  _PendingNotificationsPageState createState() => _PendingNotificationsPageState();
}

class _PendingNotificationsPageState extends State<PendingNotificationsPage> {
  final NotificationManager _manager = NotificationManager();
  List<NotificationModel> _pendingNotifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingNotifications();
  }

  Future<void> _loadPendingNotifications() async {
    setState(() {
      _isLoading = true;
    });

    final notifications = await _manager.getPendingNotifications();
    
    setState(() {
      _pendingNotifications = notifications;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pending Notifications'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPendingNotifications,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _pendingNotifications.isEmpty
              ? const Center(child: Text('No pending notifications'))
              : ListView.builder(
                  itemCount: _pendingNotifications.length,
                  itemBuilder: (context, index) {
                    final notification = _pendingNotifications[index];
                    return ListTile(
                      title: Text(notification.title),
                      subtitle: Text(notification.body),
                      trailing: Text(_getNotificationTypeText(notification)),
                    );
                  },
                ),
    );
  }

  String _getNotificationTypeText(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.instant:
        return 'Instant';
      case NotificationType.scheduled:
        final scheduledTime = notification.scheduledTime;
        if (scheduledTime != null) {
          return 'Scheduled for ${scheduledTime.day}/${scheduledTime.month} at ${scheduledTime.hour}:${scheduledTime.minute.toString().padLeft(2, '0')}';
        }
        return 'Scheduled';
      case NotificationType.periodic:
        return 'Periodic';
    }
  }
}

[T] This page displays a list of pending notifications and provides a refresh button to update the list. It uses the getPendingNotifications method to retrieve the notifications and displays them in a ListView.

[T] Up Next: Let's summarize what we've learned in this section and look ahead to the next section.





Chapter Title 
Section Summary

[H2] Section Summary
[T] Let's summarize what we have covered in this section: 
[List] 
We explored the importance of time zone handling in scheduled notifications
We implemented the scheduleNotification method to schedule notifications for future delivery
We enhanced the NotificationBuilder with methods for scheduling notifications
We connected the UI date and time pickers to our notification system
We implemented weekly recurring notifications using a combination of methods
We tackled a code challenge to implement pending notifications retrieval

[T] Key concepts to remember:
[List]
Always use TZDateTime for scheduling notifications to ensure proper time zone handling
Consider how notifications should behave when users change time zones
Use the matchDateTimeComponents parameter to create recurring notifications
Test your notifications with different time zones to ensure they behave correctly

[T] Code we implemented:
[List]
NotificationManager.scheduleNotification(): Schedules a notification for a future time
NotificationManager.cancelNotification(): Cancels a notification
NotificationManager.getPendingNotifications(): Gets all pending notifications
NotificationBuilder.scheduleFor(): Configures a notification for a specific time
NotificationBuilder.setRepeatInterval(): Configures a notification to repeat at a specific interval
NotificationBuilder.atTimeOfDay(): Sets a specific time of day for recurring notifications
NotificationBuilder.onDayOfWeek(): Sets the day of week for weekly recurring notifications

[T] Up Next, we'll dive into implementing more advanced notification features, including notification actions, full-screen notifications, and notification groups. You'll learn how to create interactive notifications that allow users to respond directly from the notification.




