[H1] Handling Notification Clicks and Deep Linking

[H2] Overview

[T] In this section, we'll implement notification click handling and deep linking. These features allow users to navigate to specific screens in your app by tapping on notifications, providing a seamless user experience and making notifications more interactive and useful.

[T] By the end of this section, you'll be able to handle notification taps and deep links, allowing users to navigate directly to relevant content from notifications.

[H3] Learning Objectives

[List]
Understand deep links and their role in notifications
Implement notification tap handling to navigate to specific screens
Set up deep links using the app_links package
Test notification taps and deep link handling

[H2] Understanding Deep Links

[H3] What are Deep Links?

[T] Deep links are URLs that navigate to specific content within your app. In the context of notifications, deep links allow users to tap a notification and be taken directly to the relevant screen, rather than just opening the app to the home screen.

[T] In our app, we'll use the format `timora:/notification-details?id=123` to navigate to a specific notification's details screen.

[H2] Implementing Deep Link Handling

[T] Our app already has a DeepLinkHandler class that handles deep links. Let's examine how it works and implement the notification deep link handling.

[T] Open **lib/core/util/deeplink/deeplink_handler.dart** and examine the class:

[Code: dart]
/// Handles deep link navigation within the app
///
/// This handler supports two types of deeplinks with a centralized processing approach:
/// 1. External deeplinks - from browsers, other apps, etc. (using app_links package)
/// 2. Notification deeplinks - from tapping on notifications
class DeepLinkHandler {
  // Private constructor for singleton pattern
  DeepLinkHandler._();

  // Singleton instance
  static final DeepLinkHandler instance = DeepLinkHandler._();

  // App links instance for handling external deep links
  final AppLinks _appLinks = AppLinks();

  /// Initialize deep link handling - should be called on app startup
  void init() {
    _handleInitialLink();
    _listenForLinks();
  }

  /// Sets up a listener for incoming external deep links while the app is running
  void _listenForLinks() {
    _appLinks.uriLinkStream.listen(
      (Uri? uri) {
        if (uri != null) {
          _handleDeepLink(uri);
        }
      },
      onError: (err) {
        debugPrint('Error handling external deep link: $err');
      },
    );
  }

  /// Handle initial deep link when app is launched from an external link
  Future<void> _handleInitialLink() async {
    try {
      final Uri? initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        debugPrint('App launched from deeplink: $initialUri');
        _handleDeepLink(initialUri);
      }
    } catch (e) {
      debugPrint('Error handling initial deep link: $e');
    }
  }
}

[T] Now, let's implement the methods to handle notification deep links:

[Code: dart]
/// Handle a deeplink from a notification payload
///
/// This is a public method that can be called directly when a notification is tapped.
/// It takes a deeplink URI string from the notification payload and processes it.
void handleNotificationDeeplink(String? deeplinkUri) {
  if (deeplinkUri == null) return;

  try {
    final uri = Uri.parse(deeplinkUri);
    debugPrint('Processing notification deeplink: $uri');
    // Use the shared processing logic
    processDeepLink(uri);
  } catch (e) {
    debugPrint('Error parsing notification deeplink URI: $e');
    // Navigate to home as fallback
    AppRouter.navigateTo('/');
  }
}

/// Process the deep link from external sources
void _handleDeepLink(Uri uri) {
  debugPrint('Processing external deeplink: $uri');
  // Use the shared processing logic
  processDeepLink(uri);
}

/// Shared processing logic for all deeplinks
void processDeepLink(Uri uri) {
  try {
    final DeepLinkData linkData = _parseDeepLink(uri);
    debugPrint(
      'Navigating to: ${linkData.path} with arguments: ${linkData.arguments}',
    );

    // Use AppRouter's navigation method
    AppRouter.navigateTo(linkData.path, arguments: linkData.arguments);
  } catch (e) {
    debugPrint('Error processing deeplink: $e');
    // Navigate to home as fallback
    AppRouter.navigateTo('/');
  }
}

/// Parse a deep link URI into path and arguments
DeepLinkData _parseDeepLink(Uri uri) {
  // Extract the path from the URI
  String path = uri.path;
  if (path.isEmpty) {
    path = '/';
  }
  
  // Default arguments is null
  dynamic arguments;
  
  // Handle notification details deeplink
  if (path == AppRoutes.notificationDetails.value) {
    final notificationId = NotificationDeepLinkUtil.extractNotificationId(uri);
    if (notificationId != null) {
      // Replace the arguments with just the ID as an integer
      arguments = notificationId;
    }
  }
  
  return DeepLinkData(path: path, arguments: arguments);
}

[H3] Creating Deep Links for Notifications

[T] Now, let's examine the NotificationDeepLinkUtil class, which generates deep links for notifications:

[Code: dart]
/// Utility class for generating and parsing notification deeplinks
class NotificationDeepLinkUtil {
  // Private constructor for utility class
  NotificationDeepLinkUtil._();

  /// The scheme used for deeplinks in the app
  static const String scheme = 'timora';

  /// Generates a deeplink for a notification details page
  static String generateNotificationDetailsDeepLink(int notificationId) {
    return '$scheme:${AppRoutes.notificationDetails.value}?id=$notificationId';
  }

  /// Extracts a notification ID from a deeplink URI
  static int? extractNotificationId(Uri uri) {
    if (uri.path == AppRoutes.notificationDetails.value) {
      final idParam = uri.queryParameters['id'];
      if (idParam != null) {
        return int.tryParse(idParam);
      }
    }
    return null;
  }
}

[T] This class provides methods to generate deep links for notification details pages and extract notification IDs from deep link URIs.

[H2] Handling Notification Taps

[T] When a user taps a notification, we want to navigate to the appropriate screen based on the deep link in the notification payload. Let's implement the notification tap handler in the NotificationManager class:

[Code: dart]
/// Callback handler for notification responses.
void _onNotificationResponse(NotificationResponse response) {
  debugPrint('Notification tapped: ${response.payload}');
  
  // If there's a payload, try to parse it and handle the notification
  if (response.payload != null) {
    try {
      final notificationModel = NotificationModel.fromPayload(response.payload!);
      
      // If the notification has a deeplink, use it
      if (notificationModel.deepLink != null) {
        debugPrint(
          'Notification tapped with deeplink: ${notificationModel.deepLink}',
        );
        // Use the DeepLinkHandler to process the deeplink
        DeepLinkHandler.instance.handleNotificationDeeplink(
          notificationModel.deepLink,
        );
      } else {
        // Fallback to direct navigation if no deeplink is provided
        debugPrint(
          'Notification tapped without deeplink, using ID: ${notificationModel.id}',
        );
        AppRouter.navigateTo(
          AppRoutes.notificationDetails.value,
          arguments: notificationModel.id,
        );
      }
    } catch (e) {
      debugPrint('Error processing notification payload: $e');
    }
  }
}

[T] This method extracts the deep link from the notification payload and passes it to the DeepLinkHandler, which will navigate to the appropriate screen.

[H3] Adding Deep Links to Notifications

[T] To add a deep link to a notification, we need to modify the NotificationBuilder class:

[Code: dart]
/// Configures the notification with a custom deep link.
///
/// Sets a custom URI that will be used for navigation when the notification is tapped.
///
/// [deepLink] The deep link URI string
NotificationBuilder withDeepLink(String deepLink) {
  _model = _model.copyWith(deepLink: deepLink);
  return this;
}

[T] We also need to ensure that all notifications have a deep link by default. Let's modify the showInstantNotification method in the NotificationManager class:

[Code: dart]
/// Shows an instant notification immediately.
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show notification: prerequisites not met');
    return;
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  final details = await getNotificationDetailsConfig(
    channelId: notificationWithDeepLink.channelId,
    level: notificationWithDeepLink.level,
    isFullScreen: notificationWithDeepLink.isFullScreen,
    imageAttachment: notificationWithDeepLink.imageAttachment,
    hasActions: notificationWithDeepLink.hasActions,
    customSound: notificationWithDeepLink.customSound,
  );

  await _flutterLocalNotificationsPlugin.show(
    notificationWithDeepLink.id,
    notificationWithDeepLink.title,
    notificationWithDeepLink.body,
    details,
    payload: notificationWithDeepLink.toPayload(),
  );
}

[T] This ensures that all notifications have a deep link, even if one isn't explicitly provided.

[H2] Platform-Specific Configuration for Deep Links

[T] To make deep links work properly, we need to configure them in the platform-specific project files.

[H3] Android Configuration

[T] Open **android/app/src/main/AndroidManifest.xml** and add the following inside the `<activity>` tag:

[Code: xml]
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="timora" />
</intent-filter>

[T] This tells Android that your app can handle URLs with the `timora:` scheme.

[H3] iOS Configuration

[T] Open **ios/Runner/Info.plist** and add the following:

[Code: xml]
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>com.droidcon.timora</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>timora</string>
        </array>
    </dict>
</array>

[T] This tells iOS that your app can handle URLs with the `timora:` scheme.

[H2] Testing Notification Interactions

[T] Now that we've implemented notification interaction handling and deep links, let's test them by creating notifications and tapping on them.

[H3] Testing Basic Notification Taps

[T] Run the app and create a simple notification:

[List]
**Title**: "Tap Test"
**Body**: "Tap this notification to open the details screen"
**Channel**: "Default"
**Type**: "Instant"
**Level**: "Normal"

[T] Tap the "Create" button. The notification should appear. Tap on the notification, and you should be taken to the notification details screen.

[H3] Testing Custom Deep Links

[T] Let's create a notification with a custom deep link:

[Code: dart]
final notification = NotificationBuilder.create(
  NotificationManager(),
  id: 123,
  title: 'Custom Deep Link',
  body: 'Tap to open the create notification screen',
  channelId: NotificationChannelIds.defaultChannel,
)
.withDeepLink('timora:/create-notification')
.build();

await NotificationManager().showInstantNotification(model: notification);

[T] This notification, when tapped, should take you to the create notification screen instead of the details screen.

[H2] Summary

[T] In this section, we've implemented notification interaction handling and deep links:

[List]
Understood the structure and purpose of deep links in notifications
Implemented the DeepLinkHandler to process deep links and navigate to appropriate screens
Added deep links to notifications to enable direct navigation
Configured platform-specific settings for deep links
Tested notification taps and deep link handling

[T] With these features, our notification system now provides a seamless user experience, allowing users to navigate directly to relevant content by tapping on notifications.

[H3] Code to Implement

[T] Here's a summary of the code we've implemented in this section:

[List]
**DeepLinkHandler.handleNotificationDeeplink()**: Processes deep links from notification payloads
**DeepLinkHandler.processDeepLink()**: Shared logic for processing all deep links
**DeepLinkHandler._parseDeepLink()**: Parses a deep link URI into path and arguments
**NotificationDeepLinkUtil.generateNotificationDetailsDeepLink()**: Generates deep links for notification details
**NotificationDeepLinkUtil.extractNotificationId()**: Extracts notification IDs from deep links
**NotificationManager._onNotificationResponse()**: Handles notification taps
**NotificationBuilder.withDeepLink()**: Configures a notification with a custom deep link
