Section 7 eBook
Debugging and Troubleshooting Notifications

<!-- Chapter 1: Section Overview -->
[H2] Section Overview

[T] In this section, you'll explore:
[List]
Identifying and understanding common notification issues across Android and iOS platforms
Applying debugging techniques to diagnose and resolve notification failures effectively
Managing permission-related challenges on Android 13+ and iOS with user-friendly solutions
Mitigating device-specific optimizations (e.g., battery restrictions) impacting notification delivery
Implementing a notification troubleshooter to empower users

[T] Let's begin!


<!-- Chapter 2: Understanding Common Notification Problems -->
[H2] Understanding Common Notification Problems

[T] In this chapter, we'll explore the most common reasons notifications fail to appear on users' devices. Just like a detective investigating a mystery, we need to understand the usual suspects before we can solve the case.

[H3] Permission Issues
[T] One of the most common reasons notifications fail is missing permissions. Let's examine how to handle permissions properly on both platforms.

[H4] Android 13+ POST_NOTIFICATIONS Permission
[T] Starting with Android 13 (API level 33), apps must request the POST_NOTIFICATIONS runtime permission to show notifications. Without this permission, your notifications will silently fail.

[Code: dart]
/// Checks if notification permission is granted on Android 13+
Future<bool> _checkNotificationPermission() async {
  if (Platform.isAndroid) {
    final androidInfo = await DeviceInfoPlugin().androidInfo;
    if (androidInfo.version.sdkInt >= 33) {
      final status = await FlutterLocalNotificationsPlugin()
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestPermission() ?? false;
      return status;
    }
  }
  return true; // Permission not needed for older Android versions
}

/// Requests notification permission with explanation if needed
Future<bool> _requestNotificationPermission() async {
  if (Platform.isAndroid) {
    final androidInfo = await DeviceInfoPlugin().androidInfo;
    if (androidInfo.version.sdkInt >= 33) {
      final status = await _checkNotificationPermission();
      if (!status) {
        // Show explanation dialog before requesting permission
        final shouldRequest = await requestPermissionsWithExplanation(
          'Notification Permission',
          'This app needs notification permission to alert you about important events and reminders.',
        );

        if (shouldRequest) {
          return await FlutterLocalNotificationsPlugin()
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.requestPermission() ?? false;
        }
        return false;
      }
      return status;
    }
  }
  return true; // Permission not needed for older Android versions
}
[/Code]

[T] The permission dialog should be user-friendly and explain why the permission is needed:

[Code: dart]
/// Shows a dialog explaining why a permission is needed
Future<bool> requestPermissionsWithExplanation(
  String title,
  String message,
) async {
  final result = await showDialog<bool>(
    context: navigatorKey.currentContext!,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('Not Now'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, true),
          child: const Text('Continue'),
        ),
      ],
    ),
  );

  return result ?? false;
}
[/Code]

[H4] iOS Notification Permissions
[T] On iOS, notification permissions are requested when initializing the plugin, but users can later disable them in Settings. We need to check and handle this scenario:

[Code: dart]
/// Checks if notification permission is granted on iOS
Future<bool> _checkIOSNotificationPermission() async {
  if (Platform.isIOS) {
    final settings = await FlutterLocalNotificationsPlugin()
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.getNotificationAppLaunchDetails();

    return settings?.didNotificationLaunchApp ?? false;
  }
  return true;
}

/// Opens app settings so user can enable notifications
Future<void> _openAppSettings() async {
  await openAppSettings();
}
[/Code]

[H3] Notification Delivery Issues
[T] Even with permissions granted, notifications might still fail to appear due to device-specific optimizations.

[H4] Battery Optimization on Android
[T] Many Android manufacturers implement aggressive battery optimization that can prevent notifications from being delivered. We need to guide users to disable these optimizations:

[Code: dart]
/// Checks if battery optimization is enabled and disables it if possible
Future<void> _disableBatteryOptimization() async {
  if (Platform.isAndroid) {
    final intent = AndroidIntent(
      action: 'android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS',
      data: 'package:${await PackageInfo.fromPlatform().then((info) => info.packageName)}',
    );
    await intent.launch();
  }
}
[/Code]

[T] For scheduled notifications, use the exact scheduling mode to improve reliability:

[Code: dart]
// When scheduling notifications
androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
[/Code]

[T] For users experiencing issues, provide a link to dontkillmyapp.com, which offers manufacturer-specific instructions:

[Code: dart]
void _showBatteryOptimizationHelp() {
  launchUrl(Uri.parse('https://dontkillmyapp.com'));
}
[/Code]

[H3] Time Zone Issues
[T] Scheduled notifications can fail when users change time zones. We need to listen for time zone changes and reschedule notifications accordingly:

[Code: dart]
/// Initializes time zones for proper scheduling
Future<void> _initializeTimeZones() async {
  try {
    await initializeTimeZones();
    final localTimeZone = await FlutterTimezone.getLocalTimezone();
    setLocalLocation(getLocation(localTimeZone));
  } catch (e) {
    // Fallback to UTC if time zone initialization fails
    setLocalLocation(getLocation('UTC'));
    debugPrint('Failed to initialize time zones: $e');
  }
}

/// Listens for time zone changes and reschedules notifications
void _listenForTimeZoneChanges() {
  // This is a simplified example. In a real app, you would use a
  // platform-specific method to detect time zone changes.
  Stream.periodic(const Duration(hours: 1)).listen((_) async {
    final currentTimeZone = await FlutterTimezone.getLocalTimezone();
    final storedTimeZone = await _getStoredTimeZone();

    if (currentTimeZone != storedTimeZone) {
      await _rescheduleAllNotifications();
      await _storeTimeZone(currentTimeZone);
    }
  });
}
[/Code]


<!-- Chapter 3: Debugging Techniques -->
[H2] Debugging Techniques

[T] In this chapter, we'll explore effective techniques for diagnosing notification issues. Like a doctor examining a patient, we need the right diagnostic tools to identify the problem.

[H3] Implementing Robust Error Handling
[T] Wrap notification methods in try-catch blocks to prevent crashes and capture errors:

[Code: dart]
/// Shows an instant notification with error handling
Future<void> showInstantNotification({
  required NotificationModel model,
}) async {
  try {
    final canShow = await _checkNotificationPrerequisites();
    if (!canShow) {
      debugPrint('Cannot show notification: prerequisites not met');
      return;
    }

    // Add deeplink to the notification if not already set
    final notificationWithDeepLink =
        model.deepLink == null
            ? model.copyWith(
              deepLink:
                  NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                    model.id,
                  ),
            )
            : model;

    // Build notification details
    final details = await _buildNotificationDetails(notificationWithDeepLink);

    // Show the notification
    await flutterLocalNotificationsPlugin.show(
      notificationWithDeepLink.id,
      notificationWithDeepLink.title,
      notificationWithDeepLink.body,
      details,
      payload: notificationWithDeepLink.payload,
    );

    _logNotificationEvent(
      'Showed instant notification, id: ${notificationWithDeepLink.id}',
    );
  } catch (e, stackTrace) {
    _logNotificationEvent(
      'Failed to show notification, error: $e\n$stackTrace',
    );
  }
}
[/Code]

[H3] Implementing Comprehensive Logging
[T] Add detailed logging to track notification events:

[Code: dart]
/// Logs notification-related events
void _logNotificationEvent(String message) {
  debugPrint('NotificationManager: $message');
}
[/Code]

[H3] Filtering Logs
[T] Learn how to filter logs to focus on notification-related issues:

[H4] iOS Log Filtering (Xcode Console)
[T] To filter logs in Xcode:
[List]
1. Connect your iOS device to your Mac
2. Open Xcode and navigate to Window > Devices and Simulators
3. Select your device and click on the "Open Console" button
4. In the search field, enter "NotificationManager" to filter logs

[H4] Android Log Filtering (ADB Logcat)
[T] To filter logs using ADB:
[List]
1. Connect your Android device to your computer
2. Open a terminal and run: `adb logcat -s flutter`
3. To filter specifically for notification logs: `adb logcat | grep NotificationManager`

[H3] Testing on Different Devices and States
[T] Create a systematic testing plan to ensure notifications work across various scenarios:

[List]
Test on different Android manufacturers (especially Huawei, Xiaomi, OnePlus)
Test on iOS 10.0+ for rich notifications
Test on Android 8.0+ for notification channels
Test with the app in different states (foreground, background, terminated)
Test scheduled notifications across time zone changes

[T] Use debug logs to identify specific issues:

[Code: dart]
// Before showing a notification
debugPrint('Attempting to show notification with ID: ${model.id}');

// After showing a notification
debugPrint('Successfully showed notification with ID: ${model.id}');

// When a prerequisite check fails
debugPrint('Cannot show notification: prerequisites not met');
[/Code]


<!-- Chapter 4: Error Handling Strategies -->
[H2] Error Handling Strategies

[T] In this chapter, we'll explore strategies for gracefully handling notification errors. Like a safety net for a trapeze artist, good error handling prevents catastrophic failures.

[H3] Graceful Failure Handling
[T] Enhance the notification methods to handle failures gracefully:

[Code: dart]
/// Shows a notification with permission handling
Future<void> showNotificationWithPermissionHandling({
  required NotificationModel model,
}) async {
  try {
    final hasPermission = await _checkNotificationPermission();
    if (!hasPermission) {
      final granted = await _requestNotificationPermission();
      if (!granted) {
        // Show a snackbar with a link to settings
        final scaffoldMessenger = ScaffoldMessenger.of(navigatorKey.currentContext!);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: const Text('Notifications are disabled. Enable them in settings to receive alerts.'),
            action: SnackBarAction(
              label: 'Settings',
              onPressed: _openAppSettings,
            ),
            duration: const Duration(seconds: 5),
          ),
        );
        return;
      }
    }

    // Proceed with showing the notification
    await showInstantNotification(model: model);
  } catch (e) {
    debugPrint('Error showing notification with permission handling: $e');
  }
}
[/Code]

[H3] Handling Device-Specific Issues
[T] Different manufacturers require different approaches. Create device-specific solutions:

[Code: dart]
/// Gets battery optimization instructions based on device manufacturer
Future<String> _getBatteryOptimizationInstructions() async {
  final deviceInfo = await DeviceInfoPlugin().androidInfo;
  final manufacturer = deviceInfo.manufacturer.toLowerCase();

  if (manufacturer.contains('xiaomi') || manufacturer.contains('redmi')) {
    return '''
    To ensure notifications work properly:
    1. Go to Settings > Apps > Manage Apps > Your App
    2. Enable "Autostart"
    3. Go to Battery & Performance > App Battery Saver > Your App > No restrictions
    ''';
  } else if (manufacturer.contains('huawei')) {
    return '''
    To ensure notifications work properly:
    1. Go to Settings > Apps > Your App
    2. Enable "Auto-launch"
    3. Go to Battery > Launch > Your App > Manage manually > Allow
    ''';
  } else if (manufacturer.contains('oneplus')) {
    return '''
    To ensure notifications work properly:
    1. Go to Settings > Apps > Your App
    2. Enable "Auto-launch"
    3. Go to Battery > Battery optimization > Your App > Don't optimize
    ''';
  }

  return 'To ensure notifications work properly, disable battery optimization for this app in your device settings.';
}

/// Shows device-specific battery optimization instructions
Future<void> _showBatteryOptimizationInstructions() async {
  final instructions = await _getBatteryOptimizationInstructions();

  showDialog(
    context: navigatorKey.currentContext!,
    builder: (context) => AlertDialog(
      title: const Text('Improve Notification Reliability'),
      content: Text(instructions),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Later'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _disableBatteryOptimization();
          },
          child: const Text('Go to Settings'),
        ),
      ],
    ),
  );
}
[/Code]

[H3] iOS Background Refresh Issues
[T] On iOS, background refresh is crucial for scheduled notifications. Check and handle this:

[Code: dart]
/// Checks if background refresh is enabled on iOS
Future<bool> _isBackgroundRefreshEnabled() async {
  if (Platform.isIOS) {
    final status = await BackgroundFetch.status;
    return status == BackgroundFetch.STATUS_AVAILABLE;
  }
  return true;
}

/// Shows instructions for enabling background refresh on iOS
Future<void> _showBackgroundRefreshInstructions() async {
  showDialog(
    context: navigatorKey.currentContext!,
    builder: (context) => AlertDialog(
      title: const Text('Enable Background Refresh'),
      content: const Text(
        'To ensure notifications work properly, please enable Background App Refresh:\n\n'
        '1. Go to Settings > General > Background App Refresh\n'
        '2. Enable Background App Refresh\n'
        '3. Find this app in the list and enable it',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Later'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _openAppSettings();
          },
          child: const Text('Go to Settings'),
        ),
      ],
    ),
  );
}
[/Code]


<!-- Chapter 5: Implementing a Notification Troubleshooter -->
[H2] Implementing a Notification Troubleshooter

[T] In this chapter, we'll create a simple troubleshooter utility to help diagnose and fix notification issues. Like a self-service diagnostic tool, this empowers users to solve their own problems.

[H3] Creating the NotificationTroubleshooter Class
[T] Let's create a utility class to check for common notification issues:

[Code: dart]
/// Represents a notification issue with a description and action
class NotificationIssue {
  final String title;
  final String description;
  final String actionLabel;
  final Function() action;

  const NotificationIssue({
    required this.title,
    required this.description,
    required this.actionLabel,
    required this.action,
  });
}

/// Utility class to diagnose notification issues
class NotificationTroubleshooter {
  final NotificationManager _notificationManager;

  NotificationTroubleshooter(this._notificationManager);

  /// Checks for common notification issues
  Future<List<NotificationIssue>> checkForIssues() async {
    final issues = <NotificationIssue>[];

    // Check for permission issues
    final hasPermission = await _notificationManager._checkNotificationPermission();
    if (!hasPermission) {
      issues.add(
        NotificationIssue(
          title: 'Notification Permission Denied',
          description: 'This app needs notification permission to alert you about important events.',
          actionLabel: 'Grant Permission',
          action: () => _notificationManager._requestNotificationPermission(),
        ),
      );
    }

    // Check for battery optimization (Android only)
    if (Platform.isAndroid) {
      final isBatteryOptimized = await _isBatteryOptimized();
      if (isBatteryOptimized) {
        issues.add(
          NotificationIssue(
            title: 'Battery Optimization Enabled',
            description: 'Battery optimization may prevent notifications from appearing.',
            actionLabel: 'Disable Optimization',
            action: () => _notificationManager._disableBatteryOptimization(),
          ),
        );
      }
    }

    // Check for background refresh (iOS only)
    if (Platform.isIOS) {
      final isBackgroundRefreshEnabled = await _notificationManager._isBackgroundRefreshEnabled();
      if (!isBackgroundRefreshEnabled) {
        issues.add(
          NotificationIssue(
            title: 'Background Refresh Disabled',
            description: 'Background refresh is needed for scheduled notifications.',
            actionLabel: 'Enable Background Refresh',
            action: () => _notificationManager._showBackgroundRefreshInstructions(),
          ),
        );
      }
    }

    // Check if there are any pending notifications
    final pendingNotifications = await _notificationManager.getPendingNotifications();
    if (pendingNotifications.isEmpty) {
      issues.add(
        NotificationIssue(
          title: 'No Pending Notifications',
          description: 'You don\'t have any scheduled notifications. Create one to test if notifications are working.',
          actionLabel: 'Create Notification',
          action: () => Navigator.pushNamed(navigatorKey.currentContext!, '/create-notification'),
        ),
      );
    }

    return issues;
  }

  /// Checks if battery optimization is enabled
  Future<bool> _isBatteryOptimized() async {
    if (Platform.isAndroid) {
      // This is a simplified check. In a real app, you would use
      // a platform-specific method to check battery optimization status.
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      final manufacturer = deviceInfo.manufacturer.toLowerCase();

      // For demonstration purposes, we'll assume certain manufacturers
      // have battery optimization enabled by default
      return manufacturer.contains('xiaomi') ||
             manufacturer.contains('huawei') ||
             manufacturer.contains('oneplus');
    }
    return false;
  }
}
[/Code]

[T] You can use this troubleshooter in your app to diagnose notification issues and guide users to fix them. The implementation of the UI is left as an exercise, but you would typically display the issues in a list and provide buttons for the user to take the recommended actions.

[Image]
<Add placeholder for notification troubleshooter in action>




<!-- Chapter 6: Section Summary -->
[H2] Section Summary

[T] Let's summarize what we have covered in this section:

[List]
We identified common notification issues across Android and iOS platforms
We implemented robust permission handling for Android 13+ and iOS
We addressed battery optimization issues on Android devices
We created strategies for handling time zone changes
We implemented comprehensive logging and debugging techniques
We developed error handling strategies for graceful failure
We created device-specific solutions for manufacturers like Xiaomi, Huawei, and OnePlus
We built a notification troubleshooter to empower users

[T] By implementing these techniques, you've significantly improved the reliability of your notification system and provided users with the tools they need to resolve issues on their own.

[H3] Key Takeaways
[List]
Always check and request permissions appropriately for each platform
Implement robust error handling to prevent crashes and capture diagnostic information
Address device-specific optimizations that can impact notification delivery
Provide users with clear guidance and tools to resolve notification issues
Test your notification system thoroughly across different devices and scenarios

[T] Up Next, we'll explore how to analyze notification engagement and optimize your notification strategy based on user behavior.


<!-- Chapter 7: Quiz -->
[H2] Quiz

[T] Test your understanding of notification debugging and troubleshooting with these questions:

[H3] Question 1
[T] What is a common reason notifications fail to appear on Android 13+ devices?
[List]
A. Missing POST_NOTIFICATIONS permission
B. Incorrect notification channel
C. Outdated Flutter version
D. Missing notification icon

[H3] Question 2
[T] A scheduled notification fires hours late on a Huawei device. What's the most likely cause?
[List]
A. Time zone misconfiguration
B. Battery optimization
C. Network connectivity issues
D. Incorrect scheduling code

[H3] Question 3
[T] After a user denies permissions on iOS, notifications don't appear, and no feedback is shown. What's missing?
[List]
A. Proper error handling
B. A mechanism to guide users to app settings
C. Background refresh capability
D. Both A and B

[T] Answers: 1-A, 2-B, 3-D
