Section 3 eBook
Organizing Notifications with Custom Channels

Chapter Title 
Section Overview 

[T] In this section, you'll explore: 
[List] 
What notification channels are and why they're important
How to create and configure notification channels with different importance levels
How to implement channel groups to organize related channels
How to provide a consistent experience across both Android and iOS platforms
How to let users customize their notification preferences

[T] Once you explore the fundamentals, you'll get a Code Challenge to practice, and we'll discuss its solution at the end of this codelab/section. 

[T] Let's begin! 


Next Chapter Title 
Understanding Notification Channels

[T] In this chapter, we'll explore what notification channels are, why they're important, and how they help organize notifications in Android.

[H2] What Are Notification Channels?
[T] Notification channels are a feature introduced in Android 8.0 (API level 26) that help categorize notifications and allow users to control them at a granular level. Each channel represents a category of notifications with its own importance level, sound, vibration pattern, and other settings.

[T] For example, a messaging app might have separate channels for:
[List]
Direct messages
Group messages
Friend requests
System notifications

[T] Users can then customize how each type of notification behaves, such as:
[List]
Allowing some notifications to make sounds while silencing others
Setting different importance levels for different types of notifications
Blocking certain types of notifications entirely while allowing others

[H2] Why Are Notification Channels Important?
[T] Notification channels provide several benefits:

[List]
**User Control**: Users can customize notification behavior at a granular level
**Reduced Notification Fatigue**: Users can silence less important notifications without disabling all notifications
**Better Organization**: Developers can categorize notifications logically
**Compliance**: Required for apps targeting Android 8.0 (API level 26) or higher

[T] While notification channels are an Android-specific feature, we'll implement them in a way that provides a consistent experience across both Android and iOS.

[H2] Planning Our Notification Channels
[T] For our Timora app, we'll create several notification channels:

[List]
**Default**: For general notifications
**Personal**: For personal reminders and events
**Work**: For work-related reminders and meetings
**Health**: For health and fitness reminders
**System**: For system-related notifications

[T] Each channel will have different default settings for importance, sound, and vibration patterns, providing a rich and customizable notification experience.

[T] Up Next, we'll implement these notification channels in our app, starting with the basic channel structure.


Next Chapter Title 
Implementing Basic Notification Channels

[T] In this chapter, we'll implement basic notification channels in our app, setting up the foundation for our notification organization system.

[H2] Setting Up Channel Constants
[T] First, let's define constants for our notification channels. Open **lib/core/constants/notification_constants.dart** and add the following:

[Code: dart]
/// Notification channel IDs
class NotificationChannelIds {
  static const String defaultChannel = 'default';
  static const String personal = 'personal';
  static const String work = 'work';
  static const String health = 'health';
  static const String system = 'system';
}

/// Notification channel group IDs
class NotificationChannelGroupIds {
  static const String general = 'general';
  static const String reminders = 'reminders';
  static const String system = 'system';
}

[T] These constants define the IDs for our notification channels and channel groups, which we'll use throughout our app.

[H2] Implementing the _setupNotificationChannels Method
[T] Now, let's implement the _setupNotificationChannels method in the NotificationManager class. Open **lib/service/notification-manager/notification_manager.dart** and update the method:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined notification channels with different importance levels
/// and configurations. This is required for Android 8.0 (API level 26) and higher.
Future<void> _setupNotificationChannels() async {
  // Only needed for Android
  if (!Platform.isAndroid) return;

  // Get the Android-specific implementation
  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin == null) {
    debugPrint('Failed to get Android plugin implementation');
    return;
  }

  // Create the default channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.defaultChannel,
      'Default Notifications',
      description: 'General notifications from the app',
      importance: Importance.defaultImportance,
    ),
  );

  // Create the personal channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.personal,
      'Personal Reminders',
      description: 'Notifications for personal reminders and events',
      importance: Importance.high,
    ),
  );

  // Create the work channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.work,
      'Work Reminders',
      description: 'Notifications for work-related reminders and meetings',
      importance: Importance.high,
    ),
  );

  // Create the health channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.health,
      'Health Reminders',
      description: 'Notifications for health and fitness reminders',
      importance: Importance.defaultImportance,
    ),
  );

  // Create the system channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.system,
      'System Notifications',
      description: 'System-related notifications',
      importance: Importance.low,
    ),
  );

  debugPrint('Set up notification channels');
}

[T] This method creates five notification channels with different names, descriptions, and importance levels. Each channel represents a different category of notifications in our app.

[T] Up Next, we'll enhance our notification channels with more advanced configurations and organize them into channel groups.


Next Chapter Title 
Enhancing Notification Channels with Advanced Configurations

[T] In this chapter, we'll enhance our notification channels with more advanced configurations, including custom sounds, vibration patterns, and lights.

[H2] Adding Custom Sounds to Channels
[T] Let's update our notification channels to include custom sounds for certain channels. First, we need to add sound files to our project:

[List]
1. For Android: Add sound files to the **android/app/src/main/res/raw** directory
2. For iOS: Add sound files to the **ios/Runner** directory and include them in the Xcode project

[T] Now, let's update the _setupNotificationChannels method to include custom sounds:

[Code: dart]
// Create the personal channel with custom sound
await androidPlugin.createNotificationChannel(
  const AndroidNotificationChannel(
    NotificationChannelIds.personal,
    'Personal Reminders',
    description: 'Notifications for personal reminders and events',
    importance: Importance.high,
    sound: RawResourceAndroidNotificationSound(NotificationResources.customSoundAndroid),
    enableVibration: true,
    enableLights: true,
    ledColor: Color(0xFF9C27B0), // Purple color
  ),
);

// Create the work channel with custom sound
await androidPlugin.createNotificationChannel(
  const AndroidNotificationChannel(
    NotificationChannelIds.work,
    'Work Reminders',
    description: 'Notifications for work-related reminders and meetings',
    importance: Importance.high,
    sound: RawResourceAndroidNotificationSound(NotificationResources.customSoundAndroid),
    enableVibration: true,
    vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
    enableLights: true,
    ledColor: Color(0xFF2196F3), // Blue color
  ),
);

[T] This code adds custom sounds, vibration patterns, and LED colors to our personal and work channels, making them more distinctive and noticeable.

[H2] Implementing Channel Groups
[T] To further organize our notification channels, let's implement channel groups. Channel groups allow users to manage multiple related channels at once.

[T] Update the _setupNotificationChannels method to create channel groups:

[Code: dart]
/// Sets up notification channels for Android.
///
/// Creates predefined notification channels with different importance levels
/// and configurations. This is required for Android 8.0 (API level 26) and higher.
Future<void> _setupNotificationChannels() async {
  // Only needed for Android
  if (!Platform.isAndroid) return;

  // Get the Android-specific implementation
  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin == null) {
    debugPrint('Failed to get Android plugin implementation');
    return;
  }

  // Create channel groups
  await androidPlugin.createNotificationChannelGroup(
    const AndroidNotificationChannelGroup(
      NotificationChannelGroupIds.general,
      'General',
      description: 'General notifications',
    ),
  );

  await androidPlugin.createNotificationChannelGroup(
    const AndroidNotificationChannelGroup(
      NotificationChannelGroupIds.reminders,
      'Reminders',
      description: 'Reminder notifications',
    ),
  );

  await androidPlugin.createNotificationChannelGroup(
    const AndroidNotificationChannelGroup(
      NotificationChannelGroupIds.system,
      'System',
      description: 'System notifications',
    ),
  );

  // Create the default channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.defaultChannel,
      'Default Notifications',
      description: 'General notifications from the app',
      groupId: NotificationChannelGroupIds.general,
      importance: Importance.defaultImportance,
    ),
  );

  // Create the personal channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.personal,
      'Personal Reminders',
      description: 'Notifications for personal reminders and events',
      groupId: NotificationChannelGroupIds.reminders,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound(NotificationResources.customSoundAndroid),
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF9C27B0), // Purple color
    ),
  );

  // Create the work channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.work,
      'Work Reminders',
      description: 'Notifications for work-related reminders and meetings',
      groupId: NotificationChannelGroupIds.reminders,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound(NotificationResources.customSoundAndroid),
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
      enableLights: true,
      ledColor: Color(0xFF2196F3), // Blue color
    ),
  );

  // Create the health channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.health,
      'Health Reminders',
      description: 'Notifications for health and fitness reminders',
      groupId: NotificationChannelGroupIds.reminders,
      importance: Importance.defaultImportance,
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF4CAF50), // Green color
    ),
  );

  // Create the system channel
  await androidPlugin.createNotificationChannel(
    const AndroidNotificationChannel(
      NotificationChannelIds.system,
      'System Notifications',
      description: 'System-related notifications',
      groupId: NotificationChannelGroupIds.system,
      importance: Importance.low,
    ),
  );

  debugPrint('Set up notification channels and groups');
}

[T] This code creates three channel groups (General, Reminders, and System) and assigns each channel to the appropriate group. This organization makes it easier for users to manage related notifications.

[T] Up Next, we'll implement iOS notification categories to provide a consistent experience across platforms.


Next Chapter Title 
Implementing iOS Notification Categories

[T] In this chapter, we'll implement iOS notification categories to provide a consistent experience across both Android and iOS platforms.

[H2] Understanding iOS Notification Categories
[T] While Android uses notification channels, iOS uses notification categories to organize notifications. iOS notification categories serve a similar purpose but work differently:

[List]
**Android Channels**: Focus on notification appearance and behavior
**iOS Categories**: Focus on notification actions and interaction options

[T] To provide a consistent experience, we'll map our Android channels to iOS categories with similar functionality.

[H2] Implementing iOS Notification Categories
[T] Let's implement iOS notification categories in our _setupNotificationChannels method:

[Code: dart]
/// Sets up notification channels for Android and categories for iOS.
///
/// Creates predefined notification channels with different importance levels
/// and configurations. This is required for Android 8.0 (API level 26) and higher.
/// Also sets up equivalent categories for iOS.
Future<void> _setupNotificationChannels() async {
  // Android-specific setup
  if (Platform.isAndroid) {
    // Android channel setup code (as implemented previously)
    // ...
  }

  // iOS-specific setup
  if (Platform.isIOS) {
    final IOSFlutterLocalNotificationsPlugin? iosPlugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();

    if (iosPlugin == null) {
      debugPrint('Failed to get iOS plugin implementation');
      return;
    }

    // Set up iOS notification categories
    await iosPlugin.setNotificationCategories([
      DarwinNotificationCategory(
        NotificationChannelIds.defaultChannel,
        actions: [],
      ),
      DarwinNotificationCategory(
        NotificationChannelIds.personal,
        actions: [],
      ),
      DarwinNotificationCategory(
        NotificationChannelIds.work,
        actions: [],
      ),
      DarwinNotificationCategory(
        NotificationChannelIds.health,
        actions: [],
      ),
      DarwinNotificationCategory(
        NotificationChannelIds.system,
        actions: [],
      ),
    ]);

    debugPrint('Set up iOS notification categories');
  }
}

[T] This code sets up iOS notification categories that correspond to our Android notification channels. For now, we're not adding any actions to these categories, but we'll add them in a later section when we implement interactive notifications.

[H2] Updating the _buildNotificationDetails Method
[T] Now that we have our channels and categories set up, let's update the _buildNotificationDetails method to use the appropriate channel or category based on the platform:

[Code: dart]
/// Builds platform-specific notification details based on the notification model.
///
/// Configures Android and iOS specific settings according to the notification properties.
Future<NotificationDetails> _buildNotificationDetails(NotificationModel model) async {
  // Configure Android-specific details
  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    channelDescription: '${model.channelId} notifications',
    importance: _getAndroidImportance(model.level),
    priority: _getAndroidPriority(model.level),
    styleInformation: const DefaultStyleInformation(true, true),
    icon: _getNotificationIcon(model.channelId),
    color: _getNotificationColor(model.channelId),
    playSound: model.customSound,
    enableLights: true,
    fullScreenIntent: model.isFullScreen,
    category: AndroidNotificationCategory.reminder,
  );

  // Configure iOS-specific details
  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: model.customSound,
    sound: model.customSound ? NotificationResources.customSoundIOS : null,
    interruptionLevel: _getIOSInterruptionLevel(model.level),
  );

  // Combine the platform-specific details
  return NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

[T] This method now uses helper methods to get the appropriate icon and color based on the notification channel, providing a consistent visual identity for each type of notification.

[T] Up Next, we'll implement helper methods for notification customization based on channels.


Next Chapter Title 
Implementing Channel-Based Notification Customization

[T] In this chapter, we'll implement helper methods to customize notifications based on their channel, providing a consistent visual identity for each type of notification.

[H2] Implementing Helper Methods for Notification Icons
[T] Let's implement a helper method to get the appropriate icon based on the notification channel:

[Code: dart]
/// Gets the appropriate notification icon based on the channel ID.
String _getNotificationIcon(String channelId) {
  switch (channelId) {
    case NotificationChannelIds.work:
      return NotificationResources.workIcon;
    case NotificationChannelIds.personal:
      return NotificationResources.personalIcon;
    case NotificationChannelIds.health:
      return NotificationResources.healthIcon;
    case NotificationChannelIds.system:
      return NotificationResources.systemIcon;
    default:
      return NotificationResources.defaultIcon;
  }
}

[T] This method returns a different icon for each notification channel, making it easier for users to identify the type of notification at a glance.

[H2] Implementing Helper Methods for Notification Colors
[T] Similarly, let's implement a helper method to get the appropriate color based on the notification channel:

[Code: dart]
/// Gets the appropriate notification color based on the channel ID.
Color? _getNotificationColor(String channelId) {
  switch (channelId) {
    case NotificationChannelIds.work:
      return const Color(0xFF2196F3); // Blue
    case NotificationChannelIds.personal:
      return const Color(0xFF9C27B0); // Purple
    case NotificationChannelIds.health:
      return const Color(0xFF4CAF50); // Green
    case NotificationChannelIds.system:
      return const Color(0xFF607D8B); // Blue Grey
    default:
      return null; // Use system default
  }
}

[T] This method returns a different color for each notification channel, providing a visual cue about the type of notification.

[H2] Updating the NotificationResources Class
[T] Let's update the NotificationResources class to include the new icons:

[Code: dart]
/// Notification resources
class NotificationResources {
  // Icons
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String workIcon = '@drawable/ic_work';
  static const String personalIcon = '@drawable/ic_personal';
  static const String healthIcon = '@drawable/ic_health';
  static const String systemIcon = '@drawable/ic_system';
  
  // Sounds
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] These constants define the resources used for different types of notifications, making it easy to maintain a consistent visual identity.

[T] Up Next, we'll test our notification channels by creating notifications for different channels.


Next Chapter Title 
Testing Notification Channels

[T] In this chapter, we'll test our notification channels by creating notifications for different channels and observing how they appear on both Android and iOS devices.

[H2] Creating Notifications for Different Channels
[T] Let's create a method to test our notification channels by showing a notification for each channel:

[Code: dart]
/// Tests notification channels by showing a notification for each channel.
Future<void> testNotificationChannels() async {
  // Test default channel
  await NotificationBuilder.create(
    this,
    id: 1,
    title: 'Default Channel Test',
    body: 'This is a test notification for the default channel',
    channelId: NotificationChannelIds.defaultChannel,
  ).show();

  // Test personal channel
  await NotificationBuilder.create(
    this,
    id: 2,
    title: 'Personal Channel Test',
    body: 'This is a test notification for the personal channel',
    channelId: NotificationChannelIds.personal,
    level: NotificationLevel.high,
  ).setCustomSound(true).show();

  // Test work channel
  await NotificationBuilder.create(
    this,
    id: 3,
    title: 'Work Channel Test',
    body: 'This is a test notification for the work channel',
    channelId: NotificationChannelIds.work,
    level: NotificationLevel.high,
  ).setCustomSound(true).show();

  // Test health channel
  await NotificationBuilder.create(
    this,
    id: 4,
    title: 'Health Channel Test',
    body: 'This is a test notification for the health channel',
    channelId: NotificationChannelIds.health,
  ).show();

  // Test system channel
  await NotificationBuilder.create(
    this,
    id: 5,
    title: 'System Channel Test',
    body: 'This is a test notification for the system channel',
    channelId: NotificationChannelIds.system,
    level: NotificationLevel.low,
  ).show();
}

[T] This method creates and shows a notification for each channel, allowing us to test how they appear and behave.

[H2] Observing Channel Settings in Android Settings
[T] On Android, users can customize notification channels through the system settings. Let's add a method to open the notification channel settings:

[Code: dart]
/// Opens the notification channel settings for this app.
///
/// This is only applicable for Android 8.0 (API level 26) and higher.
Future<void> openNotificationChannelSettings() async {
  if (!Platform.isAndroid) return;

  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin == null) {
    debugPrint('Failed to get Android plugin implementation');
    return;
  }

  await androidPlugin.openNotificationSettings();
}

[T] This method opens the notification settings for our app, allowing users to customize the notification channels.

[H2] Testing on Different Devices
[T] When testing notification channels, it's important to test on both Android and iOS devices to ensure a consistent experience. Here are some things to check:

[List]
**Android**: Verify that notifications appear with the correct icon, color, sound, and vibration pattern for each channel
**iOS**: Verify that notifications appear with the correct sound and interruption level for each category
**Both Platforms**: Verify that notifications are grouped correctly and that tapping them navigates to the appropriate screen

[T] Up Next, we'll tackle a code challenge to test your understanding of notification channels.


Next Chapter Title 
Code Challenge: Creating a Custom Notification Channel

[T] In this chapter, you'll get a chance to practice what you've learned with a code challenge focused on creating a custom notification channel.

[H2] The Challenge
[T] Your challenge is to create a new notification channel for "Social" notifications. You'll need to:

[List]
1. Add a new constant in NotificationChannelIds for the social channel
2. Update the _setupNotificationChannels method to create the social channel
3. Add a new icon and color for social notifications
4. Test the new channel by creating a social notification

[T] Take some time to implement this challenge before looking at the solution.

[H2] Solution
[T] Let's go through the solution to the challenge:

[T] First, add a new constant in NotificationChannelIds:

[Code: dart]
/// Notification channel IDs
class NotificationChannelIds {
  static const String defaultChannel = 'default';
  static const String personal = 'personal';
  static const String work = 'work';
  static const String health = 'health';
  static const String system = 'system';
  static const String social = 'social';
}

[T] Then, update the _setupNotificationChannels method to create the social channel:

[Code: dart]
// Create the social channel
await androidPlugin.createNotificationChannel(
  const AndroidNotificationChannel(
    NotificationChannelIds.social,
    'Social Notifications',
    description: 'Notifications for social interactions and updates',
    groupId: NotificationChannelGroupIds.general,
    importance: Importance.high,
    enableVibration: true,
    enableLights: true,
    ledColor: Color(0xFFE91E63), // Pink color
  ),
);

[T] Add a new icon for social notifications in the NotificationResources class:

[Code: dart]
/// Notification resources
class NotificationResources {
  // Icons
  static const String defaultIcon = '@mipmap/ic_launcher';
  static const String workIcon = '@drawable/ic_work';
  static const String personalIcon = '@drawable/ic_personal';
  static const String healthIcon = '@drawable/ic_health';
  static const String systemIcon = '@drawable/ic_system';
  static const String socialIcon = '@drawable/ic_social';
  
  // Sounds
  static const String customSoundAndroid = 'custom_sound';
  static const String customSoundIOS = 'custom_sound.wav';
}

[T] Update the _getNotificationIcon and _getNotificationColor methods:

[Code: dart]
/// Gets the appropriate notification icon based on the channel ID.
String _getNotificationIcon(String channelId) {
  switch (channelId) {
    case NotificationChannelIds.work:
      return NotificationResources.workIcon;
    case NotificationChannelIds.personal:
      return NotificationResources.personalIcon;
    case NotificationChannelIds.health:
      return NotificationResources.healthIcon;
    case NotificationChannelIds.system:
      return NotificationResources.systemIcon;
    case NotificationChannelIds.social:
      return NotificationResources.socialIcon;
    default:
      return NotificationResources.defaultIcon;
  }
}

/// Gets the appropriate notification color based on the channel ID.
Color? _getNotificationColor(String channelId) {
  switch (channelId) {
    case NotificationChannelIds.work:
      return const Color(0xFF2196F3); // Blue
    case NotificationChannelIds.personal:
      return const Color(0xFF9C27B0); // Purple
    case NotificationChannelIds.health:
      return const Color(0xFF4CAF50); // Green
    case NotificationChannelIds.system:
      return const Color(0xFF607D8B); // Blue Grey
    case NotificationChannelIds.social:
      return const Color(0xFFE91E63); // Pink
    default:
      return null; // Use system default
  }
}

[T] Finally, test the new channel by creating a social notification:

[Code: dart]
// Test social channel
await NotificationBuilder.create(
  this,
  id: 6,
  title: 'Social Channel Test',
  body: 'This is a test notification for the social channel',
  channelId: NotificationChannelIds.social,
  level: NotificationLevel.high,
).show();

[T] This challenge demonstrates how to create and customize a new notification channel, allowing you to expand your app's notification capabilities.

[T] Up Next, we'll summarize what we've learned in this section and preview what's coming in the next section.


Last Chapter Title 
Section Summary 

[T] Let's summarize what we have covered in this section: 
[List] 
We learned about notification channels and their importance in Android
We implemented multiple notification channels with different importance levels
We organized channels into channel groups for better management
We added custom sounds, vibration patterns, and LED colors to our channels
We implemented iOS notification categories for a consistent cross-platform experience
We created helper methods to customize notifications based on their channel
We tested our implementation by creating notifications for different channels

[T] You now have a solid understanding of how to organize notifications using channels and provide a consistent experience across both Android and iOS platforms. Your app can now deliver notifications with different importance levels, sounds, and visual cues, giving users more control over how they receive information.

[T] The notification channels we've implemented provide a foundation for more advanced notification features, which we'll explore in the upcoming sections.

[T] Up Next, we'll dive into scheduling notifications with time zone awareness, where you'll learn how to create notifications that appear at specific times in the future, regardless of the user's location or time zone changes.
