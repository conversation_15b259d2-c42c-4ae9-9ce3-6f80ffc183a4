Section 5 eBook
Implementing Recurring and Periodic Notifications

Chapter Title
Section Overview

[T] In this section, you'll explore:
[List]
What recurring and periodic notifications are and how they differ
How to implement notifications that repeat at regular intervals
How to create daily and weekly recurring notifications
How to handle platform differences between Android and iOS
How to manage and cancel recurring notifications

[T] Once you explore the fundamentals, you'll get hands-on experience implementing recurring notifications in the Timora app.

[T] Recurring notifications are essential for many types of apps:

[List]
**Health apps**: Medication reminders, exercise prompts, or hydration reminders
**Productivity apps**: Daily task reminders, weekly planning notifications, or work break reminders
**Calendar apps**: Meeting reminders, birthday notifications, or anniversary alerts
**Habit-building apps**: Daily meditation reminders, journaling prompts, or habit check-ins

[T] By implementing recurring notifications effectively, you can significantly improve user engagement and retention by providing timely, relevant reminders that help users get the most value from your app.

[T] Let's begin!


Next Chapter Title
Understanding Recurring Notifications

[T] In this chapter, we'll explore what recurring and periodic notifications are, their different types, and how they can be implemented in Flutter.

[H2] Types of Repeating Notifications

[T] There are two main approaches to implementing repeating notifications:

[List]
**Calendar-based (Recurring)**: Notifications that repeat based on calendar patterns (e.g., daily at 9:00 AM, weekly on Mondays)
**Fixed intervals (Periodic)**: Notifications that repeat at fixed time intervals (e.g., every minute, every hour)

[T] The flutter_local_notifications package supports both approaches through different methods:

[List]
**zonedSchedule() with matchDateTimeComponents**: For calendar-based repetitions
**periodicallyShow()**: For fixed interval repetitions

[H2] Repeat Intervals

[T] The RepeatInterval enum in our app defines the supported repetition patterns:

[Code: dart]
enum RepeatInterval {
  everyMinute,
  hourly,
  daily,
  weekly,
}

[T] Each interval corresponds to a different repetition pattern:

[List]
**everyMinute**: Repeats every minute (primarily for testing purposes)
**hourly**: Repeats every hour at the same minute
**daily**: Repeats every day at the same time
**weekly**: Repeats every week on the same day and time

[H2] Platform Differences

[T] It's important to note that Android and iOS handle periodic notifications differently:

[List]
**Android**: Provides direct support for periodic notifications through the periodicallyShow() method
**iOS**: Requires using zonedSchedule() with custom logic to simulate periodic behavior

[T] In our implementation, we'll handle these platform differences transparently, providing a consistent API for both platforms.


Next Chapter Title
Core Implementation of Recurring Notifications

[T] In this chapter, we'll dive deep into the implementation of recurring notifications in our NotificationManager class. We'll explore how to handle both calendar-based and fixed-interval notifications, and how to calculate the next occurrence of a notification.

[H2] The showPeriodicNotification Method

[T] The core of our recurring notification implementation is the showPeriodicNotification() method in the NotificationManager class. This method handles the scheduling of notifications that repeat at regular intervals.

[Code: dart]
/// Displays a periodic notification that repeats at a fixed interval.
///
/// Configures a notification to repeat according to the [model.repeatInterval].
/// If [enabled] is `false`, cancels any existing notification with the same ID.
///
/// Throws an [ArgumentError] if [model.repeatInterval] is null.
Future<void> showPeriodicNotification({
  required NotificationModel model,
  bool enabled = true,
}) async {
  if (!enabled) {
    await cancelNotification(model.id);
    return;
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show periodic notification: prerequisites not met');
    return;
  }

  if (model.repeatInterval == null) {
    throw ArgumentError(
      'repeatInterval must be provided for periodic notifications',
    );
  }

  // Add deeplink to the notification if not already set
  final notificationWithDeepLink =
      model.deepLink == null
          ? model.copyWith(
            deepLink:
                NotificationDeepLinkUtil.generateNotificationDetailsDeepLink(
                  model.id,
                ),
          )
          : model;

  final details = await getNotificationDetailsConfig(
    channelId: notificationWithDeepLink.channelId,
    level: notificationWithDeepLink.level,
    isFullScreen: notificationWithDeepLink.isFullScreen,
    imageAttachment: notificationWithDeepLink.imageAttachment,
    hasActions: notificationWithDeepLink.hasActions,
    customSound: notificationWithDeepLink.customSound,
  );

  // For daily/weekly notifications with a specific time, use zonedSchedule with matchDateTimeComponents
  if (_shouldUseZonedSchedule(notificationWithDeepLink)) {
    await _scheduleRecurringAtTime(notificationWithDeepLink, details);
  } else {
    // For simpler repeating notifications without specific time requirements
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      notificationWithDeepLink.id,
      notificationWithDeepLink.title,
      notificationWithDeepLink.body,
      notificationWithDeepLink.repeatInterval!,
      details,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: notificationWithDeepLink.toPayload(),
    );
  }
}

[T] Let's break down what this method does:

[List]
1. First, it checks if the notification should be enabled. If not, it cancels any existing notification with the same ID.
2. It verifies that the notification prerequisites (initialization and permissions) are met.
3. It validates that a repeat interval is provided.
4. It adds a deeplink to the notification if one isn't already set, which allows users to navigate to the notification details when they tap the notification.
5. It configures the notification details based on the channel, importance level, and other properties.
6. It determines whether to use zonedSchedule (for calendar-based notifications) or periodicallyShow (for fixed-interval notifications).

[H2] Handling Platform Differences

[T] One of the key challenges in implementing recurring notifications is handling the differences between Android and iOS. Our implementation addresses this by using different approaches based on the notification type:

[Code: dart]
/// Determines if zonedSchedule should be used instead of periodicallyShow.
///
/// Returns `true` if the notification is daily or weekly and has a specific
/// time of day set.
bool _shouldUseZonedSchedule(NotificationModel model) {
  return (model.repeatInterval == RepeatInterval.daily ||
          model.repeatInterval == RepeatInterval.weekly) &&
      model.timeOfDay != null;
}

[T] This method determines which scheduling approach to use:

[List]
For calendar-based notifications (daily or weekly with a specific time), we use zonedSchedule with matchDateTimeComponents.
For fixed-interval notifications (everyMinute, hourly), we use periodicallyShow.

[T] This approach provides a consistent API for both platforms while handling their differences internally.

[H2] Implementing Calendar-Based Notifications

[T] For calendar-based notifications (daily and weekly), we use the _scheduleRecurringAtTime method:

[Code: dart]
/// Schedules a recurring notification at a specific time.
///
/// Configures a notification to repeat at a specific time of day based on
/// the model's timeOfDay and optional dayOfWeek properties.
Future<void> _scheduleRecurringAtTime(
  NotificationModel model,
  NotificationDetails details,
) async {
  // Calculate the next occurrence based on the model properties
  tz.TZDateTime nextOccurrence = _calculateNextOccurrence(
    timeOfDay: model.timeOfDay!,
    dayOfWeek:
        model.repeatInterval == RepeatInterval.weekly
            ? model.dayOfWeek
            : null,
  );

  // Determine the recurrence pattern
  DateTimeComponents matchDateTimeComponents =
      model.repeatInterval == RepeatInterval.daily
          ? DateTimeComponents.time
          : DateTimeComponents.dayOfWeekAndTime;

  // Schedule the notification with the recurrence pattern
  await _flutterLocalNotificationsPlugin.zonedSchedule(
    model.id,
    model.title,
    model.body,
    nextOccurrence,
    details,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.wallClockTime,
    matchDateTimeComponents: matchDateTimeComponents,
    payload: model.toPayload(),
  );
}

[T] This method:

[List]
1. Calculates the next occurrence of the notification based on the time of day and optional day of week.
2. Determines the recurrence pattern using DateTimeComponents:
   - For daily notifications, we use DateTimeComponents.time to match only the time.
   - For weekly notifications, we use DateTimeComponents.dayOfWeekAndTime to match both the day of week and time.
3. Schedules the notification with the zonedSchedule method, which handles time zone changes and daylight saving time correctly.

[H2] Time Zone Handling and Next Occurrence Calculation

[T] A critical aspect of recurring notifications is calculating the next occurrence correctly, taking into account the current time and the user's time zone:

[Code: dart]
/// Calculates the next occurrence time for a recurring notification.
///
/// Determines when a notification should next appear based on the specified
/// [timeOfDay] and optional [dayOfWeek].
tz.TZDateTime _calculateNextOccurrence({
  required TimeOfDay timeOfDay,
  int? dayOfWeek,
}) {
  final now = tz.TZDateTime.now(tz.local);

  // Set the time component
  tz.TZDateTime scheduledDate = tz.TZDateTime(
    tz.local,
    now.year,
    now.month,
    now.day,
    timeOfDay.hour,
    timeOfDay.minute,
  );

  // If the time today is already past, schedule for tomorrow
  if (scheduledDate.isBefore(now)) {
    scheduledDate = scheduledDate.add(const Duration(days: 1));
  }

  // If a specific day of week is requested (for weekly notifications)
  if (dayOfWeek != null) {
    // Keep adding days until we reach the desired day of week
    while (scheduledDate.weekday != dayOfWeek) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
  }

  return scheduledDate;
}

[T] This method handles several important scenarios:

[List]
1. It uses the tz package to create time zone-aware dates, ensuring that notifications appear at the correct local time regardless of time zone changes.
2. If the specified time for today has already passed, it schedules the notification for tomorrow instead.
3. For weekly notifications, it adjusts the date to the next occurrence of the specified day of week.

[T] By handling these scenarios correctly, we ensure that recurring notifications appear at the expected times, even when the device's time zone changes or when daylight saving time begins or ends.


Next Chapter Title
Building a Fluent API for Recurring Notifications

[T] In this chapter, we'll explore how the NotificationBuilder class provides a fluent API for creating recurring notifications. We'll also see how this API is used in practice with real-world examples.

[H2] The Builder Pattern for Notifications

[T] The NotificationBuilder class uses the builder pattern to provide a fluent, chainable API for creating notifications. This pattern makes the code more readable and less error-prone by guiding developers through the configuration process.

[T] The basic structure of the NotificationBuilder class is:

[Code: dart]
class NotificationBuilder {
  final NotificationManager _manager;
  NotificationModel _model;

  NotificationBuilder._(this._manager, this._model);

  /// Creates a basic notification builder with required information.
  factory NotificationBuilder.create(
    NotificationManager manager, {
    required int id,
    required String title,
    required String body,
    required String channelId,
    NotificationLevel level = NotificationLevel.normal,
  }) {
    // Validation and initialization code...
    return NotificationBuilder._(manager, model);
  }

  // Methods for configuring the notification...

  /// Shows the notification immediately or at the configured time.
  Future<void> show() async {
    // Implementation...
  }
}

[T] This structure allows us to create and configure notifications with a chainable API, where each method returns the builder instance for further configuration.

[H2] Methods for Recurring Notifications

[T] The NotificationBuilder class provides several methods specifically for creating recurring notifications:

[Code: dart]
/// Configures the notification to repeat at a specific interval.
///
/// Changes the notification type to periodic.
///
/// [repeatInterval] How frequently the notification should repeat
NotificationBuilder setRepeatInterval(RepeatInterval repeatInterval) {
  _model = _model.copyWith(
    type: NotificationType.periodic,
    repeatInterval: repeatInterval,
  );
  return this;
}

/// Sets a specific time of day for recurring notifications.
///
/// Used primarily with daily and weekly notifications to ensure they appear
/// at a consistent time.
///
/// [timeOfDay] The time when the notification should be shown
NotificationBuilder atTimeOfDay(TimeOfDay timeOfDay) {
  _model = _model.copyWith(timeOfDay: timeOfDay);
  return this;
}

/// Sets the day of week for weekly recurring notifications.
///
/// [dayOfWeek] Day of week (1-7, where 1 is Monday and 7 is Sunday)
///
/// Throws an [ArgumentError] if dayOfWeek is out of valid range.
NotificationBuilder onDayOfWeek(int dayOfWeek) {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw ArgumentError('Day of week must be between 1-7 (Monday to Sunday)');
  }
  _model = _model.copyWith(dayOfWeek: dayOfWeek);
  return this;
}

[T] These methods work together to provide a comprehensive API for creating different types of recurring notifications:

[List]
**setRepeatInterval()** configures a notification to repeat at a specific interval (daily, weekly, hourly, etc.)
**atTimeOfDay()** sets the time of day for daily and weekly notifications
**onDayOfWeek()** sets the day of the week for weekly notifications (using 1-7 where 1 is Monday)

[H2] Real-World Examples

[T] Let's look at some real-world examples of how to use the NotificationBuilder to create different types of recurring notifications:

[Code: dart]
// Example 1: Daily medication reminder at 9:00 AM
final medicationReminder = NotificationBuilder.create(
  notificationManager,
  id: 1,
  title: 'Medication Reminder',
  body: 'Time to take your morning medication',
  channelId: NotificationChannelIds.health,
)
.setRepeatInterval(RepeatInterval.daily)
.atTimeOfDay(const TimeOfDay(hour: 9, minute: 0));

await medicationReminder.show();

// Example 2: Weekly team meeting reminder on Monday at 10:00 AM
final meetingReminder = NotificationBuilder.create(
  notificationManager,
  id: 2,
  title: 'Team Meeting',
  body: 'Weekly team sync meeting in Conference Room A',
  channelId: NotificationChannelIds.work,
)
.setRepeatInterval(RepeatInterval.weekly)
.atTimeOfDay(const TimeOfDay(hour: 10, minute: 0))
.onDayOfWeek(1); // 1 = Monday

await meetingReminder.show();

// Example 3: Hourly hydration reminder
final hydrationReminder = NotificationBuilder.create(
  notificationManager,
  id: 3,
  title: 'Hydration Reminder',
  body: 'Time to drink some water!',
  channelId: NotificationChannelIds.health,
)
.setRepeatInterval(RepeatInterval.hourly);

await hydrationReminder.show();

// Example 4: Every minute reminder (for testing purposes)
final testReminder = NotificationBuilder.create(
  notificationManager,
  id: 4,
  title: 'Test Notification',
  body: 'This notification repeats every minute',
  channelId: NotificationChannelIds.default_,
)
.setRepeatInterval(RepeatInterval.everyMinute);

await testReminder.show();

[T] These examples demonstrate how the fluent API makes it easy to create different types of recurring notifications with just a few lines of code. The API is intuitive and guides developers through the configuration process, ensuring that all required properties are set correctly.

[H2] Behind the Scenes

[T] When you call show() on a NotificationBuilder configured for a periodic notification, it calls the showPeriodicNotification() method in the NotificationManager class, which we explored in the previous chapter.

[T] The builder pattern abstracts away the complexity of the underlying implementation, allowing developers to focus on what they want to achieve rather than how it's implemented. This makes the code more readable, maintainable, and less error-prone.

[T] For example, when you call setRepeatInterval(RepeatInterval.daily), the builder sets both the notification type to periodic and the repeat interval to daily. This ensures that the notification is configured correctly without requiring the developer to remember all the required properties.


Next Chapter Title
User Interface and Management of Recurring Notifications

[T] In this chapter, we'll explore how users interact with recurring notifications through the app's UI. We'll examine how to create, view, update, and cancel recurring notifications from a user's perspective.

[H2] Creating Recurring Notifications in the UI

[T] The app provides a user-friendly interface for creating recurring notifications. When a user navigates to the Create Notification screen, they can select "Periodic" as the notification type, which reveals additional options for configuring the recurrence pattern.

[T] The notification type section allows users to select between different notification types:

[List]
**Instant**: Notifications that appear immediately
**Scheduled**: Notifications that appear at a specific date and time
**Periodic**: Notifications that repeat at regular intervals

[T] When the user selects "Periodic" as the notification type, additional options become visible:

[List]
A dropdown for selecting the repetition interval (daily, weekly, hourly, every minute)
A time picker for selecting the time of day (for daily and weekly notifications)
A dropdown for selecting the day of the week (for weekly notifications)

[T] Behind the scenes, the CreateNotificationController handles the creation of periodic notifications based on user selections:

[Code: dart]
// Configure based on type
switch (value.notificationType) {
  case 'Instant':
    // No additional configuration needed for instant notifications
    break;

  case 'Scheduled':
    builder = builder.scheduleFor(value.scheduledDateTime!);
    break;

  case 'Periodic':
    // Map UI selection to RepeatInterval
    late RepeatInterval repeatInterval;

    switch (value.periodicSubtype) {
      case 'Daily':
        repeatInterval = RepeatInterval.daily;
        break;
      case 'Weekly':
        repeatInterval = RepeatInterval.weekly;
        break;
      case 'Hourly':
        repeatInterval = RepeatInterval.hourly;
        break;
      case 'Every Minute':
        repeatInterval = RepeatInterval.everyMinute;
        break;
    }

    builder = builder.setRepeatInterval(repeatInterval);

    // For daily/weekly notifications, set the time of day
    if (value.periodicSubtype == 'Daily' ||
        value.periodicSubtype == 'Weekly') {
      builder = builder.atTimeOfDay(value.recurringTime!);

      // For weekly notifications, set the day of week
      if (value.periodicSubtype == 'Weekly') {
        builder = builder.onDayOfWeek(value.dayOfWeek!);
      }
    }

    // For hourly or every minute notifications with a specific start time
    if ((value.periodicSubtype == 'Hourly' ||
            value.periodicSubtype == 'Every Minute') &&
        value.recurringTime != null) {
      // Just set the time of day without scheduling a separate notification
      builder = builder.atTimeOfDay(value.recurringTime!);
    }
    break;
}

[T] For weekly notifications, the UI allows users to select the day of the week using a dropdown:

[Code: dart]
StyledDropdown<int>(
  label: 'Day of Week',
  prefixIcon: Icons.view_week,
  value: controller.value.dayOfWeek,
  items: const [
    DropdownMenuItem(value: 1, child: Text('Monday')),
    DropdownMenuItem(value: 2, child: Text('Tuesday')),
    DropdownMenuItem(value: 3, child: Text('Wednesday')),
    DropdownMenuItem(value: 4, child: Text('Thursday')),
    DropdownMenuItem(value: 5, child: Text('Friday')),
    DropdownMenuItem(value: 6, child: Text('Saturday')),
    DropdownMenuItem(value: 7, child: Text('Sunday')),
  ],
  onChanged: (value) {
    if (value != null) {
      controller.updateDayOfWeek(value);
    }
  },
)

[T] This dropdown uses values 1-7 (where 1 is Monday and 7 is Sunday), which matches the expected format for the onDayOfWeek() method.

[H2] Viewing and Managing Notifications

[T] Once notifications are created, users need to be able to view, update, and cancel them. The app includes a NotificationDetailsPage that displays information about a notification, including its repetition settings:

[Code: dart]
Widget _buildPeriodicDetails(NotificationModel notification) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Repeat Interval: ${notification.repeatInterval.toString().split('.').last}',
        style: Theme.of(context).textTheme.titleMedium,
      ),
      const SizedBox(height: 8),
      if (notification.repeatInterval == RepeatInterval.daily ||
          notification.repeatInterval == RepeatInterval.weekly)
        Text(
          'Time: ${notification.timeOfDay!.hour}:${notification.timeOfDay!.minute.toString().padLeft(2, '0')}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
      if (notification.repeatInterval == RepeatInterval.weekly &&
          notification.dayOfWeek != null)
        Text(
          'Day of Week: ${_getDayOfWeekName(notification.dayOfWeek!)}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
    ],
  );
}

[T] This method displays the repeat interval, time of day (for daily and weekly notifications), and day of week (for weekly notifications) in a user-friendly format.

[H2] Cancelling Recurring Notifications

[T] Recurring notifications continue to repeat until they're explicitly cancelled. The NotificationManager class provides a cancelNotification() method that works for all types of notifications, including recurring ones:

[Code: dart]
/// Cancels a notification with the specified ID.
///
/// Removes the notification from the system and prevents it from being displayed.
/// If the notification is already displayed, it will be removed from the notification drawer.
Future<void> cancelNotification(int id) async {
  await _flutterLocalNotificationsPlugin.cancel(id);
  debugPrint('Cancelled notification: $id');
}

[T] The NotificationDetailsPage provides a "Cancel Notification" button that calls this method when pressed:

[Code: dart]
ElevatedButton(
  onPressed: () => _cancelNotification(notification),
  child: const Text('Cancel Notification'),
)

[T] This allows users to easily cancel recurring notifications when they're no longer needed.

[H2] Updating Recurring Notifications

[T] To update a recurring notification, the app provides an "Edit Notification" button that navigates to an edit screen:

[Code: dart]
ElevatedButton(
  onPressed: () {
    // Navigate to edit page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditNotificationPage(
          notification: notification,
        ),
      ),
    ).then((_) {
      // Refresh the notification details
      setState(() {
        _notificationFuture = _loadNotification();
      });
    });
  },
  child: const Text('Edit Notification'),
)

[T] Behind the scenes, updating a notification involves cancelling the existing notification and scheduling a new one with the same ID but different properties:

[Code: dart]
// Get a reference to the notification manager
final notificationManager = NotificationManager();

// Create a builder for the notification with ID 123
final builder = NotificationBuilder.create(
  notificationManager,
  id: 123,
  title: 'Updated Reminder',
  body: 'This notification has been updated',
  channelId: NotificationChannelIds.personal,
)
.setRepeatInterval(RepeatInterval.weekly)
.atTimeOfDay(const TimeOfDay(hour: 14, minute: 30))
.onDayOfWeek(5); // 5 = Friday

// Show the updated notification
await builder.show();

[T] This approach ensures that the updated notification retains the same ID, making it easy to track and manage.


Next Chapter Title
Best Practices for Recurring Notifications

[T] In this chapter, we'll explore best practices for implementing recurring notifications in your Flutter apps.

[H2] Key Best Practices

[T] When implementing recurring notifications, consider these best practices:

[List]
**Respect user preferences**: Always provide a way for users to manage or disable recurring notifications. Notifications that can't be controlled can lead to user frustration and app uninstallation.

**Be mindful of frequency**: Choose appropriate intervals for your notifications. Too frequent notifications can annoy users, while too infrequent ones might not achieve your engagement goals.

**Handle time zone changes**: Ensure your notifications adapt correctly when users change time zones or when daylight saving time begins or ends. Our implementation using the tz package handles this automatically.

**Provide clear information**: When displaying notification details, make sure users can easily understand when the notification will appear next and how often it will repeat.

**Test thoroughly**: Test your recurring notifications on both Android and iOS devices to ensure they behave as expected across platforms.

**Consider battery impact**: Frequent notifications, especially those that wake up the device, can impact battery life. Use the minimum frequency necessary for your use case.

**Implement deep linking**: Allow users to navigate directly to relevant content when they tap on a notification, as we've done with the NotificationDeepLinkUtil.

[H2] Common Pitfalls to Avoid

[T] Be aware of these common pitfalls when implementing recurring notifications:

[List]
**Ignoring platform differences**: Android and iOS handle notifications differently. Our implementation abstracts these differences, but it's important to understand them.

**Not handling edge cases**: Ensure your implementation handles edge cases like time zone changes, daylight saving time, and device restarts.

**Over-notifying users**: Too many notifications can lead to notification fatigue and users disabling notifications entirely.

**Insufficient testing**: Test your notifications thoroughly, including edge cases like time zone changes and daylight saving time transitions.

[T] By following these best practices and avoiding common pitfalls, you can create a notification system that enhances the user experience rather than detracting from it.


Last Chapter Title
Section Summary

[T] Let's summarize what we have covered in this section:

[List]
We learned about recurring and periodic notifications and their different types (calendar-based and fixed-interval)
We explored the RepeatInterval enum and how it defines the supported repetition patterns
We implemented the showPeriodicNotification() method to handle both calendar-based and fixed-interval notifications
We created helper methods to calculate the next occurrence of a notification, taking into account time zones and day of week
We examined the NotificationBuilder class and how it provides a fluent API for creating recurring notifications
We explored how the UI allows users to create, view, update, and cancel recurring notifications
We discussed best practices and common pitfalls to avoid when implementing recurring notifications

[T] You now have a solid understanding of how to implement recurring and periodic notifications in Flutter. Your app can now deliver notifications that repeat at regular intervals, with full control over the repetition pattern.

[T] The recurring notifications we've implemented provide a powerful way to keep users engaged with your app by delivering timely reminders and updates. Whether it's a daily medication reminder, a weekly meeting notification, or an hourly alert, you now have the tools to create notifications that meet your users' needs.

[T] Up Next, we'll dive into customizing notification appearance and behavior, where you'll learn how to create rich notifications with images, actions, and more.
