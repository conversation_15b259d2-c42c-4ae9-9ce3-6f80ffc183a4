<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- TODO: Add required permissions for notifications
         You'll need to add permissions for:
         1. Receiving boot completed events (for rescheduling notifications after device restart)
         2. Vibration
         3. Full screen intents (for high-priority notifications)
         4. Exact alarms (for precise scheduling)
         5. Posting notifications (required for Android 13+)
    -->

    <!-- Basic permission for internet access -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <application
        android:label="timora"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <!-- Disable default Flutter deep linking -->
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />

             <!-- Deep Link sample -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Add optional android:host to distinguish your app
                     from others in case of conflicting scheme name -->
                <!-- <data android:scheme="sample" android:host="open.my.app" /> -->
                <data android:scheme="timora" />
            </intent-filter>

            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- TODO: Add notification service and receivers
             You'll need to add:
             1. Foreground service for ongoing notifications
             2. Action broadcast receiver for notification actions
             3. Scheduled notification receiver for showing scheduled notifications
             4. Boot receiver for rescheduling notifications after device restart
        -->

        <!-- Example of a boot receiver with intent filter:
        <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
            </intent-filter>
        </receiver>
        -->
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
